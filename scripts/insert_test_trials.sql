-- 插入测试开庭数据，用于测试相邻开庭安排给一个律师的功能
-- 这些数据将创建一些在同一天或相邻时间的开庭，以便测试智能调度算法

-- 首先确保有足够的案件ID（使用随机的大数字作为案件ID）
-- 插入一些测试开庭数据，状态设为1（待排庭）

-- 2025-07-18 上午的相邻开庭（北京地区）
INSERT INTO `trials` (`case_id`, `court_id`, `status`, `mode`, `type`, `start_time`, `end_time`) VALUES
(10001, 1, 1, 2, 1, '2025-07-18 09:00:00', '2025-07-18 10:30:00'),  -- 最高人民法院
(10002, 5, 1, 2, 1, '2025-07-18 11:00:00', '2025-07-18 12:30:00'),  -- 北京市高级人民法院
(10003, 1, 1, 2, 2, '2025-07-18 14:00:00', '2025-07-18 15:30:00'),  -- 最高人民法院

-- 2025-07-18 下午的相邻开庭（上海地区）
(10004, 2, 1, 2, 1, '2025-07-18 09:30:00', '2025-07-18 11:00:00'),  -- 上海市高级人民法院
(10005, 2, 1, 2, 2, '2025-07-18 11:30:00', '2025-07-18 13:00:00'),  -- 上海市高级人民法院
(10006, 2, 1, 2, 1, '2025-07-18 14:30:00', '2025-07-18 16:00:00'),  -- 上海市高级人民法院

-- 2025-07-19 的相邻开庭（广东地区）
(10007, 14, 1, 2, 1, '2025-07-19 09:00:00', '2025-07-19 10:30:00'), -- 广东省高级人民法院
(10008, 14, 1, 2, 2, '2025-07-19 11:00:00', '2025-07-19 12:30:00'), -- 广东省高级人民法院
(10009, 14, 1, 2, 1, '2025-07-19 13:30:00', '2025-07-19 15:00:00'), -- 广东省高级人民法院
(10010, 14, 1, 2, 2, '2025-07-19 15:30:00', '2025-07-19 17:00:00'), -- 广东省高级人民法院

-- 2025-07-20 跨地区但时间相邻的开庭（测试算法是否会合理分配）
(10011, 1, 1, 2, 1, '2025-07-20 09:00:00', '2025-07-20 10:30:00'),  -- 北京 最高人民法院
(10012, 2, 1, 2, 1, '2025-07-20 11:00:00', '2025-07-20 12:30:00'),  -- 上海 上海市高级人民法院（距离远，不应该分配给同一律师）
(10013, 5, 1, 2, 2, '2025-07-20 14:00:00', '2025-07-20 15:30:00'),  -- 北京 北京市高级人民法院

-- 2025-07-21 同一法院的连续开庭（最理想的相邻开庭情况）
(10014, 1, 1, 2, 1, '2025-07-21 09:00:00', '2025-07-21 10:00:00'),  -- 最高人民法院
(10015, 1, 1, 2, 2, '2025-07-21 10:15:00', '2025-07-21 11:15:00'),  -- 最高人民法院（15分钟间隔）
(10016, 1, 1, 2, 1, '2025-07-21 11:30:00', '2025-07-21 12:30:00'),  -- 最高人民法院（15分钟间隔）
(10017, 1, 1, 2, 2, '2025-07-21 14:00:00', '2025-07-21 15:00:00'),  -- 最高人民法院（午休后）
(10018, 1, 1, 2, 1, '2025-07-21 15:15:00', '2025-07-21 16:15:00'),  -- 最高人民法院（15分钟间隔）

-- 2025-07-22 不同法院但地理位置相近的开庭（北京地区）
(10019, 1, 1, 2, 1, '2025-07-22 09:00:00', '2025-07-22 10:30:00'),  -- 最高人民法院（北京东城区）
(10020, 5, 1, 2, 2, '2025-07-22 11:00:00', '2025-07-22 12:30:00'),  -- 北京市高级人民法院（北京朝阳区）
(10021, 1, 1, 2, 1, '2025-07-22 14:00:00', '2025-07-22 15:30:00'),  -- 最高人民法院

-- 2025-07-23 时间冲突的开庭（测试算法冲突处理）
(10022, 2, 1, 2, 1, '2025-07-23 09:00:00', '2025-07-23 11:00:00'),  -- 上海市高级人民法院
(10023, 2, 1, 2, 2, '2025-07-23 10:30:00', '2025-07-23 12:00:00'),  -- 上海市高级人民法院（时间重叠）
(10024, 14, 1, 2, 1, '2025-07-23 10:00:00', '2025-07-23 11:30:00'), -- 广东省高级人民法院（同时间不同地点）

-- 2025-07-24 全天密集开庭（测试算法的负载均衡）
(10025, 1, 1, 2, 1, '2025-07-24 09:00:00', '2025-07-24 10:00:00'),  -- 最高人民法院
(10026, 1, 1, 2, 2, '2025-07-24 10:30:00', '2025-07-24 11:30:00'),  -- 最高人民法院
(10027, 1, 1, 2, 1, '2025-07-24 12:00:00', '2025-07-24 13:00:00'),  -- 最高人民法院
(10028, 1, 1, 2, 2, '2025-07-24 14:00:00', '2025-07-24 15:00:00'),  -- 最高人民法院
(10029, 1, 1, 2, 1, '2025-07-24 15:30:00', '2025-07-24 16:30:00'),  -- 最高人民法院
(10030, 1, 1, 2, 2, '2025-07-24 17:00:00', '2025-07-24 18:00:00'),  -- 最高人民法院

-- 2025-07-25 周末开庭（较少）
(10031, 2, 1, 2, 1, '2025-07-25 09:00:00', '2025-07-25 11:00:00'),  -- 上海市高级人民法院
(10032, 14, 1, 2, 2, '2025-07-25 14:00:00', '2025-07-25 16:00:00'), -- 广东省高级人民法院

-- 2025-07-26 周末开庭（较少）
(10033, 1, 1, 2, 1, '2025-07-26 10:00:00', '2025-07-26 12:00:00'),  -- 最高人民法院
(10034, 5, 1, 2, 2, '2025-07-26 14:00:00', '2025-07-26 16:00:00'),  -- 北京市高级人民法院

-- 2025-07-28 新一周的开庭
(10035, 1, 1, 2, 1, '2025-07-28 09:00:00', '2025-07-28 10:30:00'),  -- 最高人民法院
(10036, 1, 1, 2, 2, '2025-07-28 11:00:00', '2025-07-28 12:30:00'),  -- 最高人民法院
(10037, 2, 1, 2, 1, '2025-07-28 09:30:00', '2025-07-28 11:00:00'),  -- 上海市高级人民法院
(10038, 2, 1, 2, 2, '2025-07-28 11:30:00', '2025-07-28 13:00:00'),  -- 上海市高级人民法院

-- 2025-07-29 更多相邻开庭测试
(10039, 14, 1, 2, 1, '2025-07-29 09:00:00', '2025-07-29 10:15:00'), -- 广东省高级人民法院
(10040, 14, 1, 2, 2, '2025-07-29 10:30:00', '2025-07-29 11:45:00'), -- 广东省高级人民法院（15分钟间隔）
(10041, 14, 1, 2, 1, '2025-07-29 12:00:00', '2025-07-29 13:15:00'), -- 广东省高级人民法院（15分钟间隔）
(10042, 14, 1, 2, 2, '2025-07-29 14:00:00', '2025-07-29 15:15:00'), -- 广东省高级人民法院（45分钟间隔）
(10043, 14, 1, 2, 1, '2025-07-29 15:30:00', '2025-07-29 16:45:00'), -- 广东省高级人民法院（15分钟间隔）

-- 2025-07-30 跨省但相邻时间的开庭（测试地理距离权重）
(10044, 4, 1, 2, 1, '2025-07-30 09:00:00', '2025-07-30 10:30:00'),  -- 内蒙古自治区高级人民法院
(10045, 6, 1, 2, 2, '2025-07-30 11:00:00', '2025-07-30 12:30:00'),  -- 吉林省高级人民法院
(10046, 1, 1, 2, 1, '2025-07-30 14:00:00', '2025-07-30 15:30:00'),  -- 最高人民法院

-- 2025-07-31 月末密集开庭
(10047, 1, 1, 2, 1, '2025-07-31 09:00:00', '2025-07-31 10:00:00'),  -- 最高人民法院
(10048, 1, 1, 2, 2, '2025-07-31 10:15:00', '2025-07-31 11:15:00'),  -- 最高人民法院
(10049, 1, 1, 2, 1, '2025-07-31 11:30:00', '2025-07-31 12:30:00'),  -- 最高人民法院
(10050, 5, 1, 2, 2, '2025-07-31 14:00:00', '2025-07-31 15:00:00'),  -- 北京市高级人民法院
(10051, 5, 1, 2, 1, '2025-07-31 15:15:00', '2025-07-31 16:15:00'),  -- 北京市高级人民法院
(10052, 5, 1, 2, 2, '2025-07-31 16:30:00', '2025-07-31 17:30:00');  -- 北京市高级人民法院

-- 更新一些现有的trials记录，将它们的状态改为待排庭(1)，并设置合适的时间
UPDATE `trials` SET 
    `status` = 1,
    `start_time` = '2025-07-18 09:00:00',
    `end_time` = '2025-07-18 10:30:00'
WHERE `id` BETWEEN 101 AND 110 AND `status` != 1;

UPDATE `trials` SET 
    `status` = 1,
    `start_time` = '2025-07-19 09:00:00',
    `end_time` = '2025-07-19 10:30:00'
WHERE `id` BETWEEN 111 AND 120 AND `status` != 1;

UPDATE `trials` SET 
    `status` = 1,
    `start_time` = '2025-07-20 09:00:00',
    `end_time` = '2025-07-20 10:30:00'
WHERE `id` BETWEEN 121 AND 130 AND `status` != 1;
