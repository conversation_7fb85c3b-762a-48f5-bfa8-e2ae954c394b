#!/usr/bin/env python3
"""
通过API插入测试开庭数据的脚本
用于测试相邻开庭安排给一个律师的功能
"""

import requests
import json
from datetime import datetime, timedelta

# API基础URL
BASE_URL = "http://localhost:8080/api"

def create_trial(case_id, court_id, status, mode, type_val, start_time, end_time):
    """创建开庭记录"""
    trial_data = {
        "case_id": case_id,
        "court_id": court_id,
        "status": status,
        "mode": mode,
        "type": type_val,
        "start_time": start_time,
        "end_time": end_time
    }
    
    try:
        response = requests.post(f"{BASE_URL}/trials", json=trial_data)
        if response.status_code in [200, 201]:  # 200 OK 或 201 Created 都表示成功
            print(f"✓ 成功创建开庭记录: 案件{case_id}, 法院{court_id}, {start_time}")
            return response.json()
        else:
            print(f"✗ 创建开庭记录失败: {response.status_code}, {response.text}")
            return None
    except Exception as e:
        print(f"✗ 请求失败: {e}")
        return None

def main():
    """主函数"""
    print("开始插入测试开庭数据...")
    
    # 测试数据列表
    test_trials = [
        # 2025-07-18 上午的相邻开庭（北京地区）
        (10001, 1, 1, 2, 1, '2025-07-18T09:00:00Z', '2025-07-18T10:30:00Z'),  # 最高人民法院
        (10002, 5, 1, 2, 1, '2025-07-18T11:00:00Z', '2025-07-18T12:30:00Z'),  # 北京市高级人民法院
        (10003, 1, 1, 2, 2, '2025-07-18T14:00:00Z', '2025-07-18T15:30:00Z'),  # 最高人民法院

        # 2025-07-18 下午的相邻开庭（上海地区）
        (10004, 2, 1, 2, 1, '2025-07-18T09:30:00Z', '2025-07-18T11:00:00Z'),  # 上海市高级人民法院
        (10005, 2, 1, 2, 2, '2025-07-18T11:30:00Z', '2025-07-18T13:00:00Z'),  # 上海市高级人民法院
        (10006, 2, 1, 2, 1, '2025-07-18T14:30:00Z', '2025-07-18T16:00:00Z'),  # 上海市高级人民法院
        
        # 2025-07-19 的相邻开庭（广东地区）
        (10007, 14, 1, 2, 1, '2025-07-19T09:00:00Z', '2025-07-19T10:30:00Z'), # 广东省高级人民法院
        (10008, 14, 1, 2, 2, '2025-07-19T11:00:00Z', '2025-07-19T12:30:00Z'), # 广东省高级人民法院
        (10009, 14, 1, 2, 1, '2025-07-19T13:30:00Z', '2025-07-19T15:00:00Z'), # 广东省高级人民法院
        (10010, 14, 1, 2, 2, '2025-07-19T15:30:00Z', '2025-07-19T17:00:00Z'), # 广东省高级人民法院
        
        # 2025-07-21 同一法院的连续开庭（最理想的相邻开庭情况）
        (10014, 1, 1, 2, 1, '2025-07-21T09:00:00Z', '2025-07-21T10:00:00Z'),  # 最高人民法院
        (10015, 1, 1, 2, 2, '2025-07-21T10:15:00Z', '2025-07-21T11:15:00Z'),  # 最高人民法院（15分钟间隔）
        (10016, 1, 1, 2, 1, '2025-07-21T11:30:00Z', '2025-07-21T12:30:00Z'),  # 最高人民法院（15分钟间隔）
        (10017, 1, 1, 2, 2, '2025-07-21T14:00:00Z', '2025-07-21T15:00:00Z'),  # 最高人民法院（午休后）
        (10018, 1, 1, 2, 1, '2025-07-21T15:15:00Z', '2025-07-21T16:15:00Z'),  # 最高人民法院（15分钟间隔）

        # 2025-07-22 不同法院但地理位置相近的开庭（北京地区）
        (10019, 1, 1, 2, 1, '2025-07-22T09:00:00Z', '2025-07-22T10:30:00Z'),  # 最高人民法院（北京东城区）
        (10020, 5, 1, 2, 2, '2025-07-22T11:00:00Z', '2025-07-22T12:30:00Z'),  # 北京市高级人民法院（北京朝阳区）
        (10021, 1, 1, 2, 1, '2025-07-22T14:00:00Z', '2025-07-22T15:30:00Z'),  # 最高人民法院
        
        # 2025-07-24 全天密集开庭（测试算法的负载均衡）
        (10025, 1, 1, 2, 1, '2025-07-24T09:00:00Z', '2025-07-24T10:00:00Z'),  # 最高人民法院
        (10026, 1, 1, 2, 2, '2025-07-24T10:30:00Z', '2025-07-24T11:30:00Z'),  # 最高人民法院
        (10027, 1, 1, 2, 1, '2025-07-24T12:00:00Z', '2025-07-24T13:00:00Z'),  # 最高人民法院
        (10028, 1, 1, 2, 2, '2025-07-24T14:00:00Z', '2025-07-24T15:00:00Z'),  # 最高人民法院
        (10029, 1, 1, 2, 1, '2025-07-24T15:30:00Z', '2025-07-24T16:30:00Z'),  # 最高人民法院
        (10030, 1, 1, 2, 2, '2025-07-24T17:00:00Z', '2025-07-24T18:00:00Z'),  # 最高人民法院
        
        # 2025-07-29 更多相邻开庭测试
        (10039, 14, 1, 2, 1, '2025-07-29T09:00:00Z', '2025-07-29T10:15:00Z'), # 广东省高级人民法院
        (10040, 14, 1, 2, 2, '2025-07-29T10:30:00Z', '2025-07-29T11:45:00Z'), # 广东省高级人民法院（15分钟间隔）
        (10041, 14, 1, 2, 1, '2025-07-29T12:00:00Z', '2025-07-29T13:15:00Z'), # 广东省高级人民法院（15分钟间隔）
        (10042, 14, 1, 2, 2, '2025-07-29T14:00:00Z', '2025-07-29T15:15:00Z'), # 广东省高级人民法院（45分钟间隔）
        (10043, 14, 1, 2, 1, '2025-07-29T15:30:00Z', '2025-07-29T16:45:00Z'), # 广东省高级人民法院（15分钟间隔）

        # 2025-07-31 月末密集开庭
        (10047, 1, 1, 2, 1, '2025-07-31T09:00:00Z', '2025-07-31T10:00:00Z'),  # 最高人民法院
        (10048, 1, 1, 2, 2, '2025-07-31T10:15:00Z', '2025-07-31T11:15:00Z'),  # 最高人民法院
        (10049, 1, 1, 2, 1, '2025-07-31T11:30:00Z', '2025-07-31T12:30:00Z'),  # 最高人民法院
        (10050, 5, 1, 2, 2, '2025-07-31T14:00:00Z', '2025-07-31T15:00:00Z'),  # 北京市高级人民法院
        (10051, 5, 1, 2, 1, '2025-07-31T15:15:00Z', '2025-07-31T16:15:00Z'),  # 北京市高级人民法院
        (10052, 5, 1, 2, 2, '2025-07-31T16:30:00Z', '2025-07-31T17:30:00Z'),  # 北京市高级人民法院
    ]
    
    success_count = 0
    total_count = len(test_trials)
    
    for trial_data in test_trials:
        case_id, court_id, status, mode, type_val, start_time, end_time = trial_data
        result = create_trial(case_id, court_id, status, mode, type_val, start_time, end_time)
        if result:
            success_count += 1
    
    print(f"\n插入完成: {success_count}/{total_count} 条记录成功")
    
    if success_count > 0:
        print("\n测试数据说明:")
        print("1. 2025-07-18: 北京和上海地区的相邻开庭")
        print("2. 2025-07-19: 广东地区的连续开庭")
        print("3. 2025-07-21: 同一法院的理想连续开庭（15分钟间隔）")
        print("4. 2025-07-22: 北京地区不同法院但地理位置相近的开庭")
        print("5. 2025-07-24: 全天密集开庭测试负载均衡")
        print("6. 2025-07-29: 广东地区更多相邻开庭测试")
        print("7. 2025-07-31: 月末密集开庭")
        print("\n这些数据将帮助测试智能调度算法是否能够:")
        print("- 将相邻时间的开庭分配给同一律师")
        print("- 考虑地理距离因素")
        print("- 处理时间冲突")
        print("- 进行负载均衡")

if __name__ == "__main__":
    main()
