-- 创建表结构脚本
-- 使用 external-tool 数据库
USE `external-tool`;

-- 律师信息表
CREATE TABLE IF NOT EXISTS `lawyers` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL COMMENT '姓名',
    `phone` varchar(20) NOT NULL COMMENT '手机号',
    `id_card` varchar(18) NOT NULL COMMENT '身份证号',
    `employment` varchar(10) NOT NULL COMMENT '雇佣关系授薪/外包',
    `ability` varchar(10) NOT NULL COMMENT '职能类别开庭/办案',
    `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态0-禁用1-启用',
    `original_address` varchar(500) NOT NULL COMMENT '原始地址信息',
    `formatted_address` varchar(500) NOT NULL DEFAULT '' COMMENT '格式化后地址',
    `province` varchar(50) NOT NULL DEFAULT '' COMMENT '省份',
    `city` varchar(50) NOT NULL DEFAULT '' COMMENT '城市',
    `district` varchar(50) NOT NULL DEFAULT '' COMMENT '区县',
    `street` varchar(100) NOT NULL DEFAULT '' COMMENT '街道',
    `street_number` varchar(50) NOT NULL DEFAULT '' COMMENT '街道号码',
    `lng` decimal(10,7) NOT NULL DEFAULT '0' COMMENT '经度',
    `lat` decimal(10,7) NOT NULL DEFAULT '0' COMMENT '纬度',
    `reliability` tinyint NOT NULL DEFAULT '0' COMMENT '0-10 可信度 >= 7 可信任',
    `created_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_id_card` (`id_card`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='律师信息表';

-- 法院信息表
CREATE TABLE IF NOT EXISTS `courts` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL COMMENT '名称',
    `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态: 0-禁用 1-启用',
    `original_address` varchar(500) NOT NULL COMMENT '原始地址信息',
    `formatted_address` varchar(500) NOT NULL DEFAULT '' COMMENT '格式化后地址',
    `province` varchar(50) NOT NULL DEFAULT '' COMMENT '省份',
    `city` varchar(50) NOT NULL DEFAULT '' COMMENT '城市',
    `district` varchar(50) NOT NULL DEFAULT '' COMMENT '区县',
    `street` varchar(100) NOT NULL DEFAULT '' COMMENT '街道',
    `street_number` varchar(50) NOT NULL DEFAULT '' COMMENT '街道号码',
    `lng` decimal(10,7) NOT NULL DEFAULT '0' COMMENT '经度',
    `lat` decimal(10,7) NOT NULL DEFAULT '0' COMMENT '纬度',
    `reliability` tinyint NOT NULL DEFAULT '0' COMMENT '0-10 可信度 >= 7 可信任',
    `created_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='法院信息表';

-- 开庭信息表
CREATE TABLE IF NOT EXISTS `trials` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `case_id` bigint unsigned NOT NULL COMMENT '案件ID',
    `court_id` bigint unsigned NOT NULL COMMENT '法院ID',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态0-待登记1-待排庭2-待滴滴3-待支付4-待开庭5-开庭缺席6-已开庭7-已取消',
    `mode` tinyint NOT NULL DEFAULT '0' COMMENT '开庭方式0-待定1-线上2-线下',
    `type` tinyint NOT NULL COMMENT '庭审类型1-民初庭2-调解庭',
    `start_time` datetime(3) NULL DEFAULT NULL COMMENT '开始时间',
    `end_time` datetime(3) NULL DEFAULT NULL COMMENT '结束时间',
    `created_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='开庭信息表';

-- 路线计算表
CREATE TABLE IF NOT EXISTS `routes` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `type` varchar(50) NOT NULL COMMENT '类型 lawyer_to_court, court_to_court, court_to_lawyer',
    `from_start_time` datetime(3) NULL DEFAULT NULL COMMENT '出发地开始时间',
    `from_end_time` datetime(3) NULL DEFAULT NULL COMMENT '出发地结束时间',
    `to_start_time` datetime(3) NULL DEFAULT NULL COMMENT '目标地开始时间',
    `to_end_time` datetime(3) NULL DEFAULT NULL COMMENT '目标地结束时间',
    `from_lat` decimal(10,7) NULL DEFAULT NULL COMMENT '出发地纬度',
    `from_lng` decimal(10,7) NULL DEFAULT NULL COMMENT '出发地经度',
    `from_formatted_address` varchar(500) NOT NULL DEFAULT '' COMMENT '出发地格式化地址',
    `to_lat` decimal(10,7) NULL DEFAULT NULL COMMENT '目标地纬度',
    `to_lng` decimal(10,7) NULL DEFAULT NULL COMMENT '目标地经度',
    `to_formatted_address` varchar(500) NOT NULL DEFAULT '' COMMENT '目标地格式化地址',
    `distance` int NOT NULL DEFAULT 0 COMMENT '距离单位: 米',
    `duration` int NOT NULL DEFAULT 0 COMMENT '时间单位: 分钟',
    `cost` decimal(10,2) NULL DEFAULT 0 COMMENT '差旅成本',
    `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态: 0-禁用 1-可用 2-人工修正数据',
    `remark` varchar(500) NULL DEFAULT '' COMMENT '备注',
    `detail` text NULL COMMENT '路线规划结果',
    `created_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_type_from_to_time` (`type`, `from_lat`, `from_lng`, `to_lat`, `to_lng`, `from_start_time`, `from_end_time`, `to_start_time`, `to_end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='路线计算表';
