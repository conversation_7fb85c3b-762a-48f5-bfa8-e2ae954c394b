-- 创建数据库初始化脚本
-- 创建 external-tool 数据库
CREATE DATABASE IF NOT EXISTS `external-tool` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建读写账号
CREATE USER IF NOT EXISTS 'external_tool_user'@'%' IDENTIFIED BY 'external_tool_pass@123';
CREATE USER IF NOT EXISTS 'external_tool_user'@'localhost' IDENTIFIED BY 'external_tool_pass@123';

-- 授予权限
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, ALTER, INDEX ON `external-tool`.* TO 'external_tool_user'@'%';
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, ALTER, INDEX ON `external-tool`.* TO 'external_tool_user'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;

-- 切换到新创建的数据库
USE `external-tool`; 