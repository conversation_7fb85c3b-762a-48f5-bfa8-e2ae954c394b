# DFS 性能分析使用说明

## 快速开始

### 1. 运行性能测试
```bash
cd internal/service/schedule
go test -run TestRealDFS -v
```

### 2. 查看生成的文件
测试完成后会生成两个文件：
- `cpu_profile_YYYYMMDD_HHMMSS.prof` - CPU 性能数据
- `mem_profile_YYYYMMDD_HHMMSS.prof` - 内存使用数据

### 3. 通过 Web 界面分析

#### 分析 CPU 性能：
```bash
go tool pprof -http=:3000 cpu_profile_YYYYMMDD_HHMMSS.prof
```
然后在浏览器打开 `http://localhost:3000`

#### 分析内存使用：
```bash
go tool pprof -http=:3001 mem_profile_YYYYMMDD_HHMMSS.prof
```
然后在浏览器打开 `http://localhost:3001`

## Web 界面功能

### 主要视图：
1. **Top** - 显示耗时最多的函数
2. **Graph** - 调用关系图
3. **Flame Graph** - 火焰图（最直观）
4. **Source** - 源代码视图

### 重点关注：
- `dfsHelperByTrials` - 递归搜索函数
- `isTimeCompatibleMulti` - 时间兼容性检查
- `copyAssignmentMulti` - 状态复制（可能的内存瓶颈）
- `getTravelTime` - 路径计算

## 常用命令行分析

### 查看 CPU 热点：
```bash
go tool pprof -top cpu_profile_YYYYMMDD_HHMMSS.prof
```

### 查看内存分配：
```bash
go tool pprof -top mem_profile_YYYYMMDD_HHMMSS.prof
```

### 交互式分析：
```bash
go tool pprof cpu_profile_YYYYMMDD_HHMMSS.prof
```
然后使用命令：
- `top` - 查看热点函数
- `list funcName` - 查看函数详情
- `web` - 生成调用图
- `svg` - 生成 SVG 图

## 性能优化建议

根据分析结果，可能的优化方向：

1. **减少递归深度** - 考虑使用迭代替代递归
2. **缓存计算结果** - 避免重复计算路径时间
3. **优化数据结构** - 减少内存分配和复制
4. **提前剪枝** - 在搜索过程中提前排除不可能的方案

## 常见问题

**Q: 为什么每次生成的文件名都不同？**
A: 使用时间戳避免文件覆盖，方便对比不同时间的性能。

**Q: Web 界面无法打开？**
A: 确保端口没有被占用，可以换其他端口如 `:3002`。

**Q: 火焰图看不清？**
A: 可以点击图表进行缩放，或使用 Source 视图查看源代码。 