# Web 界面使用说明

## 启动方式

使用以下命令启动Web界面：

```bash
go run main.go sched web
```

服务器将在 `http://localhost:8080` 启动。

## 功能说明

### 1. 主要功能
- 数据管理：律师、法院、开庭、路线四个模块的数据管理
- 搜索功能：支持模糊搜索
- 分页功能：可调整每页显示条数
- 批量操作：支持批量删除（当前页面/所有数据）
- 增删改查：完整的数据操作功能

### 2. 界面布局
- 顶部：导航栏显示系统名称
- 搜索栏：输入关键词进行搜索
- 操作按钮：添加、批量编辑、批量删除
- Tab标签：律师、法院、开庭、路线四个模块
- 数据表格：显示相应模块的数据
- 底部分页：分页控制和数据统计

### 3. 数据管理

#### 律师模块
- 字段：ID、姓名、电话、身份证、雇佣关系、职能、状态、地址
- 搜索：支持按姓名、电话、雇佣关系搜索

#### 法院模块  
- 字段：ID、名称、状态、省份、城市、区县、地址
- 搜索：支持按名称、城市、区县搜索

#### 开庭模块
- 字段：ID、案件ID、法院ID、状态、开庭方式、庭审类型、开始时间、结束时间
- 搜索：支持按案件ID、法院ID搜索

#### 路线模块
- 字段：ID、类型、出发地、目的地、距离、时间、成本、状态
- 搜索：支持按类型、出发地、目的地搜索

### 4. 操作说明

#### 添加数据
1. 点击"添加"按钮
2. 在弹出的表单中填写数据
3. 点击"保存"按钮

#### 编辑数据
1. 点击表格中的"编辑"按钮
2. 在弹出的表单中修改数据
3. 点击"保存"按钮

#### 删除数据
1. 单个删除：点击表格中的"删除"按钮
2. 批量删除：
   - 选中要删除的数据（勾选复选框）
   - 点击"批量删除"按钮
   - 选择删除范围（当前页面/所有数据）
   - 点击"确认"按钮

#### 搜索功能
1. 在搜索框中输入关键词
2. 点击搜索按钮或按Enter键
3. 清空搜索框可恢复显示所有数据

#### 分页功能
1. 使用下拉框调整每页显示条数
2. 点击页码进行翻页
3. 使用"上一页"/"下一页"按钮翻页

### 5. 技术特性
- 响应式设计：支持各种屏幕尺寸
- Bootstrap 5：现代化的UI组件
- 前后端分离：RESTful API接口
- 实时数据：无需刷新页面即可看到数据变化

### 6. API接口

#### 律师接口
- `GET /api/lawyers` - 获取律师列表
- `GET /api/lawyers/:id` - 获取单个律师
- `POST /api/lawyers` - 创建律师
- `PUT /api/lawyers/:id` - 更新律师
- `DELETE /api/lawyers/:id` - 删除律师
- `POST /api/lawyers/batch` - 批量操作律师

#### 法院接口
- `GET /api/courts` - 获取法院列表
- `GET /api/courts/:id` - 获取单个法院
- `POST /api/courts` - 创建法院
- `PUT /api/courts/:id` - 更新法院
- `DELETE /api/courts/:id` - 删除法院
- `POST /api/courts/batch` - 批量操作法院

#### 开庭接口
- `GET /api/trials` - 获取开庭列表
- `GET /api/trials/:id` - 获取单个开庭
- `POST /api/trials` - 创建开庭
- `PUT /api/trials/:id` - 更新开庭
- `DELETE /api/trials/:id` - 删除开庭
- `POST /api/trials/batch` - 批量操作开庭

#### 路线接口
- `GET /api/routes` - 获取路线列表
- `GET /api/routes/:id` - 获取单个路线
- `POST /api/routes` - 创建路线
- `PUT /api/routes/:id` - 更新路线
- `DELETE /api/routes/:id` - 删除路线
- `POST /api/routes/batch` - 批量操作路线

### 7. 查询参数
- `page`: 页码（默认：1）
- `page_size`: 每页条数（默认：10）
- `search`: 搜索关键词

### 8. 注意事项
- 确保数据库连接正常
- 批量操作请谨慎使用
- 建议在操作重要数据前先备份
- 系统默认端口为8080，请确保端口未被占用

## 开发说明

### 目录结构
```
├── cmd/sched.go                # 命令定义
├── internal/service/web.go     # Web服务实现
├── templates/index.html        # 前端页面模板
└── README_WEB.md              # 使用说明文档
```

### 扩展功能
如需添加新的功能模块，请参考现有的实现方式：
1. 在 `internal/service/web.go` 中添加相应的API接口
2. 在 `templates/index.html` 中添加相应的前端逻辑
3. 根据需要添加新的数据模型

### 技术栈
- 后端：Go + Gin + GORM
- 前端：HTML + JavaScript + Bootstrap 5
- 数据库：MySQL
- 图标：Font Awesome 