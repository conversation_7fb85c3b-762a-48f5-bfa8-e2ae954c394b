# 智能调度功能实现说明

## 功能概述

根据文档要求，已成功实现了智能调度功能，包括以下主要特性：

### 1. 权重配置系统
- **距离权重**: 控制距离对调度结果的影响
- **费用权重**: 控制费用对调度结果的影响（新增）
- **时间权重**: 控制时间对调度结果的影响
- **工作负载权重**: 控制工作负载均衡的影响

### 2. 界面特性
- 默认打开智能调度tab页面
- 第一行：权重配置控制面板，包含4个权重输入框和重新计算按钮
- 第二行：方案选择下拉框（显示top10方案）和地图查看按钮
- 第三部分：时间表格，支持水平滚动，锁定第一列

### 3. 时间表格功能
- 显示律师名和匹配到的开庭数量
- 按日期显示开庭安排
- 三层显示：去程/开庭/返程
- 鼠标悬停显示详细信息
- 支持点击查看律师和法院详情

### 4. 地图显示功能
- 使用腾讯地图API（key: FODBZ-4SI6W-NFURJ-3RX7Q-OGUWO-KPBKW）
- 显示律师位置（绿色标记）和法院位置（橙色标记）
- 显示出差线路连线
- 鼠标悬停显示详细信息
- 新开窗口显示地图

## 技术实现

### 后端实现
1. **权重配置结构更新**:
   ```go
   type GeneticConfig struct {
       Name           string  `json:"name"`
       DistWeight     float64 `json:"dist_weight"`
       CostWeight     float64 `json:"cost_weight"`
       TimeWeight     float64 `json:"time_weight"`
       WorkloadWeight float64 `json:"workload_weight"`
   }
   ```

2. **遗传算法调度器更新**:
   - 添加了费用权重字段
   - 更新费用计算逻辑，包含距离、费用、时间三个维度
   - 支持自定义权重配置

3. **API接口**:
   - `GET /api/genetic/configs`: 获取权重配置选项
   - `POST /api/genetic/schedule`: 运行遗传算法调度
   - `GET /genetic/map`: 地图显示页面

### 前端实现
1. **权重配置界面**:
   - 4个权重输入框，支持实时修改
   - 预设配置下拉框，包含10种不同的权重组合
   - 重新计算按钮，使用当前权重进行计算

2. **方案选择**:
   - 显示top10方案的详细信息
   - 包含匹配数量、总费用、总距离、总耗时
   - 格式：`方案 1: 匹配 33 开庭(¥33.00 | 7.89KM | 789MIN)`

3. **时间表格**:
   - 固定第一列（律师信息）
   - 水平滚动显示日期列
   - 三层显示开庭安排
   - 支持点击查看详情

4. **地图显示**:
   - 集成腾讯地图API
   - 自定义标记点和连线样式
   - 支持交互式信息显示

## 使用说明

### 启动服务
```bash
go run main.go sched web
```

### 访问地址
- 主页面: http://localhost:8080
- 默认打开智能调度tab

### 操作流程
1. 选择或修改权重配置
2. 点击"重新计算"按钮
3. 从方案列表中选择最佳方案
4. 查看时间表格了解详细安排
5. 点击"地图显示"查看地理分布

## 配置说明

### 预设权重配置
1. **距离优先**: 优先考虑距离因素
2. **时间优先**: 优先考虑时间因素
3. **负载均衡**: 优先考虑工作负载均衡
4. **均衡优化**: 平衡各项因素
5. **远距离惩罚**: 对远距离进行惩罚
6. **快速到达**: 优先考虑快速到达
7. **工作量平衡**: 平衡工作量分配
8. **距离敏感**: 对距离变化敏感
9. **时间敏感**: 对时间变化敏感
10. **综合平衡**: 综合考虑各项因素

### 自定义配置
用户可以手动修改4个权重值：
- 距离权重：控制距离影响（建议范围：0.1-20）
- 费用权重：控制费用影响（建议范围：0.1-20）
- 时间权重：控制时间影响（建议范围：0.1-20）
- 工作负载权重：控制负载均衡影响（建议范围：0.1-20）

## 注意事项

1. **数据要求**: 需要律师、法院、开庭、路线数据都存在且状态为有效
2. **坐标信息**: 律师和法院需要有有效的经纬度坐标
3. **时间冲突**: 系统会自动处理时间冲突，确保律师不会在同一时间有多个开庭
4. **地图显示**: 需要网络连接以加载腾讯地图API

## 技术栈

- **后端**: Go + Gin + GORM
- **前端**: HTML + CSS + JavaScript + Bootstrap
- **地图**: 腾讯地图API
- **算法**: 遗传算法调度
- **数据库**: SQLite/MySQL

## 更新日志

- 2025-07-16: 实现智能调度功能，支持4个权重配置
- 2025-07-16: 集成腾讯地图API，实现地图显示功能
- 2025-07-16: 实现时间表格，支持三层显示开庭安排
- 2025-07-16: 优化界面交互，默认打开智能调度tab 