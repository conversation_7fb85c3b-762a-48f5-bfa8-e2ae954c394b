-- 迁移脚本：将 routes 表的 cost 字段改为 price 字段
-- 执行时间：2025-07-18
-- 说明：将成本字段改为票价字段，单位从元改为分，-1表示无票价信息

-- 1. 添加新的 price 字段（单位：分）
ALTER TABLE routes ADD COLUMN price INT NOT NULL DEFAULT -1 COMMENT '票价单位: 分,-1表示无票价信息';

-- 2. 将原有 cost 数据转换为 price（元转分，乘以100）
UPDATE routes SET price = CASE 
    WHEN cost IS NULL OR cost = 0 THEN -1 
    ELSE ROUND(cost * 100) 
END;

-- 3. 删除原有的 cost 字段
ALTER TABLE routes DROP COLUMN cost;

-- 4. 验证数据迁移结果
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN price = -1 THEN 1 END) as no_price_records,
    COUNT(CASE WHEN price > 0 THEN 1 END) as has_price_records,
    MIN(price) as min_price,
    MAX(price) as max_price,
    AVG(CASE WHEN price > 0 THEN price END) as avg_price
FROM routes;
