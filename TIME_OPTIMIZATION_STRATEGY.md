# 法诉系统时间优化策略文档

## 概述

本文档记录了法诉系统中律师出行时间优化策略的重要改进，主要针对律师到法院和法院到律师的路径计算逻辑进行了智能化升级。

## 背景与需求

### 原有问题
- **律师到法院**：固定提前时间出发，无法适应不同交通情况
- **法院到律师**：开庭结束后立即出发，未考虑更优的返程选择
- **缺乏灵活性**：无法利用过夜车、早班车等特殊交通选择

### 改进目标
- 实现多批次提前出发策略，找到最佳出发时间
- 支持延后返程，允许隔夜等候获得更好的交通选择
- 建立综合评分机制，平衡时间成本和经济成本

## 新的时间优化策略

### 1. 律师到法院（lawyer_to_court）

#### 多批次提前策略
系统会测试以下7个不同的提前出发时间：

```
1. 提前30分钟出发
2. 提前1小时出发  
3. 提前2小时出发
4. 提前4小时出发
5. 提前8小时出发（过夜车）
6. 提前12小时出发
7. 提前24小时出发（前一天同一时间）
```

#### 评分机制
```
score = 总时间 + 费用×0.1 + 等候时间×0.5
```

**权重说明：**
- **总时间权重**: 1.0（最重要）
- **费用权重**: 0.1（适中）
- **等候时间权重**: 0.5（较重要，出发阶段需要合理安排）

#### 示例分析
```
方案1: 提前2小时出发 - 高铁直达
  • 出发时间: 12:30
  • 到达时间: 14:15
  • 等候时间: 15分钟
  • 总时间: 225分钟
  • 费用: 550元
  • 综合评分: 225 + 550×0.1 + 15×0.5 = 295.5

方案2: 提前8小时出发 - 过夜车
  • 出发时间: 前一天 22:30
  • 到达时间: 当天 8:30
  • 等候时间: 360分钟
  • 总时间: 960分钟
  • 费用: 280元
  • 综合评分: 960 + 280×0.1 + 360×0.5 = 1168.0

✅ 系统选择方案1：高铁直达（评分最低）
```

### 2. 法院到律师（court_to_lawyer）

#### 多批次延后策略
系统会测试以下8个不同的延后出发时间：

```
1. 立即出发（0分钟延后）
2. 30分钟后出发
3. 1小时后出发
4. 2小时后出发
5. 4小时后出发
6. 8小时后出发（当天晚上）
7. 12小时后出发
8. 24小时后出发（第二天同一时间）
```

#### 评分机制
```
score = 总时间 + 费用×0.1 + 等候时间×0.2
```

**权重说明：**
- **总时间权重**: 1.0（最重要）
- **费用权重**: 0.1（适中）
- **等候时间权重**: 0.2（较轻，返程阶段允许适当等候）

#### 示例分析
```
方案1: 立即出发 - 高铁
  • 出发时间: 16:30
  • 到达时间: 21:15
  • 等候时间: 0分钟
  • 总时间: 285分钟
  • 费用: 550元
  • 综合评分: 285 + 550×0.1 + 0×0.2 = 340.0

方案2: 延后8小时出发 - 过夜车
  • 出发时间: 当天 24:30
  • 到达时间: 次日 8:30
  • 等候时间: 480分钟
  • 总时间: 960分钟
  • 费用: 280元
  • 综合评分: 960 + 280×0.1 + 480×0.2 = 1084.0

✅ 系统选择方案1：高铁立即返程（评分最低）
```

## 技术实现

### 核心方法

#### 1. findBestLawyerToCourtRoute
```go
func (m *MapService) findBestLawyerToCourtRoute(from, to *model.Location, trialStartTime *time.Time, departureTime time.Time) (*model.Route, string, int)
```
- 测试多个提前出发时间点
- 确保能够准时到达法院
- 使用综合评分选择最佳方案

#### 2. findBestCourtToLawyerRoute
```go
func (m *MapService) findBestCourtToLawyerRoute(from, to *model.Location, trialEndTime *time.Time, departureTime time.Time) (*model.Route, string, int)
```
- 测试多个延后出发时间点
- 支持隔夜等候和第二天返程
- 平衡等候时间和交通成本

#### 3. processRouteV2
```go
func (m *MapService) processRouteV2(routeType string, from, to *model.Location, fromStartTime, fromEndTime, toStartTime, toEndTime *time.Time, departureTime time.Time)
```
- 根据路线类型调用相应的优化方法
- 统一处理路线数据的保存

### 日志记录

系统会详细记录每次方案更新的信息：
```
律师到法院最佳方案更新: 出发时间 2025-07-16 12:30, 到达时间 2025-07-16 14:15, 等候 15分钟, 总时间 225分钟, 费用 550元

法院到律师最佳方案更新: 开庭结束 2025-07-16 16:30, 出发时间 2025-07-16 16:30, 等候 0分钟, 行程 285分钟, 总时间 285分钟, 费用 550元
```

## 系统优势

### 1. 智能化程度提升
- ✅ 自动测试多个时间策略
- ✅ 综合评分机制确保最优选择
- ✅ 适应不同开庭时间安排

### 2. 交通选择优化
- ✅ 支持过夜车、早班车等特殊交通
- ✅ 平衡时间成本和经济成本
- ✅ 考虑现实中的复杂交通情况

### 3. 灵活性增强
- ✅ 律师出发：重视准时到达
- ✅ 律师返程：允许适当等候
- ✅ 支持隔夜返程和第二天出发

### 4. 数据驱动决策
- ✅ 基于实际交通数据计算
- ✅ 量化评分确保客观选择
- ✅ 详细日志便于追踪和调试

## 使用方法

### 1. 初始化优化路径数据
```bash
go run main.go sched init-route-v2
```

### 2. 启动Web服务
```bash
go run main.go sched web
```

### 3. 查看路径数据
访问 `http://localhost:8080` 查看系统界面和路径数据

## 数据库结构

### routes表结构
```sql
CREATE TABLE routes (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    type varchar(50) NOT NULL COMMENT '类型 lawyer_to_court, court_to_court, court_to_lawyer',
    from_start_time datetime(3) NULL DEFAULT NULL COMMENT '出发地开始时间',
    from_end_time datetime(3) NULL DEFAULT NULL COMMENT '出发地结束时间',
    to_start_time datetime(3) NULL DEFAULT NULL COMMENT '目标地开始时间',
    to_end_time datetime(3) NULL DEFAULT NULL COMMENT '目标地结束时间',
    -- 其他字段...
    duration int NOT NULL DEFAULT 0 COMMENT '优化后的总时间（包括等候时间）',
    cost decimal(10,2) NULL DEFAULT 0 COMMENT '差旅成本',
    remark varchar(500) NULL DEFAULT '' COMMENT '优化策略说明',
    -- 其他字段...
);
```

## 性能特点

### 1. 并发处理
- 使用90个worker并行处理路径计算
- 支持大规模数据的高效处理

### 2. 进度监控
- 每处理1000条路径输出进度日志
- 实时监控计算状态

### 3. 内存优化
- 使用存在性检查避免重复计算
- 合理管理内存使用

## 测试验证

### 演示程序
创建了详细的演示程序展示优化效果：
- 对比新旧策略的差异
- 展示评分计算过程
- 验证时间选择逻辑

### 测试结果
- ✅ 律师到法院：7个时间策略测试通过
- ✅ 法院到律师：8个时间策略测试通过
- ✅ 综合评分机制：权重设置合理
- ✅ 系统集成：与现有功能完美配合

## 后续优化建议

### 1. 动态权重调整
- 根据历史数据动态调整权重
- 考虑不同律师的个人偏好

### 2. 机器学习优化
- 使用历史出行数据训练模型
- 预测最优出发时间

### 3. 实时交通数据
- 集成实时交通信息
- 动态调整路径规划

### 4. 用户个性化
- 支持律师个人偏好设置
- 提供多种优化策略选择

## 总结

本次时间优化策略的实施显著提升了法诉系统的智能化水平，通过多批次时间策略和综合评分机制，系统能够自动选择最佳的出行方案，有效平衡了时间成本和经济成本，为律师提供了更加合理和经济的出行安排。

---

**文档版本**: 1.0  
**更新时间**: 2025-07-16  
**作者**: 法诉系统开发团队 