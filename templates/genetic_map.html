<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .map-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .map-content {
            position: relative;
            width: 90%;
            height: 90%;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .map-header {
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }
        
        .close-btn {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .close-btn:hover {
            transform: scale(1.1);
        }
        
        .map-info {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .info-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .info-value {
            font-size: 24px;
            font-weight: bold;
            color: #2196f3;
        }
        
        .info-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        
        .china-map {
            position: relative;
            width: 100%;
            height: calc(100% - 140px);
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 800"><rect width="1000" height="800" fill="%23f0f8ff"/><path d="M200 300 L800 300 L800 600 L200 600 Z" fill="%23e8f4fd" stroke="%23b3d9f2" stroke-width="2"/><text x="500" y="450" text-anchor="middle" font-family="Arial" font-size="48" fill="%23999">中国地图</text></svg>') center/contain no-repeat;
            overflow: hidden;
        }
        
        .route-line {
            position: absolute;
            height: 3px;
            background: linear-gradient(90deg, #ff4081, #2196f3);
            border-radius: 2px;
            transform-origin: left center;
            animation: drawLine 2s ease-in-out;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .route-line:hover {
            height: 5px;
            z-index: 10;
            box-shadow: 0 0 10px rgba(33, 150, 243, 0.5);
        }
        
        @keyframes drawLine {
            from {
                width: 0;
            }
            to {
                width: 100%;
            }
        }
        
        .location-marker {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .location-marker.lawyer {
            background: #4caf50;
        }
        
        .location-marker.court {
            background: #ff9800;
        }
        
        .location-marker:hover {
            transform: scale(1.5);
            z-index: 20;
        }
        
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s;
            pointer-events: none;
        }
        
        .route-line:hover .tooltip,
        .location-marker:hover .tooltip {
            opacity: 1;
            visibility: visible;
        }
        
        .legend {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            border: 2px solid white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }
        
        .legend-line {
            width: 30px;
            height: 3px;
            background: linear-gradient(90deg, #ff4081, #2196f3);
            border-radius: 2px;
            margin-right: 10px;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        
        .error {
            text-align: center;
            padding: 50px;
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="map-container">
        <div class="map-content">
            <div class="map-header">
                <button class="close-btn" onclick="window.close()">
                    <i class="fas fa-times"></i>
                </button>
                <h2><i class="fas fa-map"></i> 遗传算法调度地图</h2>
                <p id="configName">加载中...</p>
            </div>
            
            <div class="map-info">
                <div class="info-grid">
                    <div class="info-card">
                        <div class="info-value" id="totalMatches">-</div>
                        <div class="info-label">匹配数量</div>
                    </div>
                    <div class="info-card">
                        <div class="info-value" id="totalDistance">-</div>
                        <div class="info-label">总行程 (km)</div>
                    </div>
                    <div class="info-card">
                        <div class="info-value" id="totalDuration">-</div>
                        <div class="info-label">总耗时 (分钟)</div>
                    </div>
                    <div class="info-card">
                        <div class="info-value" id="totalCost">-</div>
                        <div class="info-label">总费用</div>
                    </div>
                </div>
            </div>
            
            <div class="china-map" id="chinaMap">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i> 正在加载地图数据...
                </div>
            </div>
            
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color lawyer" style="background: #4caf50;"></div>
                    <span>律师位置</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color court" style="background: #ff9800;"></div>
                    <span>法院位置</span>
                </div>
                <div class="legend-item">
                    <div class="legend-line"></div>
                    <span>出差线路</span>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 地图数据
        let mapData = null;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadMapData();
        });
        
        // 加载地图数据
        function loadMapData() {
            try {
                const urlParams = new URLSearchParams(window.location.search);
                const dataParam = urlParams.get('data');
                
                if (dataParam) {
                    mapData = JSON.parse(decodeURIComponent(dataParam));
                    displayMap();
                } else {
                    showError('缺少地图数据参数');
                }
            } catch (error) {
                console.error('加载地图数据失败:', error);
                showError('加载地图数据失败: ' + error.message);
            }
        }
        
        // 显示地图
        function displayMap() {
            if (!mapData || !mapData.solution) {
                showError('地图数据无效');
                return;
            }
            
            const solution = mapData.solution;
            const config = mapData.config;
            
            // 更新标题和统计信息
            document.getElementById('configName').textContent = config.name;
            document.getElementById('totalMatches').textContent = solution.match_count;
            document.getElementById('totalDistance').textContent = (solution.total_distance / 1000).toFixed(1);
                            document.getElementById('totalDuration').textContent = Math.round(solution.total_duration);
            document.getElementById('totalCost').textContent = solution.total_cost.toFixed(2);
            
            // 清空地图容器
            const mapContainer = document.getElementById('chinaMap');
            mapContainer.innerHTML = '';
            
            // 创建地图元素
            createMapElements(solution);
        }
        
        // 创建地图元素
        function createMapElements(solution) {
            const mapContainer = document.getElementById('chinaMap');
            const mapRect = mapContainer.getBoundingClientRect();
            
            // 模拟中国地图的边界
            const bounds = {
                minLat: 18,
                maxLat: 53,
                minLng: 73,
                maxLng: 135
            };
            
            // 存储已添加的位置，避免重复
            const addedLocations = new Set();
            
            // 为每个律师调度创建地图元素
            solution.lawyer_schedules.forEach((lawyer, index) => {
                lawyer.trial_details.forEach((trial, trialIndex) => {
                    // 模拟坐标转换（实际应用中需要真实的坐标转换）
                    const lawyerPos = getMapPosition(lawyer.lawyer_id * 0.1, index * 0.1);
                    const courtPos = getMapPosition(trial.court_id * 0.1, trialIndex * 0.1);
                    
                    // 添加律师位置标记
                    const lawyerKey = `lawyer-${lawyer.lawyer_id}`;
                    if (!addedLocations.has(lawyerKey)) {
                        addedLocations.add(lawyerKey);
                        createLocationMarker(lawyerPos, 'lawyer', lawyer.lawyer_name, mapContainer);
                    }
                    
                    // 添加法院位置标记
                    const courtKey = `court-${trial.court_id}`;
                    if (!addedLocations.has(courtKey)) {
                        addedLocations.add(courtKey);
                        createLocationMarker(courtPos, 'court', trial.court_name, mapContainer);
                    }
                    
                    // 添加路线
                    createRouteLine(lawyerPos, courtPos, lawyer.lawyer_name, trial.court_name, trial, mapContainer);
                });
            });
        }
        
        // 获取地图位置（模拟坐标转换）
        function getMapPosition(lat, lng) {
            // 这里使用简单的模拟计算，实际应用中需要真实的坐标转换
            const mapContainer = document.getElementById('chinaMap');
            const rect = mapContainer.getBoundingClientRect();
            
            // 将经纬度转换为像素坐标
            const x = (lng * 100 + rect.width * 0.2) % (rect.width * 0.8);
            const y = (lat * 100 + rect.height * 0.2) % (rect.height * 0.8);
            
            return { x: x + 100, y: y + 100 };
        }
        
        // 创建位置标记
        function createLocationMarker(position, type, name, container) {
            const marker = document.createElement('div');
            marker.className = `location-marker ${type}`;
            marker.style.left = position.x + 'px';
            marker.style.top = position.y + 'px';
            
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = name;
            tooltip.style.bottom = '25px';
            tooltip.style.left = '50%';
            tooltip.style.transform = 'translateX(-50%)';
            
            marker.appendChild(tooltip);
            container.appendChild(marker);
        }
        
        // 创建路线
        function createRouteLine(start, end, lawyerName, courtName, trial, container) {
            const line = document.createElement('div');
            line.className = 'route-line';
            
            // 计算线条的长度和角度
            const deltaX = end.x - start.x;
            const deltaY = end.y - start.y;
            const length = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
            const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI;
            
            // 设置线条样式
            line.style.left = start.x + 'px';
            line.style.top = start.y + 'px';
            line.style.width = length + 'px';
            line.style.transform = `rotate(${angle}deg)`;
            line.style.animationDelay = Math.random() * 2 + 's';
            
            // 创建提示框
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.innerHTML = `
                ${lawyerName} → ${courtName}<br>
                案件ID: ${trial.case_id}<br>
                开庭时间: ${new Date(trial.start_time).toLocaleString('zh-CN')}<br>
                距离: ${(trial.distance / 1000).toFixed(1)}km<br>
                耗时: ${trial.duration}分钟
            `;
            tooltip.style.bottom = '10px';
            tooltip.style.left = '50%';
            tooltip.style.transform = 'translateX(-50%)';
            
            line.appendChild(tooltip);
            container.appendChild(line);
        }
        
        // 显示错误
        function showError(message) {
            const mapContainer = document.getElementById('chinaMap');
            mapContainer.innerHTML = `
                <div class="error">
                    <i class="fas fa-exclamation-triangle"></i><br>
                    ${message}
                </div>
            `;
        }
    </script>
</body>
</html> 