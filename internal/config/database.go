package config

import (
	"fmt"
	"log"
	"sync"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// 数据库单例管理器
var (
	dbInstance *gorm.DB
	dbOnce     sync.Once
)

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string
	Port     int
	Username string
	Password string
	Database string
	Charset  string
}

// DefaultDatabaseConfig 默认数据库配置
func DefaultDatabaseConfig() *DatabaseConfig {
	return &DatabaseConfig{
		Host:     "localhost",
		Port:     3306,
		Username: "external_tool_user",
		Password: "external_tool_pass@123",
		Database: "external-tool",
		Charset:  "utf8mb4",
	}
}

// DSN 生成数据库连接字符串
func (c *DatabaseConfig) DSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		c.<PERSON><PERSON>, c.Password, c.<PERSON>, c.Port, c.Database, c.Charset)
}

// InitDatabase 初始化数据库连接（单例模式）
func InitDatabase() error {
	var err error
	dbOnce.Do(func() {
		config := DefaultDatabaseConfig()
		dsn := config.DSN()

		dbInstance, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
			Logger: logger.Default.LogMode(logger.Warn),
			// Logger: logger.Default.LogMode(logger.Info),
			NowFunc: func() time.Time {
				return time.Now().Local()
			},
		})

		if err != nil {
			err = fmt.Errorf("failed to connect database: %v", err)
			return
		}

		// 获取通用数据库对象 sql.DB，然后使用其提供的功能
		sqlDB, sqlErr := dbInstance.DB()
		if sqlErr != nil {
			err = fmt.Errorf("failed to get sql.DB: %v", sqlErr)
			return
		}

		// 设置空闲连接池中连接的最大数量
		sqlDB.SetMaxIdleConns(10)

		// 设置打开数据库连接的最大数量
		sqlDB.SetMaxOpenConns(100)

		// 设置连接可复用的最大时间
		sqlDB.SetConnMaxLifetime(time.Hour)

		log.Println("数据库连接初始化成功")
	})

	return err
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	if dbInstance == nil {
		InitDatabase()
		// log.Fatal("数据库未初始化，请先调用 InitDatabase()")
	}
	return dbInstance
}

// CloseDatabase 关闭数据库连接
func CloseDatabase() error {
	if dbInstance != nil {
		sqlDB, err := dbInstance.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}
