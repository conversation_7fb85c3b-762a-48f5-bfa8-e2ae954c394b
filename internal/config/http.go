package config

import (
	"context"
	"log"
	"net/http"
	"net/url"
	"time"

	"github.com/go-resty/resty/v2"
)

type contextKey string

const startTimeKey contextKey = "start_time"

// formatURLForLog formats URL for logging with decoded query parameters but keeps full URL
func formatURLForLog(rawURL string) string {
	if u, err := url.Parse(rawURL); err == nil && u.RawQuery != "" {
		// 解码查询参数以便阅读
		if decoded, err := url.QueryUnescape(u.RawQuery); err == nil {
			return u.Scheme + "://" + u.Host + u.Path + "?" + decoded
		}
		return rawURL
	}
	return rawURL
}

func GetHTTPClient() *resty.Client {
	return resty.New().
		SetTimeout(5 * time.Minute).
		SetRetryCount(3).
		SetRetryWaitTime(10 * time.Second).
		// 请求开始前记录时间
		OnBeforeRequest(func(c *resty.Client, req *resty.Request) error {
			req.SetContext(context.WithValue(req.Context(), startTimeKey, time.Now()))
			// log.Printf("HTTP Request: %s %s", req.Method, req.URL)
			return nil
		}).
		// 请求完成后记录耗时
		OnAfterResponse(func(c *resty.Client, resp *resty.Response) error {
			if startTime, ok := resp.Request.Context().Value(startTimeKey).(time.Time); ok {
				duration := time.Since(startTime)
				log.Printf("HTTP Response: %s %s - %d - %v",
					resp.Request.Method,
					formatURLForLog(resp.Request.URL),
					resp.StatusCode(),
					duration)
			}
			return nil
		}).
		// 请求出错时也记录耗时
		OnError(func(req *resty.Request, err error) {
			if startTime, ok := req.Context().Value(startTimeKey).(time.Time); ok {
				duration := time.Since(startTime)
				log.Printf("HTTP Error: %s %s - %v - %v",
					req.Method,
					formatURLForLog(req.URL),
					err,
					duration)
			}
		}).
		AddRetryCondition(func(r *resty.Response, err error) bool {
			// 对连接超时或非 200 重试
			return err != nil || r.StatusCode() != http.StatusOK
		})
}
