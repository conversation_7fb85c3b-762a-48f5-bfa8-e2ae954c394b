package service

import (
	"external-tool/internal/config"
	"external-tool/internal/consts"
	"external-tool/internal/model"
	"external-tool/internal/model/imap"
	"fmt"
	"log"
	"sync"
	"time"

	"gorm.io/gorm"
)

type MapService struct {
	mapModel imap.MapModel
	db       *gorm.DB
}

func NewMapService(name string) *MapService {
	return &MapService{
		mapModel: imap.NewMapModel(name),
		db:       config.GetDB(),
	}
}

func (m *MapService) InitAddress() {
	// 获取所有需要处理地址的法院
	var courts []model.Court
	err := m.db.Where("lat = 0 AND lng = 0").Find(&courts).Error
	if err != nil {
		log.Fatalf("获取法院地址失败: %v", err)
	}

	// 获取所有需要处理地址的律师
	var lawyers []model.Lawyer
	err = m.db.Where("lat = 0 AND lng = 0").Find(&lawyers).Error
	if err != nil {
		log.Fatalf("获取律师地址失败: %v", err)
	}

	// 创建worker pool，5个worker，间隔1秒
	pool := NewWorkerPool(5, time.Second*1)
	defer pool.Close()

	// 处理法院地址
	for _, court := range courts {
		court := court // 避免闭包变量问题
		pool.AddTask(func() {
			location, addressComponents, err := m.mapModel.Geocoder(court.Name)
			if err != nil {
				log.Printf("获取法院地址失败: %v", err)
				return
			}

			// 更新法院地址信息
			updates := map[string]any{
				"formatted_address": fmt.Sprintf("%s%s%s%s%s", addressComponents.Province, addressComponents.City, addressComponents.District, addressComponents.Street, addressComponents.StreetNumber),
				"province":          addressComponents.Province,
				"city":              addressComponents.City,
				"district":          addressComponents.District,
				"street":            addressComponents.Street,
				"street_number":     addressComponents.StreetNumber,
				"lng":               location.Lng,
				"lat":               location.Lat,
				"reliability":       0,
			}

			if err := m.db.Model(&court).Updates(updates).Error; err != nil {
				log.Printf("更新法院地址失败: %v", err)
				return
			}
		})
	}

	// 等待法院地址处理完成
	pool.Wait()

	// 处理律师地址
	for _, lawyer := range lawyers {
		lawyer := lawyer // 避免闭包变量问题
		pool.AddTask(func() {
			location, addressComponents, err := m.mapModel.Geocoder(lawyer.OriginalAddress)
			if err != nil {
				log.Printf("获取律师地址失败: %v", err)
				return
			}

			// 更新律师地址信息
			updates := map[string]interface{}{
				"formatted_address": fmt.Sprintf("%s%s%s%s%s", addressComponents.Province, addressComponents.City, addressComponents.District, addressComponents.Street, addressComponents.StreetNumber),
				"province":          addressComponents.Province,
				"city":              addressComponents.City,
				"district":          addressComponents.District,
				"street":            addressComponents.Street,
				"street_number":     addressComponents.StreetNumber,
				"lng":               location.Lng,
				"lat":               location.Lat,
				"reliability":       0,
			}

			if err := m.db.Model(&lawyer).Updates(updates).Error; err != nil {
				log.Printf("更新律师地址失败: %v", err)
				return
			}
		})
	}

	// 等待律师地址处理完成
	pool.Wait()
}

func (m *MapService) InitRoute() {
	// 通过join获取trial中关联的court坐标信息（去重）
	var courtLocations []struct {
		ID               uint    `json:"id"`
		Lat              float64 `json:"lat"`
		Lng              float64 `json:"lng"`
		FormattedAddress string  `json:"formatted_address"`
	}
	m.db.Table("(SELECT DISTINCT court_id FROM trials where status = 1) AS distinct_trials").
		Select("courts.id, courts.lat, courts.lng, courts.formatted_address").
		Joins("INNER JOIN courts ON distinct_trials.court_id = courts.id").
		Where("courts.lat != 0 AND courts.lng != 0 AND courts.status = 1").
		Find(&courtLocations)

	// 获取所有律师坐标信息（去重）
	var lawyerLocations []struct {
		ID               uint    `json:"id"`
		Lat              float64 `json:"lat"`
		Lng              float64 `json:"lng"`
		FormattedAddress string  `json:"formatted_address"`
	}
	m.db.Table("lawyers").
		Select("DISTINCT id, lat, lng, formatted_address").
		Where("lat != 0 AND lng != 0 AND status = 1").
		Find(&lawyerLocations)

	// 查询已存在的路线，用坐标组合作为key
	var existingRoutes []struct {
		Type    string  `json:"type"`
		FromLat float64 `json:"from_lat"`
		FromLng float64 `json:"from_lng"`
		ToLat   float64 `json:"to_lat"`
		ToLng   float64 `json:"to_lng"`
	}
	m.db.Table("routes").
		Select("type, from_lat, from_lng, to_lat, to_lng").
		Find(&existingRoutes)

	// 创建已存在路线的映射
	exist := make(map[string]bool)
	for _, route := range existingRoutes {
		from := &model.Location{Lat: route.FromLat, Lng: route.FromLng}
		to := &model.Location{Lat: route.ToLat, Lng: route.ToLng}
		key := m.getRouteKey(route.Type, from, to)
		exist[key] = true
	}

	pool := NewWorkerPool(90, time.Second*1)
	defer pool.Close()

	// 处理律师到法院的路线组合
	for _, lawyer := range lawyerLocations {
		for _, court := range courtLocations {
			fromLoc := &model.Location{
				Lat:              lawyer.Lat,
				Lng:              lawyer.Lng,
				FormattedAddress: lawyer.FormattedAddress,
			}
			toLoc := &model.Location{
				Lat:              court.Lat,
				Lng:              court.Lng,
				FormattedAddress: court.FormattedAddress,
			}

			// 添加律师到法院的路径规划任务
			m.addRouteIfNotExists(consts.LawyerToCourt, fromLoc, toLoc, exist, pool)

			// 添加法院到律师的路径规划任务
			m.addRouteIfNotExists(consts.CourtToLawyer, toLoc, fromLoc, exist, pool)
		}
	}

	pool.Wait()
}

// addRouteIfNotExists 检查路线是否存在，如果不存在则添加路径规划任务
func (m *MapService) addRouteIfNotExists(routeType string, from, to *model.Location, exist map[string]bool, pool *WorkerPool) {
	key := m.getRouteKey(routeType, from, to)
	if _, ok := exist[key]; !ok {
		exist[key] = true
		// 避免闭包变量问题
		fromLoc, toLoc := from, to
		pool.AddTask(func() {
			m.processRoute(routeType, fromLoc, toLoc)
		})
	}
}

func (m *MapService) processRoute(routeType string, from, to *model.Location) {
	// 设置次日上午7点作为出发时间
	tomorrow := time.Now().Add(time.Hour * 24)
	departureTime := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 7, 0, 0, 0, tomorrow.Location())

	routes, _, err := m.mapModel.DirectionTransit(from, to, "RECOMMEND", departureTime)
	if err != nil {
		log.Printf("计算距离失败: %v from: %s to: %s", err, from.FormattedAddress, to.FormattedAddress)
		return
	}

	status := 0
	distance := 0
	duration := 0
	cost := 0
	remark := ""
	if len(routes) > 0 {
		status = 1
		distance = routes[0].Distance
		duration = routes[0].Duration
		cost = routes[0].Price
	} else {
		status = 0
		remark = "没有找到路线"
	}

	m.db.Model(&model.Routes{}).Create(map[string]any{
		"type":                   routeType,
		"from_lat":               from.Lat,
		"from_lng":               from.Lng,
		"from_formatted_address": from.FormattedAddress,
		"to_lat":                 to.Lat,
		"to_lng":                 to.Lng,
		"to_formatted_address":   to.FormattedAddress,
		"distance":               distance,
		"duration":               duration,
		"cost":                   cost,
		"status":                 status,
		"remark":                 remark,
		"detail":                 "",
		"created_at":             time.Now(),
		"updated_at":             time.Now(),
	})
}

func (m *MapService) getRouteKey(routeType string, from, to *model.Location) string {
	return fmt.Sprintf("%s:%.7f,%.7f:%.7f,%.7f", routeType, from.Lat, from.Lng, to.Lat, to.Lng)
}

// InitRouteV2 实现基于开庭时间的路径规划
func (m *MapService) InitRouteV2() {
	// 获取所有开庭信息，包括时间和法院信息
	var trialInfos []struct {
		ID        uint      `json:"id"`
		CourtID   uint      `json:"court_id"`
		StartTime time.Time `json:"start_time"`
		EndTime   time.Time `json:"end_time"`
		CourtLat  float64   `json:"court_lat"`
		CourtLng  float64   `json:"court_lng"`
		CourtAddr string    `json:"court_addr"`
	}

	m.db.Table("trials").
		Select("trials.id, trials.court_id, trials.start_time, trials.end_time, courts.lat as court_lat, courts.lng as court_lng, courts.formatted_address as court_addr").
		Joins("INNER JOIN courts ON trials.court_id = courts.id").
		Where("trials.status = 1 AND courts.lat != 0 AND courts.lng != 0 AND courts.status = 1").
		Where("trials.start_time IS NOT NULL AND trials.end_time IS NOT NULL").
		Order("trials.start_time ASC").
		Find(&trialInfos)

	// 获取所有律师信息
	var lawyerInfos []struct {
		ID               uint    `json:"id"`
		Lat              float64 `json:"lat"`
		Lng              float64 `json:"lng"`
		FormattedAddress string  `json:"formatted_address"`
	}
	m.db.Table("lawyers").
		Select("id, lat, lng, formatted_address").
		Where("lat != 0 AND lng != 0 AND status = 1").
		Find(&lawyerInfos)

	// 查询已存在的路线
	var existingRoutes []struct {
		Type          string     `json:"type"`
		FromLat       float64    `json:"from_lat"`
		FromLng       float64    `json:"from_lng"`
		ToLat         float64    `json:"to_lat"`
		ToLng         float64    `json:"to_lng"`
		FromStartTime *time.Time `json:"from_start_time"`
		FromEndTime   *time.Time `json:"from_end_time"`
		ToStartTime   *time.Time `json:"to_start_time"`
		ToEndTime     *time.Time `json:"to_end_time"`
	}
	m.db.Table("routes").
		Select("type, from_lat, from_lng, to_lat, to_lng, from_start_time, from_end_time, to_start_time, to_end_time").
		Find(&existingRoutes)

	// 创建已存在路线的映射
	exist := make(map[string]bool)
	for _, route := range existingRoutes {
		key := m.getRouteKeyV2(route.Type, route.FromLat, route.FromLng, route.ToLat, route.ToLng, route.FromStartTime, route.FromEndTime, route.ToStartTime, route.ToEndTime)
		exist[key] = true
	}

	// 创建worker pool
	pool := NewWorkerPool(90, time.Second*1)
	defer pool.Close()

	// 计算总任务数
	totalTasks := len(lawyerInfos) * len(trialInfos) * 2 // 律师到法院 + 法院到律师
	for i := 0; i < len(trialInfos); i++ {
		for j := i + 1; j < len(trialInfos); j++ {
			// 检查时间衔接是否合理（结束时间+1小时 < 开始时间）
			if trialInfos[i].EndTime.Add(time.Hour).Before(trialInfos[j].StartTime) {
				totalTasks++
			}
		}
	}

	log.Printf("开始计算路线，总任务数: %d", totalTasks)

	processedTasks := 0
	var mu sync.Mutex

	// 处理律师到法院的路线
	for _, lawyer := range lawyerInfos {
		for _, trial := range trialInfos {
			fromLoc := &model.Location{
				Lat:              lawyer.Lat,
				Lng:              lawyer.Lng,
				FormattedAddress: lawyer.FormattedAddress,
			}
			toLoc := &model.Location{
				Lat:              trial.CourtLat,
				Lng:              trial.CourtLng,
				FormattedAddress: trial.CourtAddr,
			}

			// 检查是否已存在
			key := m.getRouteKeyV2(consts.LawyerToCourt, fromLoc.Lat, fromLoc.Lng, toLoc.Lat, toLoc.Lng, nil, nil, &trial.StartTime, &trial.EndTime)
			if !exist[key] {
				exist[key] = true
				// 复制变量避免闭包问题
				trialCopy := trial
				fromLocCopy := fromLoc
				toLocCopy := toLoc

				pool.AddTask(func() {
					m.processRouteV2(consts.LawyerToCourt, fromLocCopy, toLocCopy, nil, nil, &trialCopy.StartTime, &trialCopy.EndTime, trialCopy.StartTime)

					mu.Lock()
					processedTasks++
					if processedTasks%1000 == 0 {
						log.Printf("进度: %d/%d", processedTasks, totalTasks)
					}
					mu.Unlock()
				})
			}
		}
	}

	// 处理法院到法院的路线（按时间排序，只处理时间衔接合理的）
	for i := 0; i < len(trialInfos); i++ {
		for j := i + 1; j < len(trialInfos); j++ {
			// 检查时间衔接是否合理（结束时间+1小时 < 开始时间）
			if trialInfos[i].EndTime.Add(time.Hour).Before(trialInfos[j].StartTime) {
				fromLoc := &model.Location{
					Lat:              trialInfos[i].CourtLat,
					Lng:              trialInfos[i].CourtLng,
					FormattedAddress: trialInfos[i].CourtAddr,
				}
				toLoc := &model.Location{
					Lat:              trialInfos[j].CourtLat,
					Lng:              trialInfos[j].CourtLng,
					FormattedAddress: trialInfos[j].CourtAddr,
				}

				key := m.getRouteKeyV2(consts.CourtToCourt, fromLoc.Lat, fromLoc.Lng, toLoc.Lat, toLoc.Lng, &trialInfos[i].StartTime, &trialInfos[i].EndTime, &trialInfos[j].StartTime, &trialInfos[j].EndTime)
				if !exist[key] {
					exist[key] = true
					// 复制变量避免闭包问题
					trial1 := trialInfos[i]
					trial2 := trialInfos[j]
					fromLocCopy := fromLoc
					toLocCopy := toLoc

					pool.AddTask(func() {
						// 从第一个法院的结束时间开始，到第二个法院的开始时间
						m.processRouteV2(consts.CourtToCourt, fromLocCopy, toLocCopy, &trial1.StartTime, &trial1.EndTime, &trial2.StartTime, &trial2.EndTime, trial1.EndTime)

						mu.Lock()
						processedTasks++
						if processedTasks%1000 == 0 {
							log.Printf("进度: %d/%d", processedTasks, totalTasks)
						}
						mu.Unlock()
					})
				}
			}
		}
	}

	// 处理法院到律师的路线（返程）
	for _, lawyer := range lawyerInfos {
		for _, trial := range trialInfos {
			fromLoc := &model.Location{
				Lat:              trial.CourtLat,
				Lng:              trial.CourtLng,
				FormattedAddress: trial.CourtAddr,
			}
			toLoc := &model.Location{
				Lat:              lawyer.Lat,
				Lng:              lawyer.Lng,
				FormattedAddress: lawyer.FormattedAddress,
			}

			// 检查是否已存在
			key := m.getRouteKeyV2(consts.CourtToLawyer, fromLoc.Lat, fromLoc.Lng, toLoc.Lat, toLoc.Lng, &trial.StartTime, &trial.EndTime, nil, nil)
			if !exist[key] {
				exist[key] = true
				// 复制变量避免闭包问题
				trialCopy := trial
				fromLocCopy := fromLoc
				toLocCopy := toLoc

				pool.AddTask(func() {
					// 从开庭结束时间开始返程
					m.processRouteV2(consts.CourtToLawyer, fromLocCopy, toLocCopy, &trialCopy.StartTime, &trialCopy.EndTime, nil, nil, trialCopy.EndTime)

					mu.Lock()
					processedTasks++
					if processedTasks%1000 == 0 {
						log.Printf("进度: %d/%d", processedTasks, totalTasks)
					}
					mu.Unlock()
				})
			}
		}
	}

	pool.Wait()
	log.Printf("路线计算完成，总任务数: %d", totalTasks)
}

// getRouteKeyV2 生成包含时间信息的路线键
func (m *MapService) getRouteKeyV2(routeType string, fromLat, fromLng, toLat, toLng float64, fromStartTime, fromEndTime, toStartTime, toEndTime *time.Time) string {
	var fromStartTimeStr, fromEndTimeStr, toStartTimeStr, toEndTimeStr string
	if fromStartTime != nil {
		fromStartTimeStr = fromStartTime.Format("2006-01-02 15:04:05")
	}
	if fromEndTime != nil {
		fromEndTimeStr = fromEndTime.Format("2006-01-02 15:04:05")
	}
	if toStartTime != nil {
		toStartTimeStr = toStartTime.Format("2006-01-02 15:04:05")
	}
	if toEndTime != nil {
		toEndTimeStr = toEndTime.Format("2006-01-02 15:04:05")
	}
	return fmt.Sprintf("%s:%.7f,%.7f:%.7f,%.7f:%s:%s:%s:%s", routeType, fromLat, fromLng, toLat, toLng, fromStartTimeStr, fromEndTimeStr, toStartTimeStr, toEndTimeStr)
}

// processRouteV2 处理带时间的路线规划
func (m *MapService) processRouteV2(routeType string, from, to *model.Location, fromStartTime, fromEndTime, toStartTime, toEndTime *time.Time, departureTime time.Time) {
	var bestRoute *model.Route
	var bestRemark string
	var finalDuration int
	var status int
	var remarkStr string

	switch routeType {
	case consts.LawyerToCourt:
		// 律师到法院：多批次提前计算最佳方案
		bestRoute, bestRemark, finalDuration = m.findBestLawyerToCourtRoute(from, to, toStartTime, departureTime)
		if bestRoute != nil {
			status = 1
			remarkStr = fmt.Sprintf("最佳出发策略，总行程时间: %d分钟", finalDuration)
		} else {
			status = 0
			remarkStr = "没有找到合适的出发方案"
		}
	case consts.CourtToCourt:
		// 法院到法院：从结束时间开始
		bestRoute, bestRemark, finalDuration = m.findBestCourtToCourtRoute(from, to, toStartTime, departureTime)
		if bestRoute != nil {
			status = 1
			remarkStr = fmt.Sprintf("法院间转移，行程时间: %d分钟", finalDuration)
		} else {
			status = 0
			remarkStr = "没有找到合适的转移路线"
		}
	case consts.CourtToLawyer:
		// 法院到律师：结束时间固定，往后延期寻找最佳返程时间
		bestRoute, bestRemark, finalDuration = m.findBestCourtToLawyerRoute(from, to, fromEndTime, departureTime)
		if bestRoute != nil {
			status = 1
			remarkStr = fmt.Sprintf("最佳返程策略，总等候+行程时间: %d分钟", finalDuration)
		} else {
			status = 0
			remarkStr = "没有找到合适的返程方案"
		}
	default:
		status = 0
		remarkStr = "未知的路线类型"
	}

	distance := 0
	duration := 0
	cost := 0
	detail := bestRemark

	if bestRoute != nil {
		distance = bestRoute.Distance
		duration = finalDuration // 使用计算出的实际时间
		cost = bestRoute.Price
	}

	// 保存路线信息
	m.db.Model(&model.Routes{}).Create(map[string]any{
		"type":                   routeType,
		"from_start_time":        fromStartTime,
		"from_end_time":          fromEndTime,
		"to_start_time":          toStartTime,
		"to_end_time":            toEndTime,
		"from_lat":               from.Lat,
		"from_lng":               from.Lng,
		"from_formatted_address": from.FormattedAddress,
		"to_lat":                 to.Lat,
		"to_lng":                 to.Lng,
		"to_formatted_address":   to.FormattedAddress,
		"distance":               distance,
		"duration":               duration,
		"cost":                   cost,
		"status":                 status,
		"remark":                 remarkStr,
		"detail":                 detail,
		"created_at":             time.Now(),
		"updated_at":             time.Now(),
	})
}

// findBestLawyerToCourtRoute 为律师到法院找到最佳提前出发方案
func (m *MapService) findBestLawyerToCourtRoute(from, to *model.Location, trialStartTime *time.Time, departureTime time.Time) (*model.Route, string, int) {
	if trialStartTime == nil {
		return nil, "", 0
	}

	// 定义多个提前时间策略（从小到大）
	earlyDepartures := []time.Duration{
		30 * time.Minute, // 提前30分钟
		1 * time.Hour,    // 提前1小时
		2 * time.Hour,    // 提前2小时
		4 * time.Hour,    // 提前4小时
		8 * time.Hour,    // 提前8小时（过夜车）
		12 * time.Hour,   // 提前12小时
		24 * time.Hour,   // 前一天同一时间
	}

	var bestRoute *model.Route
	var bestRemark string
	var bestScore float64 = 999999 // 分数越低越好
	var finalDuration int

	for _, earlyTime := range earlyDepartures {
		attemptTime := trialStartTime.Add(-earlyTime)

		// 跳过未来时间
		if attemptTime.After(time.Now()) {
			continue
		}

		routes, remark, err := m.mapModel.DirectionTransitV2(from, to, "RECOMMEND", attemptTime)
		if err != nil {
			continue
		}

		if len(routes) > 0 {
			route := &routes[0]
			arrivalTime := attemptTime.Add(time.Duration(route.Duration) * time.Minute)

			// 确保能够准时到达
			if arrivalTime.Before(*trialStartTime) {
				// 计算等候时间
				waitTime := int(trialStartTime.Sub(arrivalTime).Minutes())
				totalTime := route.Duration + waitTime

				// 计算综合评分：考虑总时间、费用、等候时间
				score := float64(totalTime) + float64(route.Price)*0.1 + float64(waitTime)*0.5

				if bestRoute == nil || score < bestScore {
					bestRoute = route
					bestRemark = remark
					bestScore = score
					finalDuration = totalTime
					log.Printf("律师到法院最佳方案更新: 出发时间 %s, 到达时间 %s, 等候 %d分钟, 总时间 %d分钟, 费用 %d元",
						attemptTime.Format("2006-01-02 15:04"), arrivalTime.Format("2006-01-02 15:04"), waitTime, totalTime, route.Price)
				}
			}
		}
	}

	return bestRoute, bestRemark, finalDuration
}

// findBestCourtToLawyerRoute 为法院到律师找到最佳延期返程方案
func (m *MapService) findBestCourtToLawyerRoute(from, to *model.Location, trialEndTime *time.Time, departureTime time.Time) (*model.Route, string, int) {
	if trialEndTime == nil {
		return nil, "", 0
	}

	// 定义多个延后时间策略（从小到大）
	delayDepartures := []time.Duration{
		0 * time.Minute,  // 立即出发
		30 * time.Minute, // 30分钟后
		1 * time.Hour,    // 1小时后
		2 * time.Hour,    // 2小时后
		4 * time.Hour,    // 4小时后
		8 * time.Hour,    // 8小时后（当天晚上）
		12 * time.Hour,   // 12小时后
		24 * time.Hour,   // 第二天同一时间
		36 * time.Hour,   // 第二天12小时后
		48 * time.Hour,   // 第三天同一时间
	}

	var bestRoute *model.Route
	var bestRemark string
	var bestScore float64 = 999999 // 分数越低越好
	var finalDuration int

	for _, delayTime := range delayDepartures {
		attemptTime := trialEndTime.Add(delayTime)

		routes, remark, err := m.mapModel.DirectionTransitV2(from, to, "RECOMMEND", attemptTime)
		if err != nil {
			continue
		}

		if len(routes) > 0 {
			route := &routes[0]
			waitTime := int(delayTime.Minutes())
			totalTime := route.Duration + waitTime

			// 计算综合评分：考虑总时间、费用、等候时间（但等候时间权重较低）
			score := float64(totalTime) + float64(route.Price)*0.1 + float64(waitTime)*0.2

			if bestRoute == nil || score < bestScore {
				bestRoute = route
				bestRemark = remark
				bestScore = score
				finalDuration = totalTime
				log.Printf("法院到律师最佳方案更新: 开庭结束 %s, 出发时间 %s, 等候 %d分钟, 行程 %d分钟, 总时间 %d分钟, 费用 %d元",
					trialEndTime.Format("2006-01-02 15:04"), attemptTime.Format("2006-01-02 15:04"), waitTime, route.Duration, totalTime, route.Price)
			}
		}
	}

	return bestRoute, bestRemark, finalDuration
}

// findBestCourtToCourtRoute 为法院到法院找到最佳路线
func (m *MapService) findBestCourtToCourtRoute(from, to *model.Location, trialStartTime *time.Time, departureTime time.Time) (*model.Route, string, int) {
	// 法院到法院的情况相对简单，直接从结束时间开始计算
	routes, remark, err := m.mapModel.DirectionTransitV2(from, to, "RECOMMEND", departureTime)
	if err != nil {
		log.Printf("计算法院间路线失败: %v from: %s to: %s", err, from.FormattedAddress, to.FormattedAddress)
		return nil, "", 0
	}

	if len(routes) == 0 {
		return nil, "", 0
	}

	bestRoute := &routes[0]
	finalDuration := bestRoute.Duration

	log.Printf("法院到法院路线: 出发时间 %s, 行程时间 %d分钟, 费用 %d元",
		departureTime.Format("2006-01-02 15:04"), finalDuration, bestRoute.Price)

	return bestRoute, remark, finalDuration
}
