package service

import (
	"fmt"
	"log"
	"time"
)

// ExampleWorkerPool 展示WorkerPool的基本用法
func ExampleWorkerPool() {
	// 创建一个拥有3个worker，每个任务间隔500毫秒的工作池
	pool := NewWorkerPool(3, time.Millisecond*500)
	defer pool.Close()

	// 添加一些测试任务
	for i := 0; i < 10; i++ {
		taskID := i // 避免闭包变量问题

		// 阻塞式添加任务 - 如果所有worker都忙碌，会等待
		pool.AddTask(func() {
			fmt.Printf("执行任务 %d，当前时间: %s\n", taskID, time.Now().Format("15:04:05.000"))
			// 模拟任务执行
			time.Sleep(time.Millisecond * 200)
			fmt.Printf("任务 %d 完成\n", taskID)
		})
	}

	// 等待所有任务完成
	pool.Wait()
	fmt.Println("所有任务已完成")
}

// ExampleTryAddTask 展示非阻塞式添加任务
func ExampleTryAddTask() {
	// 创建一个只有1个worker的工作池
	pool := NewWorkerPool(1, time.Second*1)
	defer pool.Close()

	// 添加一个耗时任务
	pool.AddTask(func() {
		fmt.Println("开始执行长任务...")
		time.Sleep(time.Second * 3)
		fmt.Println("长任务完成")
	})

	// 尝试添加更多任务（非阻塞）
	for i := 0; i < 5; i++ {
		taskID := i
		success := pool.TryAddTask(func() {
			fmt.Printf("快速任务 %d 执行\n", taskID)
		})

		if success {
			fmt.Printf("任务 %d 添加成功\n", taskID)
		} else {
			fmt.Printf("任务 %d 添加失败，所有worker都忙碌\n", taskID)
		}
	}

	pool.Wait()
}

// ExampleMapServiceWithWorkerPool 展示如何在MapService中配置WorkerPool
func ExampleMapServiceWithWorkerPool() {
	// 模拟地理编码任务
	type GeocodeTask struct {
		ID      int
		Address string
	}

	tasks := []GeocodeTask{
		{1, "北京市朝阳区"},
		{2, "上海市浦东新区"},
		{3, "广州市天河区"},
		{4, "深圳市南山区"},
		{5, "杭州市西湖区"},
	}

	// 根据任务数量和API限制创建合适的WorkerPool
	// 这里假设API每秒最多允许5个请求，所以用5个worker，间隔1秒
	pool := NewWorkerPool(5, time.Second*1)
	defer pool.Close()

	fmt.Printf("开始处理 %d 个地理编码任务...\n", len(tasks))

	for _, task := range tasks {
		task := task // 避免闭包变量问题
		pool.AddTask(func() {
			// 模拟地理编码API调用
			fmt.Printf("正在处理地址: %s (ID: %d)\n", task.Address, task.ID)

			// 模拟API延迟
			time.Sleep(time.Millisecond * 300)

			// 模拟处理结果
			lat, lng := 39.9+float64(task.ID)*0.1, 116.3+float64(task.ID)*0.1
			fmt.Printf("地址 %s 处理完成: lat=%.2f, lng=%.2f\n", task.Address, lat, lng)
		})
	}

	pool.Wait()
	fmt.Println("所有地理编码任务已完成")
}

// BenchmarkWorkerPool 简单的性能测试示例
func BenchmarkWorkerPool() {
	const taskCount = 1000
	const workerCount = 10

	fmt.Printf("开始性能测试：%d个任务，%d个worker\n", taskCount, workerCount)

	start := time.Now()

	pool := NewWorkerPool(workerCount, 0) // 无间隔
	defer pool.Close()

	for i := 0; i < taskCount; i++ {
		taskID := i
		pool.AddTask(func() {
			// 模拟一些计算工作
			sum := 0
			for j := 0; j < 1000; j++ {
				sum += j
			}
			if taskID%100 == 0 {
				log.Printf("完成任务 %d，结果: %d", taskID, sum)
			}
		})
	}

	pool.Wait()

	elapsed := time.Since(start)
	fmt.Printf("性能测试完成，耗时: %v，平均每个任务: %v\n",
		elapsed, elapsed/time.Duration(taskCount))
}
