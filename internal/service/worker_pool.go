package service

import (
	"sync"
	"time"
)

// Task 定义任务接口
type Task func()

// WorkerPool 工作池结构
type WorkerPool struct {
	workers   int           // worker数量
	interval  time.Duration // 任务间隔
	taskChan  chan Task     // 任务通道
	wg        sync.WaitGroup
	closed    bool
	closeChan chan struct{}
	mu        sync.RWMutex
}

// NewWorkerPool 创建新的工作池
// workers: worker数量
// interval: 每个任务执行完后的间隔时间
func NewWorkerPool(workers int, interval time.Duration) *WorkerPool {
	if workers <= 0 {
		workers = 1
	}

	pool := &WorkerPool{
		workers:   workers,
		interval:  interval,
		taskChan:  make(chan Task),
		closeChan: make(chan struct{}),
	}

	// 启动worker goroutines
	for i := 0; i < workers; i++ {
		go pool.worker()
	}

	return pool
}

// worker 工作goroutine
func (p *WorkerPool) worker() {
	for {
		select {
		case task, ok := <-p.taskChan:
			if !ok {
				return // 通道已关闭
			}

			// 执行任务
			task()
			p.wg.Done()

			// 任务间隔
			if p.interval > 0 {
				time.Sleep(p.interval)
			}

		case <-p.closeChan:
			return // 收到关闭信号
		}
	}
}

// AddTask 添加任务（阻塞式）
// 如果所有worker都忙碌，此方法会阻塞直到有worker可用
func (p *WorkerPool) AddTask(task Task) bool {
	p.mu.RLock()
	if p.closed {
		p.mu.RUnlock()
		return false
	}
	p.mu.RUnlock()

	p.wg.Add(1)

	select {
	case p.taskChan <- task:
		return true
	case <-p.closeChan:
		p.wg.Done()
		return false
	}
}

// TryAddTask 尝试添加任务（非阻塞式）
// 如果所有worker都忙碌，立即返回false
func (p *WorkerPool) TryAddTask(task Task) bool {
	p.mu.RLock()
	if p.closed {
		p.mu.RUnlock()
		return false
	}
	p.mu.RUnlock()

	select {
	case p.taskChan <- task:
		p.wg.Add(1)
		return true
	default:
		return false
	}
}

// Wait 等待所有任务完成
func (p *WorkerPool) Wait() {
	p.wg.Wait()
}

// Close 关闭工作池
func (p *WorkerPool) Close() {
	p.mu.Lock()
	if p.closed {
		p.mu.Unlock()
		return
	}
	p.closed = true
	p.mu.Unlock()

	close(p.closeChan)
	close(p.taskChan)
}

// GetWorkerCount 获取worker数量
func (p *WorkerPool) GetWorkerCount() int {
	return p.workers
}

// GetInterval 获取任务间隔
func (p *WorkerPool) GetInterval() time.Duration {
	return p.interval
}

// IsClosed 检查是否已关闭
func (p *WorkerPool) IsClosed() bool {
	p.mu.RLock()
	defer p.mu.RUnlock()
	return p.closed
}
