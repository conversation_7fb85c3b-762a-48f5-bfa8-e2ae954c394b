package service

import (
	"external-tool/internal/config"
	"external-tool/internal/model"
	"external-tool/internal/model/imap"
	"external-tool/internal/service/schedule"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type WebService struct {
	db *gorm.DB
}

func NewWebService() *WebService {
	db := config.GetDB()
	return &WebService{db: db}
}

func (w *WebService) Start() {
	r := gin.Default()

	// 加载HTML模板
	r.LoadHTMLGlob("templates/*")

	// 静态文件服务
	r.Static("/static", "./static")

	// 主页
	r.GET("/", w.homePage)

	// 遗传算法地图页面
	r.GET("/genetic/map", w.geneticMapPage)

	// API 路由
	api := r.Group("/api")
	{
		// 地址解析接口
		api.POST("/geocoder", w.geocoder)

		// 律师接口
		api.GET("/lawyers", w.getLawyers)
		api.GET("/lawyers/search", w.searchLawyers)
		api.GET("/lawyers/:id", w.getLawyer)
		api.POST("/lawyers", w.createLawyer)
		api.PUT("/lawyers/:id", w.updateLawyer)
		api.DELETE("/lawyers/:id", w.deleteLawyer)
		api.POST("/lawyers/batch", w.batchOperationLawyers)

		// 法院接口
		api.GET("/courts", w.getCourts)
		api.GET("/courts/search", w.searchCourts)
		api.GET("/courts/:id", w.getCourt)
		api.POST("/courts", w.createCourt)
		api.PUT("/courts/:id", w.updateCourt)
		api.DELETE("/courts/:id", w.deleteCourt)
		api.POST("/courts/batch", w.batchOperationCourts)

		// 开庭接口
		api.GET("/trials", w.getTrials)
		api.GET("/trials/:id", w.getTrial)
		api.POST("/trials", w.createTrial)
		api.PUT("/trials/:id", w.updateTrial)
		api.DELETE("/trials/:id", w.deleteTrial)
		api.POST("/trials/batch", w.batchOperationTrials)

		// 路线接口
		api.GET("/routes", w.getRoutes)
		api.GET("/routes/:id", w.getRoute)
		api.POST("/routes", w.createRoute)
		api.PUT("/routes/:id", w.updateRoute)
		api.DELETE("/routes/:id", w.deleteRoute)
		api.POST("/routes/batch", w.batchOperationRoutes)

		// 遗传算法调度接口
		api.GET("/genetic/schedule", w.getGeneticSchedule)
		api.POST("/genetic/schedule", w.runGeneticSchedule)
		api.GET("/genetic/configs", w.getGeneticConfigs)
	}

	fmt.Println("Web服务器启动在 http://localhost:8080")
	log.Fatal(r.Run(":8080"))
}

// 主页
func (w *WebService) homePage(c *gin.Context) {
	c.HTML(http.StatusOK, "index.html", gin.H{
		"title": "法诉数据管理系统",
	})
}

// 遗传算法地图页面
func (w *WebService) geneticMapPage(c *gin.Context) {
	c.HTML(http.StatusOK, "genetic_map.html", gin.H{
		"title": "遗传算法调度地图",
	})
}

// 通用分页响应结构
type PageResponse struct {
	Data       interface{} `json:"data"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
}

// 获取分页参数
func (w *WebService) getPageParams(c *gin.Context) (int, int, string, string) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	search := c.Query("search")
	status := c.Query("status")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	return page, pageSize, search, status
}

// Geocoder 地址解析接口
func (w *WebService) geocoder(c *gin.Context) {
	var req struct {
		Address string `json:"address"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "地址解析失败"})
		return
	}

	if req.Address == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "地址不能为空"})
		return
	}

	// 调用腾讯地图的地理编码API
	mapModel := imap.NewMapModel("tencent")
	location, addressComponents, err := mapModel.Geocoder(req.Address)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "地址解析失败: " + err.Error()})
		return
	}

	// 格式化地址
	formattedAddress := fmt.Sprintf("%s%s%s%s%s",
		addressComponents.Province,
		addressComponents.City,
		addressComponents.District,
		addressComponents.Street,
		addressComponents.StreetNumber)

	response := gin.H{
		"formatted_address": formattedAddress,
		"province":          addressComponents.Province,
		"city":              addressComponents.City,
		"district":          addressComponents.District,
		"street":            addressComponents.Street,
		"street_number":     addressComponents.StreetNumber,
		"lng":               location.Lng,
		"lat":               location.Lat,
		"reliability":       8,
	}

	c.JSON(http.StatusOK, response)
}

// 搜索律师接口
func (w *WebService) searchLawyers(c *gin.Context) {
	keyword := c.Query("keyword")
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	var lawyers []model.Lawyer
	query := w.db.Model(&model.Lawyer{})

	if keyword != "" {
		// 尝试解析为数字ID
		if id, err := strconv.Atoi(keyword); err == nil {
			query = query.Where("name LIKE ? OR id = ? OR original_address LIKE ?",
				"%"+keyword+"%", id, "%"+keyword+"%")
		} else {
			query = query.Where("name LIKE ? OR original_address LIKE ?",
				"%"+keyword+"%", "%"+keyword+"%")
		}
	}

	if err := query.Limit(limit).Find(&lawyers).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, lawyers)
}

// 搜索法院接口
func (w *WebService) searchCourts(c *gin.Context) {
	keyword := c.Query("keyword")
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	var courts []model.Court
	query := w.db.Model(&model.Court{})

	if keyword != "" {
		// 尝试解析为数字ID
		if id, err := strconv.Atoi(keyword); err == nil {
			query = query.Where("name LIKE ? OR id = ? OR original_address LIKE ?",
				"%"+keyword+"%", id, "%"+keyword+"%")
		} else {
			query = query.Where("name LIKE ? OR original_address LIKE ?",
				"%"+keyword+"%", "%"+keyword+"%")
		}
	}

	if err := query.Limit(limit).Find(&courts).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, courts)
}

// 律师相关接口
func (w *WebService) getLawyers(c *gin.Context) {
	page, pageSize, search, status := w.getPageParams(c)

	var lawyers []model.Lawyer
	var total int64

	query := w.db.Model(&model.Lawyer{})

	if search != "" {
		// 搜索ID、姓名、电话、身份证、雇佣关系、职能、地址
		query = query.Where("id = ? OR name LIKE ? OR phone LIKE ? OR id_card LIKE ? OR employment LIKE ? OR ability LIKE ? OR original_address LIKE ? OR formatted_address LIKE ?",
			search, "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	if status != "" {
		statusInt, _ := strconv.Atoi(status)
		query = query.Where("status = ?", statusInt)
	}

	query.Count(&total)

	offset := (page - 1) * pageSize
	query.Order("status DESC, id ASC").Offset(offset).Limit(pageSize).Find(&lawyers)

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	response := PageResponse{
		Data:       lawyers,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}

	c.JSON(http.StatusOK, response)
}

func (w *WebService) getLawyer(c *gin.Context) {
	id := c.Param("id")
	var lawyer model.Lawyer

	if err := w.db.First(&lawyer, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "律师不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, lawyer)
}

func (w *WebService) createLawyer(c *gin.Context) {
	var lawyer model.Lawyer

	if err := c.ShouldBindJSON(&lawyer); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	lawyer.CreatedAt = time.Now()
	lawyer.UpdatedAt = time.Now()

	if err := w.db.Create(&lawyer).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, lawyer)
}

func (w *WebService) updateLawyer(c *gin.Context) {
	id := c.Param("id")
	var lawyer model.Lawyer

	if err := w.db.First(&lawyer, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "律师不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if err := c.ShouldBindJSON(&lawyer); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	lawyer.UpdatedAt = time.Now()

	if err := w.db.Save(&lawyer).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, lawyer)
}

func (w *WebService) deleteLawyer(c *gin.Context) {
	id := c.Param("id")

	if err := w.db.Delete(&model.Lawyer{}, id).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "删除成功"})
}

func (w *WebService) batchOperationLawyers(c *gin.Context) {
	var req struct {
		Operation string      `json:"operation"`
		IDs       []uint      `json:"ids"`
		Scope     string      `json:"scope"`
		Data      interface{} `json:"data"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	switch req.Operation {
	case "delete":
		if req.Scope == "all" {
			if err := w.db.Delete(&model.Lawyer{}, "1=1").Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
		} else {
			if err := w.db.Delete(&model.Lawyer{}, req.IDs).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{"message": "批量操作成功"})
}

// 法院相关接口 - 类似律师接口的实现
func (w *WebService) getCourts(c *gin.Context) {
	page, pageSize, search, status := w.getPageParams(c)

	var courts []model.Court
	var total int64

	query := w.db.Model(&model.Court{})

	if search != "" {
		// 搜索ID、名称、省份、城市、区县、地址
		query = query.Where("id = ? OR name LIKE ? OR province LIKE ? OR city LIKE ? OR district LIKE ? OR original_address LIKE ? OR formatted_address LIKE ?",
			search, "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	if status != "" {
		statusInt, _ := strconv.Atoi(status)
		query = query.Where("status = ?", statusInt)
	}

	query.Count(&total)

	offset := (page - 1) * pageSize
	query.Order("status DESC, id ASC").Offset(offset).Limit(pageSize).Find(&courts)

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	response := PageResponse{
		Data:       courts,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}

	c.JSON(http.StatusOK, response)
}

func (w *WebService) getCourt(c *gin.Context) {
	id := c.Param("id")
	var court model.Court

	if err := w.db.First(&court, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "法院不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, court)
}

func (w *WebService) createCourt(c *gin.Context) {
	var court model.Court

	if err := c.ShouldBindJSON(&court); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	court.CreatedAt = time.Now()
	court.UpdatedAt = time.Now()

	if err := w.db.Create(&court).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, court)
}

func (w *WebService) updateCourt(c *gin.Context) {
	id := c.Param("id")
	var court model.Court

	if err := w.db.First(&court, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "法院不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if err := c.ShouldBindJSON(&court); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	court.UpdatedAt = time.Now()

	if err := w.db.Save(&court).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, court)
}

func (w *WebService) deleteCourt(c *gin.Context) {
	id := c.Param("id")

	if err := w.db.Delete(&model.Court{}, id).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "删除成功"})
}

func (w *WebService) batchOperationCourts(c *gin.Context) {
	var req struct {
		Operation string      `json:"operation"`
		IDs       []uint      `json:"ids"`
		Scope     string      `json:"scope"`
		Data      interface{} `json:"data"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	switch req.Operation {
	case "delete":
		if req.Scope == "all" {
			if err := w.db.Delete(&model.Court{}, "1=1").Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
		} else {
			if err := w.db.Delete(&model.Court{}, req.IDs).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{"message": "批量操作成功"})
}

// 开庭相关接口
func (w *WebService) getTrials(c *gin.Context) {
	page, pageSize, search, status := w.getPageParams(c)

	var trials []model.Trial
	var total int64

	query := w.db.Model(&model.Trial{})

	if search != "" {
		// 搜索ID、案件ID、法院ID
		query = query.Where("id = ? OR case_id = ? OR court_id = ?",
			search, search, search)
	}

	if status != "" {
		statusInt, _ := strconv.Atoi(status)
		query = query.Where("status = ?", statusInt)
	}

	query.Count(&total)

	offset := (page - 1) * pageSize
	query.Order("status ASC, start_time ASC, id ASC").Offset(offset).Limit(pageSize).Find(&trials)

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	response := PageResponse{
		Data:       trials,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}

	c.JSON(http.StatusOK, response)
}

func (w *WebService) getTrial(c *gin.Context) {
	id := c.Param("id")
	var trial model.Trial

	if err := w.db.First(&trial, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "开庭信息不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, trial)
}

func (w *WebService) createTrial(c *gin.Context) {
	var trial model.Trial

	if err := c.ShouldBindJSON(&trial); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	trial.CreatedAt = time.Now()
	trial.UpdatedAt = time.Now()

	if err := w.db.Create(&trial).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, trial)
}

func (w *WebService) updateTrial(c *gin.Context) {
	id := c.Param("id")
	var trial model.Trial

	if err := w.db.First(&trial, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "开庭信息不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if err := c.ShouldBindJSON(&trial); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	trial.UpdatedAt = time.Now()

	if err := w.db.Save(&trial).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, trial)
}

func (w *WebService) deleteTrial(c *gin.Context) {
	id := c.Param("id")

	if err := w.db.Delete(&model.Trial{}, id).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "删除成功"})
}

func (w *WebService) batchOperationTrials(c *gin.Context) {
	var req struct {
		Operation string      `json:"operation"`
		IDs       []uint      `json:"ids"`
		Scope     string      `json:"scope"`
		Data      interface{} `json:"data"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	switch req.Operation {
	case "delete":
		if req.Scope == "all" {
			if err := w.db.Delete(&model.Trial{}, "1=1").Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
		} else {
			if err := w.db.Delete(&model.Trial{}, req.IDs).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{"message": "批量操作成功"})
}

// 路线扩展信息结构体
type RouteWithNames struct {
	model.Routes
	FromLawyerName string `json:"from_lawyer_name,omitempty"`
	FromCourtName  string `json:"from_court_name,omitempty"`
	ToLawyerName   string `json:"to_lawyer_name,omitempty"`
	ToCourtName    string `json:"to_court_name,omitempty"`
}

// 路线相关接口
func (w *WebService) getRoutes(c *gin.Context) {
	page, pageSize, search, status := w.getPageParams(c)
	sortField := c.Query("sort_field")
	sortOrder := c.Query("sort_order")
	routeType := c.Query("type")

	var routes []RouteWithNames
	var total int64

	// 构建复杂查询，包含律师和法院名称
	query := w.db.Table("routes").
		Select(`routes.*,
			CASE
				WHEN routes.type = 'lawyer_to_court' OR routes.type = 'court_to_lawyer'
				THEN l1.name
			END as from_lawyer_name,
			CASE
				WHEN routes.type = 'court_to_court' OR routes.type = 'lawyer_to_court'
				THEN c1.name
			END as from_court_name,
			CASE
				WHEN routes.type = 'court_to_lawyer'
				THEN l2.name
			END as to_lawyer_name,
			CASE
				WHEN routes.type = 'lawyer_to_court' OR routes.type = 'court_to_court'
				THEN c2.name
			END as to_court_name`).
		Joins("LEFT JOIN lawyers l1 ON routes.type IN ('lawyer_to_court', 'court_to_lawyer') AND routes.from_lat = l1.lat AND routes.from_lng = l1.lng").
		Joins("LEFT JOIN courts c1 ON routes.type IN ('court_to_court', 'lawyer_to_court') AND routes.from_lat = c1.lat AND routes.from_lng = c1.lng").
		Joins("LEFT JOIN lawyers l2 ON routes.type = 'court_to_lawyer' AND routes.to_lat = l2.lat AND routes.to_lng = l2.lng").
		Joins("LEFT JOIN courts c2 ON routes.type IN ('lawyer_to_court', 'court_to_court') AND routes.to_lat = c2.lat AND routes.to_lng = c2.lng")

	if search != "" {
		// 搜索ID、类型、出发地址、目标地址、律师名称、法院名称
		query = query.Where(`routes.id = ? OR routes.type LIKE ? OR routes.from_formatted_address LIKE ? OR routes.to_formatted_address LIKE ?
			OR l1.name LIKE ? OR c1.name LIKE ? OR l2.name LIKE ? OR c2.name LIKE ?`,
			search, "%"+search+"%", "%"+search+"%", "%"+search+"%",
			"%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	if status != "" {
		statusInt, _ := strconv.Atoi(status)
		query = query.Where("routes.status = ?", statusInt)
	}

	if routeType != "" {
		types := strings.Split(routeType, ",")
		if len(types) > 1 {
			query = query.Where("routes.type IN ?", types)
		} else {
			query = query.Where("routes.type = ?", routeType)
		}
	}

	query.Count(&total)

	// 处理排序
	orderBy := "routes.status DESC, routes.id ASC"
	if sortField != "" && (sortField == "distance" || sortField == "duration" || sortField == "cost") {
		if sortOrder == "desc" {
			orderBy = fmt.Sprintf("routes.%s DESC", sortField)
		} else {
			orderBy = fmt.Sprintf("routes.%s ASC", sortField)
		}
	}

	offset := (page - 1) * pageSize
	query.Order(orderBy).Offset(offset).Limit(pageSize).Find(&routes)

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	response := PageResponse{
		Data:       routes,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}

	c.JSON(http.StatusOK, response)
}

func (w *WebService) getRoute(c *gin.Context) {
	id := c.Param("id")
	var route model.Routes

	if err := w.db.First(&route, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "路线不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, route)
}

func (w *WebService) createRoute(c *gin.Context) {
	var route model.Routes

	if err := c.ShouldBindJSON(&route); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	route.CreatedAt = time.Now()
	route.UpdatedAt = time.Now()

	if err := w.db.Create(&route).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, route)
}

func (w *WebService) updateRoute(c *gin.Context) {
	id := c.Param("id")
	var route model.Routes

	if err := w.db.First(&route, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "路线不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if err := c.ShouldBindJSON(&route); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	route.UpdatedAt = time.Now()

	if err := w.db.Save(&route).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, route)
}

func (w *WebService) deleteRoute(c *gin.Context) {
	id := c.Param("id")

	if err := w.db.Delete(&model.Routes{}, id).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "删除成功"})
}

func (w *WebService) batchOperationRoutes(c *gin.Context) {
	var req struct {
		Operation string      `json:"operation"`
		IDs       []uint      `json:"ids"`
		Scope     string      `json:"scope"`
		Data      interface{} `json:"data"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	switch req.Operation {
	case "delete":
		if req.Scope == "all" {
			if err := w.db.Delete(&model.Routes{}, "1=1").Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
		} else {
			if err := w.db.Delete(&model.Routes{}, req.IDs).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{"message": "批量操作成功"})
}

// 遗传算法调度接口相关方法

// GeneticScheduleRequest 遗传算法调度请求
type GeneticScheduleRequest struct {
	Configs   []GeneticConfig `json:"configs"`
	StartDate string          `json:"start_date"`
	EndDate   string          `json:"end_date"`
}

// GeneticConfig 遗传算法配置
type GeneticConfig struct {
	Name           string  `json:"name"`
	DistWeight     float64 `json:"dist_weight"`
	CostWeight     float64 `json:"cost_weight"`
	TimeWeight     float64 `json:"time_weight"`
	WorkloadWeight float64 `json:"workload_weight"`
}

// GeneticScheduleResponse 遗传算法调度响应
type GeneticScheduleResponse struct {
	Solutions []GeneticSolution `json:"solutions"`
}

// GeneticSolution 遗传算法解决方案
type GeneticSolution struct {
	ConfigName      string           `json:"config_name"`
	MatchCount      int              `json:"match_count"`
	TotalCost       float64          `json:"total_cost"`
	TotalDistance   int              `json:"total_distance"`
	TotalDuration   int              `json:"total_duration"`
	Matching        map[uint]uint    `json:"matching"`
	LawyerSchedules []LawyerSchedule `json:"lawyer_schedules"`
}

// LawyerSchedule 律师调度信息
type LawyerSchedule struct {
	LawyerID      uint          `json:"lawyer_id"`
	LawyerName    string        `json:"lawyer_name"`
	LawyerAddress string        `json:"lawyer_address"`
	TrialCount    int           `json:"trial_count"`
	TrialDetails  []TrialDetail `json:"trial_details"`
}

// TrialDetail 开庭详情
type TrialDetail struct {
	TrialID      uint      `json:"trial_id"`
	CaseID       uint      `json:"case_id"`
	CourtID      uint      `json:"court_id"`
	CourtName    string    `json:"court_name"`
	CourtAddress string    `json:"court_address"`
	StartTime    time.Time `json:"start_time"`
	EndTime      time.Time `json:"end_time"`
	Distance     int       `json:"distance"`
	Duration     int       `json:"duration"`
	TravelStart  time.Time `json:"travel_start"`
	TravelEnd    time.Time `json:"travel_end"`
}

// getGeneticSchedule 获取遗传算法调度结果
func (w *WebService) getGeneticSchedule(c *gin.Context) {
	// 获取调度结果（这里可以实现缓存机制）
	c.JSON(http.StatusOK, gin.H{
		"message": "请先运行遗传算法调度",
		"data":    nil,
	})
}

// runGeneticSchedule 运行遗传算法调度
func (w *WebService) runGeneticSchedule(c *gin.Context) {
	var req GeneticScheduleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误: " + err.Error()})
		return
	}

	fmt.Printf("Received genetic schedule request: %+v\n", req)

	// 获取数据
	var lawyers []model.Lawyer
	var courts []model.Court
	var trials []model.Trial
	var routes []model.Routes

	if err := w.db.Where("status = ?", 1).Find(&lawyers).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取律师数据失败: " + err.Error()})
		return
	}

	if err := w.db.Where("status = ?", 1).Find(&courts).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取法院数据失败: " + err.Error()})
		return
	}

	// 修改查询条件，只获取指定时间范围内的开庭数据
	now := time.Now()
	trialQuery := w.db.Where("status IN ? AND start_time >= ?", []int{1, 2, 4}, now)
	if req.StartDate != "" && req.EndDate != "" {
		start, err1 := time.Parse("2006-01-02", req.StartDate)
		end, err2 := time.Parse("2006-01-02", req.EndDate)
		if err1 == nil && err2 == nil {
			end = end.Add(24 * time.Hour) // 包含结束当天
			trialQuery = w.db.Where("status IN ? AND start_time >= ? AND start_time < ?", []int{1, 2, 4}, start, end)
		}
	}
	if err := trialQuery.Find(&trials).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取开庭数据失败: " + err.Error()})
		return
	}

	// 查询路线数据 - 修改为只获取律师到法院的路线，忽略时间字段
	if err := w.db.Where("status = ? AND type = ?", 1, "lawyer_to_court").Find(&routes).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取路线数据失败: " + err.Error()})
		return
	}

	fmt.Printf("Data counts - Lawyers: %d, Courts: %d, Trials: %d, Routes: %d\n",
		len(lawyers), len(courts), len(trials), len(routes))

	// 检查是否有足够的数据
	if len(lawyers) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "没有可用的律师数据"})
		return
	}
	if len(courts) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "没有可用的法院数据"})
		return
	}
	if len(trials) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "没有可用的开庭数据"})
		return
	}
	if len(routes) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "没有可用的路线数据"})
		return
	}

	// 创建遗传算法调度器
	mapModel := imap.NewMapModel("tencent")
	scheduler := schedule.NewGeneticAlgorithmScheduler(mapModel, lawyers, courts, trials, routes)

	// 构建权重配置
	var configs []schedule.GAWeightConfig
	if len(req.Configs) > 0 {
		configs = make([]schedule.GAWeightConfig, len(req.Configs))
		for i, config := range req.Configs {
			configs[i] = schedule.GAWeightConfig{
				Name:          config.Name,
				DistWeight:    config.DistWeight,
				TimeWeight:    config.TimeWeight,
				BalanceWeight: config.WorkloadWeight,
			}
		}
		fmt.Printf("Using custom configs: %+v\n", configs)
	} else {
		fmt.Println("Using default configs")
	}

	// 运行调度 - 使用类型断言来访问具体方法
	var results map[uint][]map[uint]uint
	var gaScheduler *schedule.GeneticAlgorithmScheduler
	if scheduler, ok := scheduler.(*schedule.GeneticAlgorithmScheduler); ok {
		gaScheduler = scheduler
		if len(configs) > 0 {
			results = gaScheduler.ScheduleWithCustomWeights(configs)
		} else {
			results = gaScheduler.Schedule()
		}
	} else {
		results = scheduler.Schedule()
	}

	fmt.Printf("Schedule results: %+v\n", results)

	// 转换结果格式
	solutions := w.convertGeneticResults(results, lawyers, courts, trials, routes, gaScheduler)

	fmt.Printf("Converted solutions count: %d\n", len(solutions))

	c.JSON(http.StatusOK, GeneticScheduleResponse{
		Solutions: solutions,
	})
}

// getGeneticConfigs 获取遗传算法配置选项
func (w *WebService) getGeneticConfigs(c *gin.Context) {
	defaultConfigs := []GeneticConfig{
		{"距离优先", 10.0, 1.0, 1.0, 0.1},
		{"时间优先", 1.0, 1.0, 10.0, 0.1},
		{"负载均衡", 2.0, 2.0, 2.0, 10.0},
		{"均衡优化", 5.0, 5.0, 5.0, 1.0},
		{"远距离惩罚", 20.0, 2.0, 2.0, 0.5},
		{"快速到达", 2.0, 1.0, 15.0, 0.5},
		{"工作量平衡", 3.0, 3.0, 3.0, 8.0},
		{"距离敏感", 15.0, 3.0, 3.0, 1.0},
		{"时间敏感", 3.0, 1.0, 12.0, 1.0},
		{"综合平衡", 6.0, 6.0, 6.0, 3.0},
	}

	c.JSON(http.StatusOK, gin.H{
		"configs": defaultConfigs,
	})
}

// convertGeneticResults 转换遗传算法结果格式
func (w *WebService) convertGeneticResults(results map[uint][]map[uint]uint, lawyers []model.Lawyer, courts []model.Court, trials []model.Trial, routes []model.Routes, gaScheduler *schedule.GeneticAlgorithmScheduler) []GeneticSolution {
	var solutions []GeneticSolution

	// 创建映射表
	lawyerMap := make(map[uint]*model.Lawyer)
	for i := range lawyers {
		lawyerMap[lawyers[i].ID] = &lawyers[i]
	}

	courtMap := make(map[uint]*model.Court)
	for i := range courts {
		courtMap[courts[i].ID] = &courts[i]
	}

	trialMap := make(map[uint]*model.Trial)
	for i := range trials {
		trialMap[trials[i].ID] = &trials[i]
	}

	routeMap := make(map[string]*model.Routes)
	for i := range routes {
		key := fmt.Sprintf("%f:%f:%f:%f", routes[i].FromLat, routes[i].FromLng, routes[i].ToLat, routes[i].ToLng)
		routeMap[key] = &routes[i]
	}

	// 遍历所有匹配数量的结果
	for matchCount, schedules := range results {
		for i, schedule := range schedules {
			// 尝试从配置名称中获取方案名称
			configName := fmt.Sprintf("方案%d", i+1)

			// 从遗传算法调度器的完整解决方案中获取费用信息
			var totalCost float64
			var totalDistance, totalDuration int
			if gaScheduler != nil && i < len(gaScheduler.GetLastCalculatedSolutions()) {
				solution := gaScheduler.GetLastCalculatedSolutions()[i]
				totalCost = solution.TotalCost
				totalDistance = solution.TotalDistance
				totalDuration = solution.TotalDuration
				configName = solution.ConfigName
			}

			solution := GeneticSolution{
				ConfigName:      configName,
				MatchCount:      int(matchCount),
				TotalCost:       totalCost,
				TotalDistance:   totalDistance,
				TotalDuration:   totalDuration,
				Matching:        schedule,
				LawyerSchedules: []LawyerSchedule{},
			}

			// 按律师分组
			lawyerTrials := make(map[uint][]uint)
			for trialID, lawyerID := range schedule {
				lawyerTrials[lawyerID] = append(lawyerTrials[lawyerID], trialID)
			}

			// 构建律师调度信息
			for lawyerID, trialIDs := range lawyerTrials {
				lawyer := lawyerMap[lawyerID]
				if lawyer == nil {
					continue
				}

				// 处理律师地址，如果FormattedAddress为空，使用OriginalAddress
				lawyerAddress := lawyer.FormattedAddress
				if lawyerAddress == "" {
					lawyerAddress = lawyer.OriginalAddress
				}
				if lawyerAddress == "" {
					lawyerAddress = "地址信息缺失"
				}

				fmt.Printf("律师 %s (ID: %d) 地址信息: FormattedAddress='%s', OriginalAddress='%s', 最终地址='%s'\n",
					lawyer.Name, lawyer.ID, lawyer.FormattedAddress, lawyer.OriginalAddress, lawyerAddress)

				lawyerSchedule := LawyerSchedule{
					LawyerID:      lawyerID,
					LawyerName:    lawyer.Name,
					LawyerAddress: lawyerAddress,
					TrialCount:    len(trialIDs),
					TrialDetails:  []TrialDetail{},
				}

				// 构建开庭详情
				for _, trialID := range trialIDs {
					trial := trialMap[trialID]
					if trial == nil {
						continue
					}

					court := courtMap[trial.CourtID]
					if court == nil {
						continue
					}

					// 查找路线信息 - 使用精确匹配（忽略时间字段）
					var route *model.Routes
					for _, r := range routes {
						if r.Type == "lawyer_to_court" &&
							fmt.Sprintf("%.6f", r.FromLat) == fmt.Sprintf("%.6f", lawyer.Lat) &&
							fmt.Sprintf("%.6f", r.FromLng) == fmt.Sprintf("%.6f", lawyer.Lng) &&
							fmt.Sprintf("%.6f", r.ToLat) == fmt.Sprintf("%.6f", court.Lat) &&
							fmt.Sprintf("%.6f", r.ToLng) == fmt.Sprintf("%.6f", court.Lng) &&
							r.Status == 1 { // 只选择状态为1的可用路径
							route = &r
							break
						}
					}

					trialDetail := TrialDetail{
						TrialID:      trialID,
						CaseID:       trial.CaseID,
						CourtID:      trial.CourtID,
						CourtName:    court.Name,
						CourtAddress: court.FormattedAddress,
						StartTime:    trial.StartTime,
						EndTime:      trial.EndTime,
					}

					if route != nil {
						trialDetail.Distance = route.Distance * 2 // 往返距离
						trialDetail.Duration = route.Duration * 2 // 往返时间

						// 计算出行时间 - route.Duration单位是分钟，需要转换为时间
						travelDurationMinutes := time.Duration(float64(route.Duration) * 1.5) // 1.5倍缓冲（与调度算法一致）
						trialDetail.TravelStart = trial.StartTime.Add(-travelDurationMinutes * time.Minute)
						trialDetail.TravelEnd = trial.EndTime.Add(travelDurationMinutes * time.Minute)

						solution.TotalDistance += route.Distance * 2
						solution.TotalDuration += route.Duration * 2
					}

					lawyerSchedule.TrialDetails = append(lawyerSchedule.TrialDetails, trialDetail)
				}

				solution.LawyerSchedules = append(solution.LawyerSchedules, lawyerSchedule)
			}

			solutions = append(solutions, solution)
		}
	}

	return solutions
}
