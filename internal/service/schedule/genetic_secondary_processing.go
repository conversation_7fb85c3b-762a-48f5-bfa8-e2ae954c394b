package schedule

import (
	"external-tool/internal/model"
	"external-tool/internal/model/imap"
	"fmt"
	"sort"
	"time"
)

// SecondaryProcessor 二次处理器
type SecondaryProcessor struct {
	mapModel  imap.MapModel
	lawyerMap map[uint]*model.Lawyer
	courtMap  map[uint]*model.Court
	trialMap  map[uint]*model.Trial
}

// NewSecondaryProcessor 创建二次处理器
func NewSecondaryProcessor(mapModel imap.MapModel, lawyerMap map[uint]*model.Lawyer, courtMap map[uint]*model.Court, trialMap map[uint]*model.Trial) *SecondaryProcessor {
	return &SecondaryProcessor{
		mapModel:  mapModel,
		lawyerMap: lawyerMap,
		courtMap:  courtMap,
		trialMap:  trialMap,
	}
}

// ProcessingSolution 处理中的解决方案
type ProcessingSolution struct {
	OriginalMatching map[uint]uint             // 原始匹配
	UpdatedMatching  map[uint]uint             // 更新后的匹配
	RealTimeRoutes   map[string]*RealTimeRoute // 实时路径信息
	IsValid          bool                      // 是否有效
	TotalDistance    int                       // 总距离
	TotalDuration    int                       // 总时间
	TotalCost        float64                   // 总费用
}

// RealTimeRoute 实时路径信息
type RealTimeRoute struct {
	Distance int
	Duration int
	Price    int
	Remark   string
}

// ProcessSolutionsWithRealTimeCalculation 对解决方案进行实时计算处理
func (sp *SecondaryProcessor) ProcessSolutionsWithRealTimeCalculation(solutions []GAScheduleSolution) []GAScheduleSolution {
	fmt.Printf("=== 开始二次处理 ===\n")
	fmt.Printf("输入解决方案数量: %d\n", len(solutions))

	var processedSolutions []ProcessingSolution

	// 第一阶段：对每个解决方案进行实时路径计算
	for i, solution := range solutions {
		fmt.Printf("处理解决方案 %d/%d\n", i+1, len(solutions))

		processed := sp.processIndividualSolution(solution)
		if processed.IsValid {
			processedSolutions = append(processedSolutions, processed)
		}
	}

	fmt.Printf("有效解决方案数量: %d\n", len(processedSolutions))

	// 第二阶段：使用贪心算法选出最佳匹配
	finalSolutions := sp.greedySelectBestMatches(processedSolutions)

	fmt.Printf("最终解决方案数量: %d\n", len(finalSolutions))

	return finalSolutions
}

// processIndividualSolution 处理单个解决方案
func (sp *SecondaryProcessor) processIndividualSolution(solution GAScheduleSolution) ProcessingSolution {
	processed := ProcessingSolution{
		OriginalMatching: solution.Matching,
		UpdatedMatching:  make(map[uint]uint),
		RealTimeRoutes:   make(map[string]*RealTimeRoute),
		IsValid:          true,
	}

	// 验证每个匹配的时间冲突
	for trialID, lawyerID := range solution.Matching {
		trial := sp.trialMap[trialID]
		lawyer := sp.lawyerMap[lawyerID]
		court := sp.courtMap[trial.CourtID]

		if trial == nil || lawyer == nil || court == nil {
			processed.IsValid = false
			break
		}

		// 获取实时路径信息
		routeKey := sp.getRouteKey(lawyer.GetLocation(), court.GetLocation())
		if _, exists := processed.RealTimeRoutes[routeKey]; !exists {
			realTimeRoute := sp.calculateRealTimeRoute(lawyer.GetLocation(), court.GetLocation(), trial.StartTime)
			processed.RealTimeRoutes[routeKey] = realTimeRoute
		}

		route := processed.RealTimeRoutes[routeKey]
		if route.Distance == 0 {
			processed.IsValid = false
			break
		}

		// 暂时添加到更新后的匹配中
		processed.UpdatedMatching[trialID] = lawyerID
	}

	// 如果基本验证通过，进行时间冲突检查
	if processed.IsValid {
		processed.UpdatedMatching = sp.resolveTimeConflicts(processed.UpdatedMatching, processed.RealTimeRoutes)
		processed.calculateMetrics()
	}

	return processed
}

// calculateRealTimeRoute 计算实时路径
func (sp *SecondaryProcessor) calculateRealTimeRoute(from, to *model.Location, trialStartTime time.Time) *RealTimeRoute {
	// 判断是否在同一个城市
	if sp.isSameCity(from, to) {
		// 同城市，返回预设的标准路径信息
		return &RealTimeRoute{
			Distance: 10000, // 10km
			Duration: 60,    // 1小时
			Price:    50,    // 50元
			Remark:   "同城市内交通",
		}
	}

	// 不同城市，尝试实时计算
	maxAttempts := 3
	for attempt := 0; attempt < maxAttempts; attempt++ {
		queryTime := trialStartTime.Add(time.Duration(-attempt-2) * time.Hour)

		routes, remark, err := sp.mapModel.DirectionTransitV2(from, to, "RECOMMEND", queryTime)
		if err != nil {
			continue
		}

		if len(routes) > 0 && routes[0].Distance > 0 {
			return &RealTimeRoute{
				Distance: routes[0].Distance,
				Duration: routes[0].Duration,
				Price:    routes[0].Price,
				Remark:   remark,
			}
		}
	}

	// 如果实时计算失败，返回空路径
	return &RealTimeRoute{
		Distance: 0,
		Duration: 0,
		Price:    0,
		Remark:   "无法计算路径",
	}
}

// isSameCity 判断两个位置是否在同一个城市
func (sp *SecondaryProcessor) isSameCity(loc1, loc2 *model.Location) bool {
	addr1 := loc1.FormattedAddress
	addr2 := loc2.FormattedAddress

	if addr1 == "" || addr2 == "" {
		return false
	}

	if len(addr1) < 6 || len(addr2) < 6 {
		return false
	}

	city1 := addr1[:6]
	city2 := addr2[:6]

	return city1 == city2
}

// resolveTimeConflicts 解决时间冲突
func (sp *SecondaryProcessor) resolveTimeConflicts(matching map[uint]uint, realTimeRoutes map[string]*RealTimeRoute) map[uint]uint {
	// 按律师分组
	lawyerTrials := make(map[uint][]uint)
	for trialID, lawyerID := range matching {
		lawyerTrials[lawyerID] = append(lawyerTrials[lawyerID], trialID)
	}

	finalMatching := make(map[uint]uint)

	// 对每个律师的案件进行冲突检查
	for lawyerID, trials := range lawyerTrials {
		if len(trials) <= 1 {
			// 单个案件无冲突
			for _, trialID := range trials {
				finalMatching[trialID] = lawyerID
			}
			continue
		}

		// 多个案件需要检查冲突
		validTrials := sp.selectNonConflictingTrials(lawyerID, trials, realTimeRoutes)
		for _, trialID := range validTrials {
			finalMatching[trialID] = lawyerID
		}
	}

	return finalMatching
}

// selectNonConflictingTrials 选择无冲突的案件
func (sp *SecondaryProcessor) selectNonConflictingTrials(lawyerID uint, trials []uint, realTimeRoutes map[string]*RealTimeRoute) []uint {
	if len(trials) <= 1 {
		return trials
	}

	// 构建案件时间窗口信息
	type trialTimeWindow struct {
		trialID   uint
		startTime time.Time
		endTime   time.Time
		priority  float64 // 优先级（可以基于费用、距离等）
	}

	var windows []trialTimeWindow
	lawyer := sp.lawyerMap[lawyerID]

	for _, trialID := range trials {
		trial := sp.trialMap[trialID]
		court := sp.courtMap[trial.CourtID]

		routeKey := sp.getRouteKey(lawyer.GetLocation(), court.GetLocation())
		route, exists := realTimeRoutes[routeKey]
		if !exists || route.Distance == 0 {
			continue
		}

		// 计算时间窗口
		travelDuration := time.Duration(float64(route.Duration) * Buffer)
		startTime := trial.StartTime.Add(-travelDuration * time.Minute)
		endTime := trial.EndTime.Add(travelDuration * time.Minute)

		// 计算优先级（距离越近，费用越低，优先级越高）
		priority := 1.0 / (float64(route.Distance) + float64(route.Price)*100)

		windows = append(windows, trialTimeWindow{
			trialID:   trialID,
			startTime: startTime,
			endTime:   endTime,
			priority:  priority,
		})
	}

	// 按优先级排序
	sort.Slice(windows, func(i, j int) bool {
		return windows[i].priority > windows[j].priority
	})

	// 贪心选择无冲突的案件
	var selected []uint
	var selectedWindows []trialTimeWindow

	for _, window := range windows {
		hasConflict := false
		for _, selectedWindow := range selectedWindows {
			if sp.isTimeOverlap(window.startTime, window.endTime, selectedWindow.startTime, selectedWindow.endTime) {
				hasConflict = true
				break
			}
		}

		if !hasConflict {
			selected = append(selected, window.trialID)
			selectedWindows = append(selectedWindows, window)
		}
	}

	return selected
}

// isTimeOverlap 检查时间重叠
func (sp *SecondaryProcessor) isTimeOverlap(start1, end1, start2, end2 time.Time) bool {
	return start1.Before(end2) && end1.After(start2)
}

// calculateMetrics 计算指标
func (sp *ProcessingSolution) calculateMetrics() {
	sp.TotalDistance = 0
	sp.TotalDuration = 0
	sp.TotalCost = 0

	for _, route := range sp.RealTimeRoutes {
		// 计算使用此路径的次数
		count := 0
		for trialID, lawyerID := range sp.UpdatedMatching {
			// 这里需要重新构建路径键来匹配
			// 简化处理，直接累加所有路径的费用
			_ = trialID
			_ = lawyerID
			count = 1 // 简化处理
		}

		sp.TotalDistance += route.Distance * count
		sp.TotalDuration += route.Duration * count
		sp.TotalCost += float64(route.Price) * float64(count)
	}
}

// greedySelectBestMatches 贪心选择最佳匹配
func (sp *SecondaryProcessor) greedySelectBestMatches(processedSolutions []ProcessingSolution) []GAScheduleSolution {
	if len(processedSolutions) == 0 {
		return []GAScheduleSolution{}
	}

	// 按匹配数量和总费用排序
	sort.Slice(processedSolutions, func(i, j int) bool {
		if len(processedSolutions[i].UpdatedMatching) != len(processedSolutions[j].UpdatedMatching) {
			return len(processedSolutions[i].UpdatedMatching) > len(processedSolutions[j].UpdatedMatching)
		}
		return processedSolutions[i].TotalCost < processedSolutions[j].TotalCost
	})

	// 转换为GAScheduleSolution格式
	var finalSolutions []GAScheduleSolution
	maxSolutions := 10
	if len(processedSolutions) < maxSolutions {
		maxSolutions = len(processedSolutions)
	}

	for i := 0; i < maxSolutions; i++ {
		processed := processedSolutions[i]
		solution := GAScheduleSolution{
			Matching:      processed.UpdatedMatching,
			MatchCount:    len(processed.UpdatedMatching),
			TotalCost:     processed.TotalCost,
			TotalDistance: processed.TotalDistance,
			TotalDuration: processed.TotalDuration,
			ConfigName:    fmt.Sprintf("二次处理方案-%d", i+1),
		}
		finalSolutions = append(finalSolutions, solution)
	}

	return finalSolutions
}

// getRouteKey 生成路径键
func (sp *SecondaryProcessor) getRouteKey(from, to *model.Location) string {
	return fmt.Sprintf("%s:%s", from.String(), to.String())
}
