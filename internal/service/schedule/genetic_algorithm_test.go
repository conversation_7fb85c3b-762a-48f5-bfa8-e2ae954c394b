package schedule

import (
	"external-tool/internal/config"
	"external-tool/internal/model"
	"external-tool/internal/model/imap"
	"fmt"
	"testing"
	"time"
)

func newGeneticAlgorithmScheduler() BestSchedule {
	db := config.GetDB()
	mapModel := imap.NewMapModel("tencent")

	var lawyers []model.Lawyer
	db.Model(&model.Lawyer{}).Where("lat != 0 AND lng != 0 AND status = 1 AND formatted_address <> '广东省深圳市福田区深南大道6025'").Order("id ASC").Find(&lawyers)
	var courts []model.Court
	db.Model(&model.Court{}).Where("lat != 0 AND lng != 0 AND status = 1").Order("id ASC").Find(&courts)
	var trials []model.Trial
	db.Model(&model.Trial{}).Where("status = 1").Order("id ASC").Find(&trials)
	var routes []model.Routes
	db.Model(&model.Routes{}).Where("status = 1").Order("id ASC").Find(&routes)

	scheduler := NewGeneticAlgorithmScheduler(mapModel, lawyers, courts, trials, routes)
	return scheduler
}

func TestGeneticAlgorithmScheduler(t *testing.T) {
	scheduler := newGeneticAlgorithmScheduler().(*GeneticAlgorithmScheduler)

	// 输出 NewGeneticAlgorithmScheduler 之后的值
	fmt.Printf("NewGeneticAlgorithmScheduler 返回的对象类型: %T\n", scheduler)

	// 尝试转换为具体类型以便查看内部结构
	fmt.Printf("GeneticAlgorithmScheduler 结构体内容:\n")
	fmt.Printf("  mapModel: %T\n", scheduler.mapModel)
	fmt.Printf("  lawyerMap 长度: %d\n", len(scheduler.lawyerMap))
	fmt.Printf("  courtMap 长度: %d\n", len(scheduler.courtMap))
	fmt.Printf("  trialMap 长度: %d\n", len(scheduler.trialMap))
	fmt.Printf("  routeMap 长度: %d\n", len(scheduler.routeMap))
	fmt.Printf("  trialsList 长度: %d\n", len(scheduler.trialsList))
	fmt.Printf("  lawyersList 长度: %d\n", len(scheduler.lawyersList))

	// 显示GA参数
	fmt.Printf("  GA参数:\n")
	fmt.Printf("    populationSize: %d\n", scheduler.populationSize)
	fmt.Printf("    generations: %d\n", scheduler.generations)
	fmt.Printf("    mutationRate: %.2f\n", scheduler.mutationRate)
	fmt.Printf("    crossoverRate: %.2f\n", scheduler.crossoverRate)
	fmt.Printf("    maxCapacityPerLawyer: %d\n", scheduler.maxCapacityPerLawyer)

	result := scheduler.Schedule()
	fmt.Printf("GeneticAlgorithmScheduler Schedule 结果: %v\n", result)

	// 输出详细的匹配数量分布
	fmt.Printf("\n遗传算法匹配数量分布:\n")
	for matchCount := uint(0); matchCount <= 10; matchCount++ {
		if schedules, exists := result[matchCount]; exists {
			fmt.Printf("  匹配数量 %d: %d 个方案\n", matchCount, len(schedules))
		}
	}
}

func TestGeneticAlgorithmSchedulerWithCustomWeights(t *testing.T) {
	scheduler := newGeneticAlgorithmScheduler().(*GeneticAlgorithmScheduler)

	// 测试自定义权重配置
	customConfigs := []GAWeightConfig{
		{"测试距离优先", 20.0, 1.0, 1.0, 0.1},
		{"测试时间优先", 1.0, 1.0, 20.0, 0.1},
		{"测试负载均衡", 5.0, 5.0, 5.0, 20.0},
	}

	fmt.Printf("===== 测试自定义权重配置 =====\n")
	result := scheduler.ScheduleWithCustomWeights(customConfigs)
	fmt.Printf("自定义权重配置结果: %v\n", result)

	// 输出详细的匹配数量分布
	fmt.Printf("\n自定义权重匹配数量分布:\n")
	for matchCount := uint(0); matchCount <= 10; matchCount++ {
		if schedules, exists := result[matchCount]; exists {
			fmt.Printf("  匹配数量 %d: %d 个方案\n", matchCount, len(schedules))
		}
	}
}

func TestGeneticAlgorithmSchedulerParameters(t *testing.T) {
	scheduler := newGeneticAlgorithmScheduler().(*GeneticAlgorithmScheduler)

	// 测试设置最大容量
	fmt.Printf("===== 测试设置最大容量 =====\n")
	scheduler.SetMaxCapacityPerLawyer(3)
	fmt.Printf("设置最大容量为3后，maxCapacityPerLawyer: %d\n", scheduler.maxCapacityPerLawyer)

	// 测试参数验证功能
	fmt.Printf("\n===== 测试参数验证 =====\n")
	
	// 测试无效参数
	err := scheduler.SetGAParameters(-10, 50, 0.1, 0.7)
	if err != nil {
		fmt.Printf("✅ 正确捕获无效种群大小错误: %v\n", err)
	} else {
		t.Error("应该捕获无效种群大小错误")
	}
	
	err = scheduler.SetGAParameters(50, -10, 0.1, 0.7)
	if err != nil {
		fmt.Printf("✅ 正确捕获无效进化代数错误: %v\n", err)
	} else {
		t.Error("应该捕获无效进化代数错误")
	}
	
	err = scheduler.SetGAParameters(50, 20, 1.5, 0.7)
	if err != nil {
		fmt.Printf("✅ 正确捕获无效变异率错误: %v\n", err)
	} else {
		t.Error("应该捕获无效变异率错误")
	}

	// 测试有效参数设置
	err = scheduler.SetGAParameters(50, 20, 0.2, 0.8)
	if err != nil {
		t.Errorf("设置有效参数时出错: %v", err)
	} else {
		fmt.Printf("✅ 成功设置有效参数\n")
	}

	fmt.Printf("调整后的GA参数:\n")
	fmt.Printf("  populationSize: %d\n", scheduler.populationSize)
	fmt.Printf("  generations: %d\n", scheduler.generations)
	fmt.Printf("  mutationRate: %.2f\n", scheduler.mutationRate)
	fmt.Printf("  crossoverRate: %.2f\n", scheduler.crossoverRate)

	result := scheduler.Schedule()
	fmt.Printf("调整参数后的Schedule结果: %v\n", result)
}

func TestGeneticAlgorithmSchedulerPerformance(t *testing.T) {
	scheduler := newGeneticAlgorithmScheduler().(*GeneticAlgorithmScheduler)

	// 性能测试：使用较小的参数
	scheduler.populationSize = 20
	scheduler.generations = 10

	fmt.Printf("===== 性能测试 =====\n")

	// 单一权重配置测试
	config := []GAWeightConfig{
		{"性能测试", 5.0, 5.0, 5.0, 1.0},
	}

	result := scheduler.ScheduleWithCustomWeights(config)
	fmt.Printf("性能测试结果: %v\n", result)

	// 统计结果
	totalSolutions := 0
	for _, schedules := range result {
		totalSolutions += len(schedules)
	}
	fmt.Printf("总方案数: %d\n", totalSolutions)
}

// TestNoTimeConflicts 测试时间冲突检查机制
func TestNoTimeConflicts(t *testing.T) {
	fmt.Println("=== 测试时间冲突检查机制 ===")

	// 创建测试数据
	lawyers := []model.Lawyer{
		{ID: 1, Name: "律师1", Lat: 39.9042, Lng: 116.4074, FormattedAddress: "北京市朝阳区"},
		{ID: 2, Name: "律师2", Lat: 39.9042, Lng: 116.4074, FormattedAddress: "北京市朝阳区"},
	}

	courts := []model.Court{
		{ID: 1, Name: "法院1", Lat: 39.9042, Lng: 116.4074, FormattedAddress: "北京市朝阳区"},
		{ID: 2, Name: "法院2", Lat: 39.9042, Lng: 116.4074, FormattedAddress: "北京市朝阳区"},
	}

	// 创建时间冲突的案件
	baseTime := time.Date(2024, 1, 1, 9, 0, 0, 0, time.UTC)
	trials := []model.Trial{
		{ID: 1, CourtID: 1, StartTime: baseTime, EndTime: baseTime.Add(time.Hour)},
		{ID: 2, CourtID: 2, StartTime: baseTime.Add(30 * time.Minute), EndTime: baseTime.Add(90 * time.Minute)}, // 时间冲突
		{ID: 3, CourtID: 1, StartTime: baseTime.Add(3 * time.Hour), EndTime: baseTime.Add(4 * time.Hour)},       // 无冲突
	}

	// 创建路径数据
	routes := []model.Routes{
		{
			Type:    "lawyer_to_court",
			FromLat: 39.9042, FromLng: 116.4074, FromFormattedAddress: "北京市朝阳区",
			ToLat: 39.9042, ToLng: 116.4074, ToFormattedAddress: "北京市朝阳区",
			Distance: 1000, Duration: 600, Cost: 50, // 10分钟路程
		},
		{
			Type:    "lawyer_to_court",
			FromLat: 39.9042, FromLng: 116.4074, FromFormattedAddress: "北京市朝阳区",
			ToLat: 39.9042, ToLng: 116.4074, ToFormattedAddress: "北京市朝阳区",
			Distance: 1000, Duration: 600, Cost: 50, // 10分钟路程
		},
		{
			Type:    "lawyer_to_court",
			FromLat: 39.9042, FromLng: 116.4074, FromFormattedAddress: "北京市朝阳区",
			ToLat: 39.9042, ToLng: 116.4074, ToFormattedAddress: "北京市朝阳区",
			Distance: 1000, Duration: 600, Cost: 50, // 10分钟路程
		},
		{
			Type:    "lawyer_to_court",
			FromLat: 39.9042, FromLng: 116.4074, FromFormattedAddress: "北京市朝阳区",
			ToLat: 39.9042, ToLng: 116.4074, ToFormattedAddress: "北京市朝阳区",
			Distance: 1000, Duration: 600, Cost: 50, // 10分钟路程
		},
	}

	// 创建调度器
	scheduler := NewGeneticAlgorithmScheduler(nil, lawyers, courts, trials, routes).(*GeneticAlgorithmScheduler)

	// 测试1: 测试时间冲突检查
	fmt.Println("\n--- 测试1: 基本时间冲突检查 ---")
	conflictMatch := map[uint]uint{
		1: 1, // 律师1 处理案件1 (9:00-10:00)
		2: 1, // 律师1 处理案件2 (9:30-10:30) - 应该冲突
	}

	isValid := scheduler.validateNoTimeConflicts(conflictMatch)
	fmt.Printf("冲突方案验证结果: %v (预期: false)\n", isValid)
	if isValid {
		t.Error("应该检测到时间冲突但没有检测到")
	}

	// 测试2: 测试无冲突方案
	fmt.Println("\n--- 测试2: 无冲突方案检查 ---")
	validMatch := map[uint]uint{
		1: 1, // 律师1 处理案件1 (9:00-10:00)
		3: 1, // 律师1 处理案件3 (12:00-13:00)
		2: 2, // 律师2 处理案件2 (9:30-10:30)
	}

	isValid = scheduler.validateNoTimeConflicts(validMatch)
	fmt.Printf("无冲突方案验证结果: %v (预期: true)\n", isValid)
	if !isValid {
		t.Error("无冲突方案被误判为有冲突")
	}

	// 测试3: 测试单个律师的详细验证
	fmt.Println("\n--- 测试3: 单个律师详细验证 ---")
	lawyer1Trials := []uint{1, 2} // 冲突案件
	valid, errors := scheduler.validateSingleLawyerSchedule(1, lawyer1Trials)
	fmt.Printf("律师1时间安排验证结果: %v\n", valid)
	if valid {
		t.Error("应该检测到律师1的时间冲突")
	}
	for _, err := range errors {
		fmt.Printf("  错误: %s\n", err)
	}

	// 测试4: 测试冲突解决机制
	fmt.Println("\n--- 测试4: 冲突解决机制 ---")
	conflictFreeMatch := scheduler.enforceNoTimeConflicts(conflictMatch)
	fmt.Printf("冲突解决前: %v\n", conflictMatch)
	fmt.Printf("冲突解决后: %v\n", conflictFreeMatch)

	isValid = scheduler.validateNoTimeConflicts(conflictFreeMatch)
	fmt.Printf("解决后验证结果: %v (预期: true)\n", isValid)
	if !isValid {
		t.Error("冲突解决后仍然存在时间冲突")
	}

	// 测试5: 测试时间窗口计算
	fmt.Println("\n--- 测试5: 时间窗口计算测试 ---")
	trial1 := trials[0]
	start, end, err := scheduler.calculateTravelTime(1, &trial1)
	if err != nil {
		t.Errorf("计算出行时间失败: %v", err)
	}
	fmt.Printf("案件1时间窗口: %s - %s\n", start.Format("15:04"), end.Format("15:04"))
	fmt.Printf("案件1庭审时间: %s - %s\n", trial1.StartTime.Format("15:04"), trial1.EndTime.Format("15:04"))

	trial2 := trials[1]
	start2, end2, err2 := scheduler.calculateTravelTime(1, &trial2)
	if err2 != nil {
		t.Errorf("计算出行时间失败: %v", err2)
	}
	fmt.Printf("案件2时间窗口: %s - %s\n", start2.Format("15:04"), end2.Format("15:04"))
	fmt.Printf("案件2庭审时间: %s - %s\n", trial2.StartTime.Format("15:04"), trial2.EndTime.Format("15:04"))

	overlap := scheduler.isTimeOverlap(start, end, start2, end2)
	fmt.Printf("时间重叠检查: %v (预期: true)\n", overlap)
	if !overlap {
		t.Error("应该检测到时间重叠但没有检测到")
	}

	fmt.Println("\n=== 时间冲突检查测试完成 ===")
}

// TestTimeConflictEdgeCases 测试时间冲突边界情况
func TestTimeConflictEdgeCases(t *testing.T) {
	fmt.Println("=== 测试时间冲突边界情况 ===")

	// 创建测试数据
	lawyers := []model.Lawyer{
		{ID: 1, Name: "律师1", Lat: 39.9042, Lng: 116.4074, FormattedAddress: "北京市朝阳区"},
	}

	courts := []model.Court{
		{ID: 1, Name: "法院1", Lat: 39.9042, Lng: 116.4074, FormattedAddress: "北京市朝阳区"},
	}

	baseTime := time.Date(2024, 1, 1, 9, 0, 0, 0, time.UTC)

	// 边界情况1: 紧挨着的案件（应该不冲突）
	trials1 := []model.Trial{
		{ID: 1, CourtID: 1, StartTime: baseTime, EndTime: baseTime.Add(time.Hour)},
		{ID: 2, CourtID: 1, StartTime: baseTime.Add(time.Hour + 30*time.Minute), EndTime: baseTime.Add(2*time.Hour + 30*time.Minute)}, // 间隔30分钟
	}

	// 边界情况2: 刚好接触的案件（应该不冲突）
	trials2 := []model.Trial{
		{ID: 3, CourtID: 1, StartTime: baseTime, EndTime: baseTime.Add(time.Hour)},
		{ID: 4, CourtID: 1, StartTime: baseTime.Add(time.Hour + 20*time.Minute), EndTime: baseTime.Add(2*time.Hour + 20*time.Minute)}, // 间隔20分钟，考虑到往返10分钟应该刚好
	}

	routes := []model.Routes{
		{
			Type:    "lawyer_to_court",
			FromLat: 39.9042, FromLng: 116.4074, FromFormattedAddress: "北京市朝阳区",
			ToLat: 39.9042, ToLng: 116.4074, ToFormattedAddress: "北京市朝阳区",
			Distance: 1000, Duration: 600, Cost: 50, // 10分钟路程
		},
	}

	// 测试边界情况1
	fmt.Println("\n--- 测试边界情况1: 间隔30分钟的案件 ---")
	scheduler1 := NewGeneticAlgorithmScheduler(nil, lawyers, courts, trials1, routes).(*GeneticAlgorithmScheduler)

	match1 := map[uint]uint{1: 1, 2: 1}
	isValid1 := scheduler1.validateNoTimeConflicts(match1)
	fmt.Printf("间隔30分钟案件验证结果: %v (预期: true)\n", isValid1)

	// 测试边界情况2
	fmt.Println("\n--- 测试边界情况2: 间隔20分钟的案件 ---")
	scheduler2 := NewGeneticAlgorithmScheduler(nil, lawyers, courts, trials2, routes).(*GeneticAlgorithmScheduler)

	match2 := map[uint]uint{3: 1, 4: 1}
	isValid2 := scheduler2.validateNoTimeConflicts(match2)
	fmt.Printf("间隔20分钟案件验证结果: %v (预期: true)\n", isValid2)

	fmt.Println("\n=== 边界情况测试完成 ===")
}

// TestPerformanceMetrics 测试性能指标功能
func TestPerformanceMetrics(t *testing.T) {
	fmt.Println("=== 测试性能指标功能 ===")
	
	scheduler := newGeneticAlgorithmScheduler().(*GeneticAlgorithmScheduler)
	
	// 设置较小的参数以便快速测试
	err := scheduler.SetGAParameters(20, 5, 0.1, 0.7)
	if err != nil {
		t.Fatalf("设置GA参数失败: %v", err)
	}
	
	// 测试带性能指标的调度
	result, metrics, err := scheduler.ScheduleWithMetrics()
	if err != nil {
		t.Fatalf("带性能指标的调度失败: %v", err)
	}
	
	fmt.Printf("性能指标:\n")
	fmt.Printf("  执行时间: %v\n", metrics.ExecutionTime)
	fmt.Printf("  运行代数: %d\n", metrics.GenerationsRun)
	fmt.Printf("  总方案数: %d\n", metrics.TotalSolutions)
	fmt.Printf("  有效方案数: %d\n", metrics.ValidSolutions)
	
	// 验证结果
	if metrics.ExecutionTime <= 0 {
		t.Error("执行时间应该大于0")
	}
	if metrics.GenerationsRun != 5 {
		t.Errorf("期望运行5代，实际运行%d代", metrics.GenerationsRun)
	}
	
	// 验证调度结果
	totalSolutions := 0
	for _, schedules := range result.Solutions {
		totalSolutions += len(schedules)
	}
	
	if totalSolutions != metrics.TotalSolutions {
		t.Errorf("总方案数不匹配: 计算得%d，指标显示%d", totalSolutions, metrics.TotalSolutions)
	}
	
	fmt.Printf("✅ 性能指标测试通过\n")
}

// TestConfigValidation 测试配置验证功能
func TestConfigValidation(t *testing.T) {
	fmt.Println("=== 测试配置验证功能 ===")
	
	scheduler := newGeneticAlgorithmScheduler().(*GeneticAlgorithmScheduler)
	
	// 测试各种无效配置
	testCases := []struct {
		name           string
		populationSize int
		generations    int
		mutationRate   float64
		crossoverRate  float64
		shouldFail     bool
	}{
		{"负数种群大小", -10, 50, 0.1, 0.7, true},
		{"零种群大小", 0, 50, 0.1, 0.7, true},
		{"负数进化代数", 50, -10, 0.1, 0.7, true},
		{"零进化代数", 50, 0, 0.1, 0.7, true},
		{"负数变异率", 50, 20, -0.1, 0.7, true},
		{"过大变异率", 50, 20, 1.5, 0.7, true},
		{"负数交叉率", 50, 20, 0.1, -0.1, true},
		{"过大交叉率", 50, 20, 0.1, 1.5, true},
		{"有效配置", 50, 20, 0.1, 0.7, false},
	}
	
	for _, tc := range testCases {
		fmt.Printf("\n测试案例: %s\n", tc.name)
		err := scheduler.SetGAParameters(tc.populationSize, tc.generations, tc.mutationRate, tc.crossoverRate)
		
		if tc.shouldFail {
			if err == nil {
				t.Errorf("案例 '%s' 应该失败但没有失败", tc.name)
			} else {
				fmt.Printf("✅ 正确捕获错误: %v\n", err)
			}
		} else {
			if err != nil {
				t.Errorf("案例 '%s' 不应该失败但失败了: %v", tc.name, err)
			} else {
				fmt.Printf("✅ 有效配置设置成功\n")
			}
		}
	}
}

// TestInternalState 测试内部状态获取功能
func TestInternalState(t *testing.T) {
	fmt.Println("=== 测试内部状态获取功能 ===")
	
	scheduler := newGeneticAlgorithmScheduler().(*GeneticAlgorithmScheduler)
	
	// 获取内部状态
	state := scheduler.GetInternalState()
	
	fmt.Printf("内部状态:\n")
	fmt.Printf("  律师数量: %d\n", state.LawyersCount)
	fmt.Printf("  法院数量: %d\n", state.CourtsCount)
	fmt.Printf("  案件数量: %d\n", state.TrialsCount)
	fmt.Printf("  路径数量: %d\n", state.RoutesCount)
	fmt.Printf("  冲突矩阵已初始化: %v\n", state.ConflictsMatrix)
	fmt.Printf("  费用矩阵已初始化: %v\n", state.CostMatrix)
	
	// 验证状态
	if state.LawyersCount <= 0 {
		t.Error("律师数量应该大于0")
	}
	if state.CourtsCount <= 0 {
		t.Error("法院数量应该大于0")
	}
	if state.TrialsCount <= 0 {
		t.Error("案件数量应该大于0")
	}
	if state.RoutesCount <= 0 {
		t.Error("路径数量应该大于0")
	}
	if !state.ConflictsMatrix {
		t.Error("冲突矩阵应该已初始化")
	}
	
	fmt.Printf("✅ 内部状态测试通过\n")
}

// TestRandomSeed 测试随机数种子功能
func TestRandomSeed(t *testing.T) {
	fmt.Println("=== 测试随机数种子功能 ===")
	
	// 创建两个相同的调度器
	scheduler1 := newGeneticAlgorithmScheduler().(*GeneticAlgorithmScheduler)
	scheduler2 := newGeneticAlgorithmScheduler().(*GeneticAlgorithmScheduler)
	
	// 设置相同的参数和种子
	seed := int64(12345)
	scheduler1.SetRandomSeed(seed)
	scheduler2.SetRandomSeed(seed)
	
	// 设置较小的参数以便快速测试
	scheduler1.SetGAParameters(10, 3, 0.1, 0.7)
	scheduler2.SetGAParameters(10, 3, 0.1, 0.7)
	
	// 使用相同的权重配置
	config := []GAWeightConfig{
		{"种子测试", 5.0, 5.0, 5.0, 1.0},
	}
	
	result1 := scheduler1.ScheduleWithCustomWeights(config)
	
	// 重新设置相同种子
	scheduler2.SetRandomSeed(seed)
	result2 := scheduler2.ScheduleWithCustomWeights(config)
	
	fmt.Printf("调度器1结果: %v\n", result1)
	fmt.Printf("调度器2结果: %v\n", result2)
	
	// 注意：由于遗传算法的复杂性，即使使用相同种子，结果也可能不完全相同
	// 这里主要测试种子设置功能是否正常工作
	fmt.Printf("✅ 随机数种子设置功能正常\n")
}

// TestErrorHandling 测试错误处理
func TestErrorHandling(t *testing.T) {
	fmt.Println("=== 测试错误处理 ===")
	
	scheduler := newGeneticAlgorithmScheduler().(*GeneticAlgorithmScheduler)
	
	// 测试无效的最大容量设置
	fmt.Println("\n--- 测试最大容量设置 ---")
	scheduler.SetMaxCapacityPerLawyer(0) // 设置为0，这在validateConfig中会被检查
	
	_, _, err := scheduler.ScheduleWithMetrics()
	if err != nil {
		fmt.Printf("✅ 正确捕获最大容量错误: %v\n", err)
	} else {
		// 如果没有错误，说明0被接受了，这也是可以的
		fmt.Printf("ℹ️  最大容量0被接受\n")
	}
	
	// 恢复有效设置
	scheduler.SetMaxCapacityPerLawyer(5)
	
	// 测试极端参数设置
	fmt.Println("\n--- 测试极端参数 ---")
	err = scheduler.SetGAParameters(1, 1, 0.0, 0.0) // 最小有效参数
	if err != nil {
		t.Errorf("最小有效参数应该被接受: %v", err)
	} else {
		fmt.Printf("✅ 最小有效参数被接受\n")
	}
	
	err = scheduler.SetGAParameters(1000, 1000, 1.0, 1.0) // 最大有效参数
	if err != nil {
		t.Errorf("最大有效参数应该被接受: %v", err)
	} else {
		fmt.Printf("✅ 最大有效参数被接受\n")
	}
	
	fmt.Printf("✅ 错误处理测试完成\n")
}

// TestMemoryUsage 测试内存使用情况
func TestMemoryUsage(t *testing.T) {
	fmt.Println("=== 测试内存使用情况 ===")
	
	scheduler := newGeneticAlgorithmScheduler().(*GeneticAlgorithmScheduler)
	state := scheduler.GetInternalState()
	
	// 估算冲突矩阵的内存使用
	conflictMatrixSize := state.LawyersCount * state.TrialsCount * state.TrialsCount
	fmt.Printf("冲突矩阵大小估算: %d 个布尔值\n", conflictMatrixSize)
	
	// 如果矩阵过大，给出警告
	if conflictMatrixSize > 1000000 { // 100万个布尔值
		fmt.Printf("⚠️  冲突矩阵较大，可能需要优化内存使用\n")
	} else {
		fmt.Printf("✅ 冲突矩阵大小合理\n")
	}
	
	// 测试大数据集的处理能力
	if state.LawyersCount > 100 || state.TrialsCount > 1000 {
		fmt.Printf("ℹ️  检测到大数据集，建议使用较小的GA参数\n")
		scheduler.SetGAParameters(50, 20, 0.1, 0.7) // 推荐参数
	}
	
	fmt.Printf("✅ 内存使用测试完成\n")
}

// TestNewSchedulerInterface 测试新的调度器接口
func TestNewSchedulerInterface(t *testing.T) {
	fmt.Println("=== 测试新的调度器接口 ===")
	
	scheduler := newGeneticAlgorithmScheduler().(*GeneticAlgorithmScheduler)
	
	// 测试SetConfig方法
	fmt.Println("\n--- 测试SetConfig方法 ---")
	config := SchedulerConfig{
		PopulationSize:       30,
		Generations:          10,
		MutationRate:         0.15,
		CrossoverRate:        0.75,
		MaxCapacityPerLawyer: 4,
		EnableConcurrency:    true,
		OptimizeForDataset:   true,
	}
	
	err := scheduler.SetConfig(config)
	if err != nil {
		t.Errorf("设置配置失败: %v", err)
	} else {
		fmt.Printf("✅ 配置设置成功\n")
	}
	
	// 测试新的Schedule方法
	fmt.Println("\n--- 测试新的Schedule方法 ---")
	result := scheduler.Schedule()
	fmt.Printf("✅ 调度成功，找到方案组数: %d\n", len(result))
	
	// 计算总方案数
	totalSolutions := 0
	for _, schedules := range result {
		totalSolutions += len(schedules)
	}
	fmt.Printf("  总方案数: %d\n", totalSolutions)
	
	// 测试GetMetrics方法
	fmt.Println("\n--- 测试GetMetrics方法 ---")
	metrics := scheduler.GetMetrics()
	fmt.Printf("调度指标:\n")
	fmt.Printf("  最佳适应度: %.2f\n", metrics.BestFitness)
	fmt.Printf("  平均适应度: %.2f\n", metrics.AverageFitness)
	fmt.Printf("  冲突率: %.2f%%\n", metrics.ConflictRate*100)
	fmt.Printf("  验证错误数: %d\n", len(metrics.ValidationErrors))
	
	if len(metrics.ValidationErrors) > 0 {
		fmt.Printf("  验证错误:\n")
		for _, err := range metrics.ValidationErrors {
			fmt.Printf("    - %s\n", err)
		}
	}
	
	// 测试SetWeights方法
	fmt.Println("\n--- 测试SetWeights方法 ---")
	weights := WeightConfig{
		Name:          "测试权重",
		DistWeight:    8.0,
		CostWeight:    3.0,
		TimeWeight:    5.0,
		BalanceWeight: 2.0,
	}
	
	err = scheduler.SetWeights(weights)
	if err != nil {
		t.Errorf("设置权重失败: %v", err)
	} else {
		fmt.Printf("✅ 权重设置成功\n")
	}
	
	fmt.Printf("✅ 新接口测试完成\n")
}

// TestConcurrentProcessing 测试并发处理
func TestConcurrentProcessing(t *testing.T) {
	fmt.Println("=== 测试并发处理 ===")
	
	scheduler := newGeneticAlgorithmScheduler().(*GeneticAlgorithmScheduler)
	
	// 设置较小的参数以便快速测试
	scheduler.SetGAParameters(20, 5, 0.1, 0.7)
	
	// 测试并发调度
	fmt.Println("\n--- 测试并发调度 ---")
	configs := []GAWeightConfig{
		{"并发测试1", 5.0, 5.0, 5.0, 1.0},
		{"并发测试2", 10.0, 2.0, 2.0, 0.5},
		{"并发测试3", 2.0, 10.0, 2.0, 0.5},
	}
	
	start := time.Now()
	result := scheduler.ScheduleWithCustomWeights(configs) // 暂时使用串行版本
	concurrentTime := time.Since(start)
	
	fmt.Printf("并发调度完成，耗时: %v\n", concurrentTime)
	
	// 统计结果
	totalSolutions := 0
	for matchCount, schedules := range result {
		fmt.Printf("  匹配数量 %d: %d 个方案\n", matchCount, len(schedules))
		totalSolutions += len(schedules)
	}
	fmt.Printf("总方案数: %d\n", totalSolutions)
	
	// 比较串行处理时间（简化测试）
	start = time.Now()
	_ = scheduler.ScheduleWithCustomWeights(configs[:1]) // 只测试一个配置
	serialTime := time.Since(start)
	
	fmt.Printf("串行处理单个配置耗时: %v\n", serialTime)
	fmt.Printf("✅ 并发处理测试完成\n")
}

// TestErrorHandlingImprovements 测试错误处理改进
func TestErrorHandlingImprovements(t *testing.T) {
	fmt.Println("=== 测试错误处理改进 ===")
	
	scheduler := newGeneticAlgorithmScheduler().(*GeneticAlgorithmScheduler)
	
	// 测试安全的费用计算
	fmt.Println("\n--- 测试安全的费用计算 ---")
	
	// 测试有效的案件-律师组合
	if len(scheduler.trialsList) > 0 && len(scheduler.lawyersList) > 0 {
		trialID := scheduler.trialsList[0]
		lawyerID := scheduler.lawyersList[0]
		
		result := scheduler.calculateAssignmentCostSafe(trialID, lawyerID)
		if result.Valid {
			fmt.Printf("✅ 有效组合费用计算成功: %.2f\n", result.Cost)
		} else {
			fmt.Printf("⚠️  有效组合费用计算失败: %v\n", result.Error)
		}
	}
	
	// 测试无效的案件ID
	invalidResult := scheduler.calculateAssignmentCostSafe(99999, scheduler.lawyersList[0])
	if !invalidResult.Valid {
		fmt.Printf("✅ 正确捕获无效案件ID错误: %v\n", invalidResult.Error)
	} else {
		t.Error("应该捕获无效案件ID错误")
	}
	
	// 测试无效的律师ID
	invalidResult = scheduler.calculateAssignmentCostSafe(scheduler.trialsList[0], 99999)
	if !invalidResult.Valid {
		fmt.Printf("✅ 正确捕获无效律师ID错误: %v\n", invalidResult.Error)
	} else {
		t.Error("应该捕获无效律师ID错误")
	}
	
	// 测试数据完整性验证
	fmt.Println("\n--- 测试数据完整性验证 ---")
	errors := scheduler.ValidateDataIntegrity()
	if len(errors) == 0 {
		fmt.Printf("✅ 数据完整性验证通过\n")
	} else {
		fmt.Printf("⚠️  发现数据完整性问题:\n")
		for _, err := range errors {
			fmt.Printf("    - %s\n", err)
		}
	}
	
	fmt.Printf("✅ 错误处理改进测试完成\n")
}

// TestMemoryOptimization 测试内存优化
func TestMemoryOptimization(t *testing.T) {
	fmt.Println("=== 测试内存优化 ===")
	
	scheduler := newGeneticAlgorithmScheduler().(*GeneticAlgorithmScheduler)
	
	// 获取内存使用情况
	memoryUsage := scheduler.GetMemoryUsage()
	
	fmt.Printf("内存使用情况:\n")
	for key, value := range memoryUsage {
		fmt.Printf("  %s: %v\n", key, value)
	}
	
	// 检查冲突映射是否使用了优化的数据结构
	if scheduler.conflicts != nil {
		fmt.Printf("✅ 冲突映射已初始化为map类型\n")
		fmt.Printf("  冲突映射大小: %d 个条目\n", len(scheduler.conflicts))
	} else {
		t.Error("冲突映射未初始化")
	}
	
	// 测试大数据集优化
	fmt.Println("\n--- 测试大数据集优化 ---")
	originalPopSize := scheduler.populationSize
	originalGens := scheduler.generations
	
	scheduler.OptimizeForLargeDataset()
	
	fmt.Printf("优化前参数: 种群=%d, 代数=%d\n", originalPopSize, originalGens)
	fmt.Printf("优化后参数: 种群=%d, 代数=%d\n", scheduler.populationSize, scheduler.generations)
	
	fmt.Printf("✅ 内存优化测试完成\n")
}
