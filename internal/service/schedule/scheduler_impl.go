package schedule

import (
	"fmt"
	"time"
)

// 实现新的Scheduler接口方法

// ScheduleWithMetrics 带性能指标的调度方法
func (s *GeneticAlgorithmScheduler) ScheduleWithMetrics() (ScheduleResult, PerformanceMetrics, error) {
	start := time.Now()

	// 验证配置
	if err := s.validateConfig(); err != nil {
		return ScheduleResult{}, PerformanceMetrics{}, err
	}

	// 验证数据完整性（仅警告，不阻止执行）
	if errors := s.ValidateDataIntegrity(); len(errors) > 0 {
		fmt.Printf("⚠️  数据完整性警告: %v\n", errors)
	}

	// 执行调度
	solutions := s.Schedule()
	executionTime := time.Since(start)

	// 计算性能指标
	totalSolutions := 0
	for _, schedules := range solutions {
		totalSolutions += len(schedules)
	}

	// 计算最佳适应度
	bestFitness := 0.0
	if len(s.lastCalculatedSolutions) > 0 {
		bestFitness = float64(s.lastCalculatedSolutions[0].TotalPrice)
	}

	result := ScheduleResult{
		Solutions: solutions,
		Metadata: ScheduleMetadata{
			TotalSolutions: totalSolutions,
			ValidSolutions: len(s.lastCalculatedSolutions),
			ExecutionTime:  executionTime,
			ConfigUsed:     "genetic_algorithm",
			DatasetInfo: DatasetInfo{
				LawyersCount: len(s.lawyerMap),
				CourtsCount:  len(s.courtMap),
				TrialsCount:  len(s.trialMap),
				RoutesCount:  len(s.routeMap),
			},
			QualityMetrics: map[string]interface{}{
				"best_cost":     bestFitness,
				"conflict_rate": 0.0, // 所有方案都无冲突
				"coverage_rate": float64(totalSolutions) / float64(len(s.trialMap)),
			},
		},
	}

	metrics := PerformanceMetrics{
		ExecutionTime:  executionTime,
		GenerationsRun: s.generations,
		BestFitness:    bestFitness,
		TotalSolutions: totalSolutions,
		ValidSolutions: len(s.lastCalculatedSolutions),
	}

	return result, metrics, nil
}

// SetConfig 设置调度器配置
func (s *GeneticAlgorithmScheduler) SetConfig(config SchedulerConfig) error {
	// 验证配置
	if config.PopulationSize <= 0 {
		return fmt.Errorf("种群大小必须为正数，当前值: %d", config.PopulationSize)
	}
	if config.Generations <= 0 {
		return fmt.Errorf("进化代数必须为正数，当前值: %d", config.Generations)
	}
	if config.MutationRate < 0 || config.MutationRate > 1 {
		return fmt.Errorf("变异率必须在0-1之间，当前值: %.2f", config.MutationRate)
	}
	if config.CrossoverRate < 0 || config.CrossoverRate > 1 {
		return fmt.Errorf("交叉率必须在0-1之间，当前值: %.2f", config.CrossoverRate)
	}
	if config.MaxCapacityPerLawyer <= 0 {
		return fmt.Errorf("律师最大容量必须为正数，当前值: %d", config.MaxCapacityPerLawyer)
	}

	// 应用配置
	s.populationSize = config.PopulationSize
	s.generations = config.Generations
	s.mutationRate = config.MutationRate
	s.crossoverRate = config.CrossoverRate
	s.maxCapacityPerLawyer = config.MaxCapacityPerLawyer

	// 根据配置优化
	if config.OptimizeForDataset {
		s.OptimizeForLargeDataset()
	}

	return nil
}

// GetMetrics 获取调度指标
func (s *GeneticAlgorithmScheduler) GetMetrics() ScheduleMetrics {
	memoryUsage := s.GetMemoryUsage()
	validationErrors := s.ValidateDataIntegrity()

	// 计算平均适应度
	avgFitness := 0.0
	if len(s.lastCalculatedSolutions) > 0 {
		totalCost := 0.0
		for _, solution := range s.lastCalculatedSolutions {
			totalCost += float64(solution.TotalPrice)
		}
		avgFitness = totalCost / float64(len(s.lastCalculatedSolutions))
	}

	// 计算最佳适应度
	bestFitness := 0.0
	if len(s.lastCalculatedSolutions) > 0 {
		bestFitness = float64(s.lastCalculatedSolutions[0].TotalPrice)
	}

	return ScheduleMetrics{
		BestFitness:      bestFitness,
		AverageFitness:   avgFitness,
		ConvergenceGen:   s.generations, // 简化处理
		ConflictRate:     0.0,           // 所有方案都无冲突
		MemoryUsage:      memoryUsage,
		ValidationErrors: validationErrors,
	}
}

// ValidateConfig 验证配置
func (s *GeneticAlgorithmScheduler) ValidateConfig() error {
	return s.validateConfig()
}

// SetWeights 设置权重配置
func (s *GeneticAlgorithmScheduler) SetWeights(weights WeightConfig) error {
	if weights.DistWeight < 0 {
		return fmt.Errorf("距离权重不能为负数: %.2f", weights.DistWeight)
	}
	if weights.CostWeight < 0 {
		return fmt.Errorf("费用权重不能为负数: %.2f", weights.CostWeight)
	}
	if weights.TimeWeight < 0 {
		return fmt.Errorf("时间权重不能为负数: %.2f", weights.TimeWeight)
	}
	if weights.BalanceWeight < 0 {
		return fmt.Errorf("负载均衡权重不能为负数: %.2f", weights.BalanceWeight)
	}

	s.distanceWeight = weights.DistWeight
	s.costWeight = weights.CostWeight
	s.timeWeight = weights.TimeWeight
	s.balanceWeight = weights.BalanceWeight
	s.currentBalanceWeight = weights.BalanceWeight

	return nil
}

// GetLastSolutions 获取最后的解决方案
func (s *GeneticAlgorithmScheduler) GetLastSolutions() []GAScheduleSolution {
	return s.lastCalculatedSolutions
}

// getConflictKey 生成冲突检查的唯一键
func (s *GeneticAlgorithmScheduler) getConflictKey(lawyerID uint, trial1, trial2 int) string {
	if trial1 > trial2 {
		trial1, trial2 = trial2, trial1 // 确保键的一致性
	}
	return fmt.Sprintf("%d:%d:%d", lawyerID, trial1, trial2)
}
