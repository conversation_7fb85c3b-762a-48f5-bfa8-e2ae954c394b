package schedule

import "time"

// Scheduler 改进的调度器接口
type Scheduler interface {
	Schedule() (ScheduleResult, error)
	ScheduleWithMetrics() (ScheduleResult, PerformanceMetrics, error)
	SetConfig(config SchedulerConfig) error
	GetMetrics() ScheduleMetrics
	ValidateConfig() error
}

// ScheduleResult 调度结果
type ScheduleResult struct {
	Solutions map[uint][]map[uint]uint `json:"solutions"`
	Metadata  ScheduleMetadata         `json:"metadata"`
}

// ScheduleMetadata 调度元数据
type ScheduleMetadata struct {
	TotalSolutions   int                    `json:"total_solutions"`
	ValidSolutions   int                    `json:"valid_solutions"`
	ExecutionTime    time.Duration          `json:"execution_time"`
	ConfigUsed       string                 `json:"config_used"`
	DatasetInfo      DatasetInfo            `json:"dataset_info"`
	QualityMetrics   map[string]interface{} `json:"quality_metrics"`
}

// DatasetInfo 数据集信息
type DatasetInfo struct {
	LawyersCount int `json:"lawyers_count"`
	CourtsCount  int `json:"courts_count"`
	TrialsCount  int `json:"trials_count"`
	RoutesCount  int `json:"routes_count"`
}

// SchedulerConfig 调度器配置
type SchedulerConfig struct {
	PopulationSize       int     `json:"population_size"`
	Generations          int     `json:"generations"`
	MutationRate         float64 `json:"mutation_rate"`
	CrossoverRate        float64 `json:"crossover_rate"`
	MaxCapacityPerLawyer int     `json:"max_capacity_per_lawyer"`
	EnableConcurrency    bool    `json:"enable_concurrency"`
	OptimizeForDataset   bool    `json:"optimize_for_dataset"`
}

// ScheduleMetrics 调度指标
type ScheduleMetrics struct {
	BestFitness      float64           `json:"best_fitness"`
	AverageFitness   float64           `json:"average_fitness"`
	ConvergenceGen   int               `json:"convergence_gen"`
	ConflictRate     float64           `json:"conflict_rate"`
	MemoryUsage      map[string]interface{} `json:"memory_usage"`
	ValidationErrors []string          `json:"validation_errors"`
}

// GeneticScheduler 遗传算法调度器接口
type GeneticScheduler interface {
	Scheduler
	SetWeights(weights WeightConfig) error
	GetLastSolutions() []GAScheduleSolution
	SetRandomSeed(seed int64)
	GetInternalState() InternalState
}

// WeightConfig 权重配置
type WeightConfig struct {
	Name          string  `json:"name"`
	DistWeight    float64 `json:"dist_weight"`
	CostWeight    float64 `json:"cost_weight"`
	TimeWeight    float64 `json:"time_weight"`
	BalanceWeight float64 `json:"balance_weight"`
}