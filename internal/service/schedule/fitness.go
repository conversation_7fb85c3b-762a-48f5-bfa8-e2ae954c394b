package schedule

import (
	"math"
	"sort"
)

// evaluateIndividual 评估个体适应度
func (s *GeneticAlgorithmScheduler) evaluateIndividual(individual *Individual) {
	numTrials := len(individual.Genes)
	numLawyers := len(s.lawyersList)

	// 按律师分组
	lawyerTrials := make([][]int, numLawyers)
	totalCost := 0.0
	penalty := 0.0

	// 计算基本费用和匹配数
	if s.continuousTrialConfig.Enable {
		// 启用连续出差时，使用优化的费用计算
		totalCost = s.calculateOptimizedCost(individual, lawyerTrials)
	} else {
		// 传统费用计算
		for t := 0; t < numTrials; t++ {
			l := individual.Genes[t]
			if l >= 0 && l < numLawyers {
				lawyerTrials[l] = append(lawyerTrials[l], t)
				cost := s.currentCosts[t][l]
				if math.IsInf(cost, 1) {
					penalty += 1e12
				} else {
					totalCost += cost
				}
			}
		}
	}

	const conflictPenalty = 1e12
	const capacityPenalty = 1e9
	const matchingBonus = 1e6

	// 检查约束违反
	for l := 0; l < numLawyers; l++ {
		trials := lawyerTrials[l]
		numAssigned := len(trials)

		// 容量约束
		if numAssigned > s.maxCapacityPerLawyer {
			penalty += float64(numAssigned-s.maxCapacityPerLawyer) * capacityPenalty
		}

		// 时间冲突约束 - 使用更严格的检查
		if numAssigned > 1 {
			// 检查预计算的冲突矩阵
			numConflicts := 0
			lawyerID := s.lawyersList[l]
			for i := 0; i < numAssigned; i++ {
				for j := i + 1; j < numAssigned; j++ {
					key := s.getConflictKey(lawyerID, trials[i], trials[j])
					if s.conflicts[key] {
						numConflicts++
					}
				}
			}

			// 如果预计算冲突矩阵检测到冲突，施加惩罚
			if numConflicts > 0 {
				penalty += float64(numConflicts) * conflictPenalty
			}

			// 额外的实时冲突检查（双重保险）
			actualTrialIDs := make([]uint, 0, numAssigned)
			for _, trialIdx := range trials {
				actualTrialIDs = append(actualTrialIDs, s.trialsList[trialIdx])
			}

			// 使用实际的时间冲突检查
			for i := 0; i < len(actualTrialIDs); i++ {
				for j := i + 1; j < len(actualTrialIDs); j++ {
					if s.hasTimeConflict(lawyerID, actualTrialIDs[i], actualTrialIDs[j]) {
						penalty += conflictPenalty
					}
				}
			}
		}
	}

	// 如果有惩罚，适应度为惩罚值
	if penalty > 0 {
		individual.Fitness = penalty + totalCost
		return
	}

	// 计算匹配数
	matching := 0
	for l := 0; l < numLawyers; l++ {
		matching += len(lawyerTrials[l])
	}

	// 计算负载均衡费用
	loads := make([]float64, numLawyers)
	for l := 0; l < numLawyers; l++ {
		loads[l] = float64(len(lawyerTrials[l]))
	}
	mean := float64(matching) / float64(numLawyers)
	variance := 0.0
	for _, load := range loads {
		variance += math.Pow(load-mean, 2)
	}
	variance /= float64(numLawyers)
	balanceCost := variance * s.currentBalanceWeight
	totalCost += balanceCost

	// 计算连续开庭奖励
	continuousBonus := s.calculateContinuousTrialsBonus(individual)

	// 适应度：最小化，所以是负的匹配奖励加上总费用，减去连续开庭奖励
	individual.Fitness = -float64(matching)*matchingBonus + totalCost - continuousBonus
}

// calculateMetrics 计算方案指标
func (s *GeneticAlgorithmScheduler) calculateMetrics(matching map[uint]uint) (int, int) {
	totalDistance := 0
	totalDuration := 0

	for trialID, lawyerID := range matching {
		trial := s.trialMap[trialID]
		lawyer := s.lawyerMap[lawyerID]
		court := s.courtMap[trial.CourtID]

		if trial == nil || lawyer == nil || court == nil {
			continue
		}

		routeKey := s.getRouteKey(lawyer.GetLocation(), court.GetLocation())
		route, exists := s.routeMap[routeKey]
		if !exists || route.Distance == 0 {
			continue
		}

		// 往返距离和时间
		totalDistance += route.Distance * 2
		totalDuration += route.Duration * 2

		// 庭审时间
		trialDuration := int(trial.EndTime.Sub(trial.StartTime).Minutes())
		totalDuration += trialDuration
	}

	return totalDistance, totalDuration
}

// calculateActualCost 计算真实的总费用
func (s *GeneticAlgorithmScheduler) calculateActualCost(matching map[uint]uint) float64 {
	totalCost := 0.0

	for trialID, lawyerID := range matching {
		// 直接使用 calculateAssignmentCost 计算费用
		cost := s.calculateAssignmentCost(trialID, lawyerID)
		if !math.IsInf(cost, 1) {
			totalCost += cost
		}
	}

	// 计算负载均衡费用
	lawyerLoads := make(map[uint]int)
	for _, lawyerID := range matching {
		lawyerLoads[lawyerID]++
	}

	// 计算负载方差
	totalMatches := len(matching)
	numLawyers := len(s.lawyersList)
	meanLoad := float64(totalMatches) / float64(numLawyers)

	variance := 0.0
	for _, lawyerID := range s.lawyersList {
		load := float64(lawyerLoads[lawyerID])
		variance += math.Pow(load-meanLoad, 2)
	}
	variance /= float64(numLawyers)

	balanceCost := variance * s.currentBalanceWeight
	totalCost += balanceCost

	return totalCost
}

// calculateContinuousTrialsBonus 计算连续开庭奖励
func (s *GeneticAlgorithmScheduler) calculateContinuousTrialsBonus(individual *Individual) float64 {
	if !s.continuousTrialConfig.Enable {
		return 0
	}

	// 将个体基因转换为匹配映射
	matching := make(map[uint]uint)
	for t, l := range individual.Genes {
		if l >= 0 && l < len(s.lawyersList) {
			trialID := s.trialsList[t]
			lawyerID := s.lawyersList[l]
			matching[trialID] = lawyerID
		}
	}

	// 查找连续开庭组
	continuousGroups := s.findContinuousTrialGroups(matching)

	totalBonus := 0.0
	for _, group := range continuousGroups {
		if len(group.Trials) < 2 {
			continue
		}

		// 计算奖励
		groupSize := float64(len(group.Trials))
		var bonus float64

		if group.SameCity {
			// 同城连续开庭奖励
			bonus = groupSize * s.continuousTrialConfig.SameCityBonus
		} else {
			// 跨城连续开庭奖励
			bonus = groupSize * s.continuousTrialConfig.CrossCityBonus
		}

		// 根据连续开庭的数量给予递增奖励
		if groupSize >= 3 {
			bonus *= 1.5 // 3个或以上案件额外50%奖励
		}
		if groupSize >= 4 {
			bonus *= 1.2 // 4个或以上案件再额外20%奖励
		}

		totalBonus += bonus
	}

	return totalBonus
}

// calculateOptimizedCost 计算优化的费用（考虑连续开庭）
func (s *GeneticAlgorithmScheduler) calculateOptimizedCost(individual *Individual, lawyerTrials [][]int) float64 {
	totalCost := 0.0
	numTrials := len(individual.Genes)
	numLawyers := len(s.lawyersList)

	// 填充律师案件分组
	for t := 0; t < numTrials; t++ {
		l := individual.Genes[t]
		if l >= 0 && l < numLawyers {
			lawyerTrials[l] = append(lawyerTrials[l], t)
		}
	}

	// 为每个律师计算费用
	for l := 0; l < numLawyers; l++ {
		trials := lawyerTrials[l]
		if len(trials) == 0 {
			continue
		}

		lawyerID := s.lawyersList[l]

		// 转换为实际的案件ID
		trialIDs := make([]uint, len(trials))
		for i, t := range trials {
			trialIDs[i] = s.trialsList[t]
		}

		// 查找连续开庭组
		continuousGroups := s.findLawyerContinuousGroups(lawyerID, trialIDs)

		// 计算每个连续组的费用
		processedTrials := make(map[uint]bool)
		for _, group := range continuousGroups {
			if len(group.Trials) >= 2 {
				// 连续开庭使用优化费用计算
				cost := s.calculateContinuousTrialsCost(lawyerID, group.Trials)
				if math.IsInf(cost, 1) {
					return math.Inf(1)
				}
				totalCost += cost

				// 标记已处理的案件
				for _, trialID := range group.Trials {
					processedTrials[trialID] = true
				}
			}
		}

		// 处理非连续的单独案件
		for _, trialID := range trialIDs {
			if !processedTrials[trialID] {
				cost := s.calculateAssignmentCost(trialID, lawyerID)
				if math.IsInf(cost, 1) {
					return math.Inf(1)
				}
				totalCost += cost
			}
		}
	}

	return totalCost
}

// findLawyerContinuousGroups 查找单个律师的连续开庭组
func (s *GeneticAlgorithmScheduler) findLawyerContinuousGroups(lawyerID uint, trials []uint) []ContinuousTrialGroup {
	if len(trials) < 2 {
		return nil
	}

	// 按时间排序案件
	sort.Slice(trials, func(i, j int) bool {
		return s.trialMap[trials[i]].StartTime.Before(s.trialMap[trials[j]].StartTime)
	})

	var groups []ContinuousTrialGroup
	currentGroup := []uint{trials[0]}

	for i := 1; i < len(trials); i++ {
		if s.isTrialsContinuous(lawyerID, trials[i-1], trials[i]) {
			currentGroup = append(currentGroup, trials[i])
		} else {
			// 当前组结束，如果有多个案件则保存
			if len(currentGroup) >= 2 {
				group := s.createContinuousTrialGroup(lawyerID, currentGroup)
				groups = append(groups, group)
			}
			// 开始新组
			currentGroup = []uint{trials[i]}
		}
	}

	// 处理最后一组
	if len(currentGroup) >= 2 {
		group := s.createContinuousTrialGroup(lawyerID, currentGroup)
		groups = append(groups, group)
	}

	return groups
}
