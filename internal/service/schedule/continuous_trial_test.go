package schedule

import (
	"external-tool/internal/model"
	"math"
	"testing"
	"time"
)

// TestContinuousTrialConfig 测试连续出差配置
func TestContinuousTrialConfig(t *testing.T) {
	scheduler := createTestScheduler()

	// 测试默认配置
	defaultConfig := scheduler.GetContinuousTrialConfig()
	if defaultConfig.Enable {
		t.Error("默认配置应该禁用连续出差功能")
	}

	// 测试设置配置
	newConfig := ContinuousTrialConfig{
		Enable:               true,
		SameCityBonus:        1500.0,
		CrossCityBonus:       750.0,
		MaxTravelTimeBetween: 120,
		MinTimeBetweenTrials: 60,
		MaxTimeBetweenTrials: 24,
	}

	scheduler.SetContinuousTrialConfig(newConfig)
	retrievedConfig := scheduler.GetContinuousTrialConfig()

	if !retrievedConfig.Enable {
		t.Error("配置设置失败：连续出差功能应该启用")
	}
	if retrievedConfig.SameCityBonus != 1500.0 {
		t.Errorf("同城奖励配置错误：期望 1500.0，实际 %f", retrievedConfig.SameCityBonus)
	}
}

// TestIsTrialsContinuous 测试连续开庭判断逻辑
func TestIsTrialsContinuous(t *testing.T) {
	scheduler := createTestSchedulerWithData()

	// 启用连续出差功能
	config := scheduler.GetContinuousTrialConfig()
	config.Enable = true
	config.MinTimeBetweenTrials = 30
	config.MaxTimeBetweenTrials = 24
	config.MaxTravelTimeBetween = 180
	scheduler.SetContinuousTrialConfig(config)

	testCases := []struct {
		name        string
		lawyerID    uint
		trial1ID    uint
		trial2ID    uint
		expected    bool
		description string
	}{
		{
			name:        "同城连续开庭-正常间隔",
			lawyerID:    1,
			trial1ID:    1,
			trial2ID:    2,
			expected:    true,
			description: "同一城市，时间间隔合理",
		},
		{
			name:        "时间间隔过短",
			lawyerID:    1,
			trial1ID:    1,
			trial2ID:    3,
			expected:    false,
			description: "时间间隔小于最小要求",
		},
		{
			name:        "时间间隔过长",
			lawyerID:    1,
			trial1ID:    1,
			trial2ID:    4,
			expected:    false,
			description: "时间间隔超过最大限制",
		},
		{
			name:        "跨城出行时间可接受",
			lawyerID:    1,
			trial1ID:    1,
			trial2ID:    5,
			expected:    true,
			description: "不同城市但出行时间在允许范围内",
		},
		{
			name:        "跨城出行时间过长",
			lawyerID:    1,
			trial1ID:    1,
			trial2ID:    6,
			expected:    false,
			description: "不同城市且出行时间超过限制",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := scheduler.isTrialsContinuous(tc.lawyerID, tc.trial1ID, tc.trial2ID)
			if result != tc.expected {
				t.Errorf("%s: 期望 %v，实际 %v - %s", tc.name, tc.expected, result, tc.description)
			}
		})
	}
}

// TestFindContinuousTrialGroups 测试连续开庭组查找
func TestFindContinuousTrialGroups(t *testing.T) {
	scheduler := createTestSchedulerWithData()

	// 启用连续出差功能
	config := scheduler.GetContinuousTrialConfig()
	config.Enable = true
	scheduler.SetContinuousTrialConfig(config)

	// 测试匹配方案
	matching := map[uint]uint{
		1: 1, // 律师1处理案件1
		2: 1, // 律师1处理案件2 (连续)
		3: 2, // 律师2处理案件3
		5: 1, // 律师1处理案件5 (跨城连续)
	}

	groups := scheduler.findContinuousTrialGroups(matching)

	// 验证结果
	if len(groups) == 0 {
		t.Error("应该找到至少一个连续开庭组")
	}

	// 查找律师1的连续开庭组
	var lawyer1Group *ContinuousTrialGroup
	for i := range groups {
		if groups[i].LawyerID == 1 {
			lawyer1Group = &groups[i]
			break
		}
	}

	if lawyer1Group == nil {
		t.Error("应该找到律师1的连续开庭组")
	} else {
		if len(lawyer1Group.Trials) < 2 {
			t.Errorf("律师1的连续开庭组应该包含至少2个案件，实际 %d", len(lawyer1Group.Trials))
		}
	}
}

// TestCalculateContinuousTrialsCost 测试连续开庭费用计算
func TestCalculateContinuousTrialsCost(t *testing.T) {
	scheduler := createTestSchedulerWithData()

	testCases := []struct {
		name        string
		lawyerID    uint
		trials      []uint
		expectValid bool
		description string
	}{
		{
			name:        "单个案件",
			lawyerID:    1,
			trials:      []uint{1},
			expectValid: true,
			description: "单个案件应该使用普通费用计算",
		},
		{
			name:        "两个连续案件",
			lawyerID:    1,
			trials:      []uint{1, 2},
			expectValid: true,
			description: "两个连续案件应该使用优化费用计算",
		},
		{
			name:        "三个连续案件",
			lawyerID:    1,
			trials:      []uint{1, 2, 5},
			expectValid: true,
			description: "三个连续案件应该进一步优化费用",
		},
		{
			name:        "无效律师ID",
			lawyerID:    999,
			trials:      []uint{1, 2},
			expectValid: false,
			description: "无效律师ID应该返回无穷大费用",
		},
		{
			name:        "空案件列表",
			lawyerID:    1,
			trials:      []uint{},
			expectValid: true,
			description: "空案件列表应该返回0费用",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			cost := scheduler.calculateContinuousTrialsCost(tc.lawyerID, tc.trials)

			if tc.expectValid {
				if cost < 0 {
					t.Errorf("%s: 费用不应该为负数，实际 %f", tc.name, cost)
				}
			} else {
				if !math.IsInf(cost, 1) {
					t.Errorf("%s: 应该返回无穷大费用，实际 %f", tc.name, cost)
				}
			}
		})
	}
}

// TestContinuousTrialsBonusCalculation 测试连续开庭奖励计算
func TestContinuousTrialsBonusCalculation(t *testing.T) {
	scheduler := createTestSchedulerWithData()

	// 启用连续出差功能并设置奖励权重
	config := scheduler.GetContinuousTrialConfig()
	config.Enable = true
	config.SameCityBonus = 1000.0
	config.CrossCityBonus = 500.0
	scheduler.SetContinuousTrialConfig(config)

	// 创建测试个体
	individual := &Individual{
		Genes: []int{0, 0, 1, -1, 0}, // 律师0处理案件0,1,4，律师1处理案件2
	}

	bonus := scheduler.calculateContinuousTrialsBonus(individual)

	if bonus <= 0 {
		t.Error("连续开庭应该产生正奖励")
	}

	// 测试禁用功能时的奖励
	config.Enable = false
	scheduler.SetContinuousTrialConfig(config)

	bonusDisabled := scheduler.calculateContinuousTrialsBonus(individual)
	if bonusDisabled != 0 {
		t.Error("禁用连续出差功能时奖励应该为0")
	}
}

// TestEdgeCases 测试边界情况
func TestEdgeCases(t *testing.T) {
	scheduler := createTestSchedulerWithData()

	// 启用连续出差功能
	config := scheduler.GetContinuousTrialConfig()
	config.Enable = true
	scheduler.SetContinuousTrialConfig(config)

	t.Run("数据缺失情况", func(t *testing.T) {
		// 测试不存在的案件ID
		result := scheduler.isTrialsContinuous(1, 999, 1000)
		if result {
			t.Error("不存在的案件应该返回false")
		}

		// 测试不存在的律师ID
		cost := scheduler.calculateContinuousTrialsCost(999, []uint{1, 2})
		if !math.IsInf(cost, 1) {
			t.Errorf("不存在的律师应该返回无穷大费用，实际 %f", cost)
		}
	})

	t.Run("时间边界情况", func(t *testing.T) {
		// 测试时间刚好等于最小间隔
		config.MinTimeBetweenTrials = 60
		scheduler.SetContinuousTrialConfig(config)

		// 这里需要根据实际测试数据调整
		// result := scheduler.isTrialsContinuous(1, trial1, trial2)
		// 验证边界时间的处理
	})

	t.Run("空数据情况", func(t *testing.T) {
		emptyMatching := make(map[uint]uint)
		groups := scheduler.findContinuousTrialGroups(emptyMatching)
		if len(groups) != 0 {
			t.Error("空匹配应该返回空的连续开庭组")
		}
	})
}

// createTestScheduler 创建测试用的调度器
func createTestScheduler() *GeneticAlgorithmScheduler {
	return &GeneticAlgorithmScheduler{
		continuousTrialConfig: ContinuousTrialConfig{
			Enable:               false,
			SameCityBonus:        1000.0,
			CrossCityBonus:       500.0,
			MaxTravelTimeBetween: 180,
			MinTimeBetweenTrials: 30,
			MaxTimeBetweenTrials: 48,
		},
	}
}

// createTestSchedulerWithData 创建包含测试数据的调度器
func createTestSchedulerWithData() *GeneticAlgorithmScheduler {
	scheduler := createTestScheduler()

	// 创建测试律师
	lawyers := []model.Lawyer{
		{ID: 1, Name: "律师1", City: "北京市", Lat: 39.9042, Lng: 116.4074},
		{ID: 2, Name: "律师2", City: "上海市", Lat: 31.2304, Lng: 121.4737},
	}

	// 创建测试法院
	courts := []model.Court{
		{ID: 1, Name: "北京法院1", City: "北京市", Lat: 39.9042, Lng: 116.4074},
		{ID: 2, Name: "北京法院2", City: "北京市", Lat: 39.9142, Lng: 116.4174},
		{ID: 3, Name: "上海法院1", City: "上海市", Lat: 31.2304, Lng: 121.4737},
		{ID: 4, Name: "天津法院1", City: "天津市", Lat: 39.3434, Lng: 117.3616},
	}

	// 创建测试案件
	baseTime := time.Now()
	trials := []model.Trial{
		{ID: 1, CourtID: 1, StartTime: baseTime, EndTime: baseTime.Add(2 * time.Hour)},
		{ID: 2, CourtID: 2, StartTime: baseTime.Add(4 * time.Hour), EndTime: baseTime.Add(6 * time.Hour)},
		{ID: 3, CourtID: 1, StartTime: baseTime.Add(10 * time.Minute), EndTime: baseTime.Add(2*time.Hour + 10*time.Minute)}, // 时间间隔过短
		{ID: 4, CourtID: 1, StartTime: baseTime.Add(50 * time.Hour), EndTime: baseTime.Add(52 * time.Hour)},                 // 时间间隔过长
		{ID: 5, CourtID: 3, StartTime: baseTime.Add(8 * time.Hour), EndTime: baseTime.Add(10 * time.Hour)},                  // 跨城案件
		{ID: 6, CourtID: 4, StartTime: baseTime.Add(8 * time.Hour), EndTime: baseTime.Add(10 * time.Hour)},                  // 跨城远距离
	}

	// 创建测试路径 - 需要包含所有可能的路径组合
	routes := []model.Routes{
		// 律师到各法院的路径
		{FromLat: 39.9042, FromLng: 116.4074, ToLat: 39.9042, ToLng: 116.4074, Distance: 5000, Duration: 30, Price: 5000},     // 律师到北京法院1
		{FromLat: 39.9042, FromLng: 116.4074, ToLat: 39.9142, ToLng: 116.4174, Distance: 3000, Duration: 20, Price: 3000},     // 律师到北京法院2
		{FromLat: 39.9042, FromLng: 116.4074, ToLat: 31.2304, ToLng: 121.4737, Distance: 120000, Duration: 120, Price: 50000}, // 律师到上海法院
		{FromLat: 39.9042, FromLng: 116.4074, ToLat: 39.3434, ToLng: 117.3616, Distance: 200000, Duration: 240, Price: 80000}, // 律师到天津法院（远）

		// 法院间的路径
		{FromLat: 39.9042, FromLng: 116.4074, ToLat: 39.9142, ToLng: 116.4174, Distance: 2000, Duration: 15, Price: 2000},     // 北京法院1到法院2
		{FromLat: 39.9142, FromLng: 116.4174, ToLat: 39.9042, ToLng: 116.4074, Distance: 2000, Duration: 15, Price: 2000},     // 北京法院2到法院1
		{FromLat: 39.9042, FromLng: 116.4074, ToLat: 31.2304, ToLng: 121.4737, Distance: 120000, Duration: 120, Price: 50000}, // 北京法院1到上海法院
		{FromLat: 31.2304, FromLng: 121.4737, ToLat: 39.9042, ToLng: 116.4074, Distance: 120000, Duration: 120, Price: 50000}, // 上海法院到北京法院1
		{FromLat: 39.9142, FromLng: 116.4174, ToLat: 31.2304, ToLng: 121.4737, Distance: 120000, Duration: 120, Price: 50000}, // 北京法院2到上海法院
		{FromLat: 31.2304, FromLng: 121.4737, ToLat: 39.9142, ToLng: 116.4174, Distance: 120000, Duration: 120, Price: 50000}, // 上海法院到北京法院2

		// 法院到律师的返程路径
		{FromLat: 39.9042, FromLng: 116.4074, ToLat: 39.9042, ToLng: 116.4074, Distance: 5000, Duration: 30, Price: 5000},     // 北京法院1到律师
		{FromLat: 39.9142, FromLng: 116.4174, ToLat: 39.9042, ToLng: 116.4074, Distance: 3000, Duration: 20, Price: 3000},     // 北京法院2到律师
		{FromLat: 31.2304, FromLng: 121.4737, ToLat: 39.9042, ToLng: 116.4074, Distance: 120000, Duration: 120, Price: 50000}, // 上海法院到律师
		{FromLat: 39.3434, FromLng: 117.3616, ToLat: 39.9042, ToLng: 116.4074, Distance: 200000, Duration: 240, Price: 80000}, // 天津法院到律师
	}

	// 初始化调度器数据
	scheduler.initMaps(lawyers, courts, trials, routes)
	scheduler.initListsAndConflicts()

	return scheduler
}
