package schedule

import (
	"fmt"
	"sort"
	"time"
)

// validateNoTimeConflicts 验证方案是否没有时间冲突
func (s *GeneticAlgorithmScheduler) validateNoTimeConflicts(matching map[uint]uint) bool {
	// 按律师分组
	lawyerTrials := make(map[uint][]uint)
	for trialID, lawyerID := range matching {
		lawyerTrials[lawyerID] = append(lawyerTrials[lawyerID], trialID)
	}

	// 检查每个律师的案件是否有时间冲突
	for lawyerID, trials := range lawyerTrials {
		if len(trials) <= 1 {
			continue // 单个案件不会冲突
		}

		// 检查该律师的所有案件对
		for i := 0; i < len(trials); i++ {
			for j := i + 1; j < len(trials); j++ {
				if s.hasTimeConflict(lawyerID, trials[i], trials[j]) {
					return false
				}
			}
		}
	}

	return true
}

// hasTimeConflict 检查两个案件是否有时间冲突
func (s *GeneticAlgorithmScheduler) hasTimeConflict(lawyerID, trialID1, trialID2 uint) bool {
	trial1 := s.trialMap[trialID1]
	trial2 := s.trialMap[trialID2]

	if trial1 == nil || trial2 == nil {
		return true // 数据缺失，认为冲突
	}

	// 计算第一个案件的时间窗口
	start1, end1, err1 := s.calculateTravelTime(lawyerID, trial1)
	if err1 != nil {
		return true // 无法计算时间，认为冲突
	}

	// 计算第二个案件的时间窗口
	start2, end2, err2 := s.calculateTravelTime(lawyerID, trial2)
	if err2 != nil {
		return true // 无法计算时间，认为冲突
	}

	// 检查是否重叠
	return s.isTimeOverlap(start1, end1, start2, end2)
}

// validateSingleLawyerSchedule 验证单个律师的时间安排
func (s *GeneticAlgorithmScheduler) validateSingleLawyerSchedule(lawyerID uint, trials []uint) (bool, []string) {
	if len(trials) <= 1 {
		return true, []string{} // 单个案件不会冲突
	}

	errors := make([]string, 0)

	// 获取所有案件的时间窗口
	type timeWindow struct {
		trialID   uint
		startTime time.Time
		endTime   time.Time
	}

	windows := make([]timeWindow, 0, len(trials))
	for _, trialID := range trials {
		trial := s.trialMap[trialID]
		if trial == nil {
			errors = append(errors, fmt.Sprintf("案件 %d 数据缺失", trialID))
			continue
		}

		start, end, err := s.calculateTravelTime(lawyerID, trial)
		if err != nil {
			errors = append(errors, fmt.Sprintf("案件 %d 无法计算出行时间: %v", trialID, err))
			continue
		}

		windows = append(windows, timeWindow{
			trialID:   trialID,
			startTime: start,
			endTime:   end,
		})
	}

	// 检查所有时间窗口是否有重叠
	for i := 0; i < len(windows); i++ {
		for j := i + 1; j < len(windows); j++ {
			if s.isTimeOverlap(windows[i].startTime, windows[i].endTime,
				windows[j].startTime, windows[j].endTime) {
				errors = append(errors, fmt.Sprintf("案件 %d 和案件 %d 时间冲突：[%s-%s] vs [%s-%s]",
					windows[i].trialID, windows[j].trialID,
					windows[i].startTime.Format("15:04"), windows[i].endTime.Format("15:04"),
					windows[j].startTime.Format("15:04"), windows[j].endTime.Format("15:04")))
			}
		}
	}

	return len(errors) == 0, errors
}

// enforceNoTimeConflicts 强制执行无时间冲突
func (s *GeneticAlgorithmScheduler) enforceNoTimeConflicts(matching map[uint]uint) map[uint]uint {
	if len(matching) == 0 {
		return matching
	}

	// 按律师分组
	lawyerTrials := make(map[uint][]uint)
	for trialID, lawyerID := range matching {
		lawyerTrials[lawyerID] = append(lawyerTrials[lawyerID], trialID)
	}

	validMatching := make(map[uint]uint)

	// 为每个律师解决时间冲突
	for lawyerID, trials := range lawyerTrials {
		nonConflictingTrials := s.selectNonConflictingTrials(lawyerID, trials)
		for _, trialID := range nonConflictingTrials {
			validMatching[trialID] = lawyerID
		}
	}

	return validMatching
}

// selectNonConflictingTrials 选择无时间冲突的案件
func (s *GeneticAlgorithmScheduler) selectNonConflictingTrials(lawyerID uint, trials []uint) []uint {
	if len(trials) <= 1 {
		return trials
	}

	// 获取所有案件的时间和费用信息
	type trialInfo struct {
		trialID   uint
		startTime time.Time
		endTime   time.Time
		cost      float64
	}

	timeInfos := make([]trialInfo, 0, len(trials))
	for _, trialID := range trials {
		trial := s.trialMap[trialID]
		if trial == nil {
			continue
		}

		startTime, endTime, err := s.calculateTravelTime(lawyerID, trial)
		if err != nil {
			continue
		}

		cost := s.calculateAssignmentCost(trialID, lawyerID)
		timeInfos = append(timeInfos, trialInfo{
			trialID:   trialID,
			startTime: startTime,
			endTime:   endTime,
			cost:      cost,
		})
	}

	// 按费用排序（优先选择费用低的）
	sort.Slice(timeInfos, func(i, j int) bool {
		return timeInfos[i].cost < timeInfos[j].cost
	})

	// 贪心选择无冲突的案件
	selected := make([]uint, 0)
	selectedWindows := make([]trialInfo, 0)

	for _, info := range timeInfos {
		hasConflict := false
		for _, selectedInfo := range selectedWindows {
			if s.isTimeOverlap(info.startTime, info.endTime, selectedInfo.startTime, selectedInfo.endTime) {
				hasConflict = true
				break
			}
		}

		if !hasConflict {
			selected = append(selected, info.trialID)
			selectedWindows = append(selectedWindows, info)
		}
	}

	return selected
}

// performDetailedValidation 执行详细的时间冲突验证
func (s *GeneticAlgorithmScheduler) performDetailedValidation(matching map[uint]uint) (bool, []string) {
	errors := make([]string, 0)

	// 按律师分组
	lawyerTrials := make(map[uint][]uint)
	for trialID, lawyerID := range matching {
		lawyerTrials[lawyerID] = append(lawyerTrials[lawyerID], trialID)
	}

	// 检查每个律师的时间安排
	for lawyerID, trials := range lawyerTrials {
		lawyer := s.lawyerMap[lawyerID]
		if lawyer == nil {
			errors = append(errors, fmt.Sprintf("律师 %d 数据缺失", lawyerID))
			continue
		}

		isValid, lawyerErrors := s.validateSingleLawyerSchedule(lawyerID, trials)
		if !isValid {
			errors = append(errors, fmt.Sprintf("律师 %s (ID:%d) 存在时间冲突:", lawyer.Name, lawyerID))
			for _, err := range lawyerErrors {
				errors = append(errors, fmt.Sprintf("  - %s", err))
			}
		}
	}

	return len(errors) == 0, errors
}

// ContinuousTrialGroup 连续开庭组
type ContinuousTrialGroup struct {
	LawyerID  uint      // 律师ID
	Trials    []uint    // 连续的案件ID列表
	StartTime time.Time // 第一个案件开始时间
	EndTime   time.Time // 最后一个案件结束时间
	SameCity  bool      // 是否在同一城市
	TotalCost float64   // 总费用
}

// findContinuousTrialGroups 查找律师的连续开庭组
func (s *GeneticAlgorithmScheduler) findContinuousTrialGroups(matching map[uint]uint) []ContinuousTrialGroup {
	if !s.continuousTrialConfig.Enable {
		return nil
	}

	// 按律师分组案件
	lawyerTrials := make(map[uint][]uint)
	for trialID, lawyerID := range matching {
		lawyerTrials[lawyerID] = append(lawyerTrials[lawyerID], trialID)
	}

	var groups []ContinuousTrialGroup

	// 为每个律师查找连续开庭组
	for lawyerID, trials := range lawyerTrials {
		if len(trials) < 2 {
			continue
		}

		// 按时间排序案件
		sort.Slice(trials, func(i, j int) bool {
			return s.trialMap[trials[i]].StartTime.Before(s.trialMap[trials[j]].StartTime)
		})

		// 查找连续的案件组
		currentGroup := []uint{trials[0]}

		for i := 1; i < len(trials); i++ {
			if s.isTrialsContinuous(lawyerID, trials[i-1], trials[i]) {
				currentGroup = append(currentGroup, trials[i])
			} else {
				// 当前组结束，如果有多个案件则保存
				if len(currentGroup) >= 2 {
					group := s.createContinuousTrialGroup(lawyerID, currentGroup)
					groups = append(groups, group)
				}
				// 开始新组
				currentGroup = []uint{trials[i]}
			}
		}

		// 处理最后一组
		if len(currentGroup) >= 2 {
			group := s.createContinuousTrialGroup(lawyerID, currentGroup)
			groups = append(groups, group)
		}
	}

	return groups
}

// isTrialsContinuous 判断两个案件是否可以连续处理
func (s *GeneticAlgorithmScheduler) isTrialsContinuous(lawyerID, trial1ID, trial2ID uint) bool {
	trial1 := s.trialMap[trial1ID]
	trial2 := s.trialMap[trial2ID]

	if trial1 == nil || trial2 == nil {
		return false
	}

	// 计算时间间隔
	timeBetween := trial2.StartTime.Sub(trial1.EndTime)
	minInterval := time.Duration(s.continuousTrialConfig.MinTimeBetweenTrials) * time.Minute
	maxInterval := time.Duration(s.continuousTrialConfig.MaxTimeBetweenTrials) * time.Hour

	if timeBetween < minInterval || timeBetween > maxInterval {
		return false
	}

	// 检查是否在同一城市
	court1 := s.courtMap[trial1.CourtID]
	court2 := s.courtMap[trial2.CourtID]

	if court1 == nil || court2 == nil {
		return false
	}

	// 如果在同一城市，直接允许
	if court1.City == court2.City {
		return true
	}

	// 如果不在同一城市，检查出行时间是否在允许范围内
	routeKey := s.getRouteKey(court1.GetLocation(), court2.GetLocation())
	route, exists := s.routeMap[routeKey]
	if !exists {
		return false
	}

	maxTravelTime := time.Duration(s.continuousTrialConfig.MaxTravelTimeBetween) * time.Minute
	actualTravelTime := time.Duration(route.Duration) * time.Minute

	return actualTravelTime <= maxTravelTime
}

// createContinuousTrialGroup 创建连续开庭组
func (s *GeneticAlgorithmScheduler) createContinuousTrialGroup(lawyerID uint, trials []uint) ContinuousTrialGroup {
	if len(trials) == 0 {
		return ContinuousTrialGroup{}
	}

	firstTrial := s.trialMap[trials[0]]
	lastTrial := s.trialMap[trials[len(trials)-1]]

	// 检查是否在同一城市
	sameCity := true
	firstCourt := s.courtMap[firstTrial.CourtID]
	for i := 1; i < len(trials); i++ {
		trial := s.trialMap[trials[i]]
		court := s.courtMap[trial.CourtID]
		if court.City != firstCourt.City {
			sameCity = false
			break
		}
	}

	// 计算总费用（使用连续开庭的费用计算方式）
	totalCost := s.calculateContinuousTrialsCost(lawyerID, trials)

	return ContinuousTrialGroup{
		LawyerID:  lawyerID,
		Trials:    trials,
		StartTime: firstTrial.StartTime,
		EndTime:   lastTrial.EndTime,
		SameCity:  sameCity,
		TotalCost: totalCost,
	}
}
