package schedule

import (
	"math"
	"math/rand"
)

// Individual 个体
type Individual struct {
	Genes   []int // 基因：案件 i 分配给律师 genes[i]，-1表示未分配
	Fitness float64
}

// createRandomIndividual 创建随机个体
func (s *GeneticAlgorithmScheduler) createRandomIndividual() *Individual {
	numTrials := len(s.trialsList)
	numLawyers := len(s.lawyersList)
	genes := make([]int, numTrials)

	for i := 0; i < numTrials; i++ {
		if rand.Float64() < 0.6 { // 60%的概率分配给律师
			l := rand.Intn(numLawyers)
			if !math.IsInf(s.currentCosts[i][l], 1) {
				genes[i] = l
			} else {
				genes[i] = -1
			}
		} else {
			genes[i] = -1
		}
	}

	individual := &Individual{Genes: genes}
	s.evaluateIndividual(individual)
	return individual
}

// mutate 变异
func (s *GeneticAlgorithmScheduler) mutate(individual *Individual) {
	numTrials := len(individual.Genes)
	numLawyers := len(s.lawyersList)

	for i := 0; i < numTrials; i++ {
		if rand.Float64() < s.mutationRate {
			individual.Genes[i] = rand.Intn(numLawyers+1) - 1 // -1到numLawyers-1
		}
	}

	s.evaluateIndividual(individual)
}

// crossover 交叉
func (s *GeneticAlgorithmScheduler) crossover(parent1, parent2 *Individual) *Individual {
	numTrials := len(parent1.Genes)
	genes := make([]int, numTrials)

	for i := 0; i < numTrials; i++ {
		if rand.Float64() < 0.5 {
			genes[i] = parent1.Genes[i]
		} else {
			genes[i] = parent2.Genes[i]
		}
	}

	individual := &Individual{Genes: genes}
	s.evaluateIndividual(individual)
	return individual
}

// selectParent 选择父代（锦标赛选择）
func (s *GeneticAlgorithmScheduler) selectParent(population []*Individual) *Individual {
	tournamentSize := 3
	best := population[rand.Intn(len(population))]

	for i := 1; i < tournamentSize; i++ {
		candidate := population[rand.Intn(len(population))]
		if candidate.Fitness < best.Fitness {
			best = candidate
		}
	}

	return best
}

// extractMatching 从个体中提取匹配结果
func (s *GeneticAlgorithmScheduler) extractMatching(individual *Individual) map[uint]uint {
	matching := make(map[uint]uint)
	for t, l := range individual.Genes {
		if l >= 0 && l < len(s.lawyersList) {
			matching[s.trialsList[t]] = s.lawyersList[l]
		}
	}
	return matching
}