# 遗传算法调度器架构文档

## 概述

遗传算法调度器已经重构为模块化架构，将原来的单一大文件拆分为多个专门的文件，每个文件负责特定的功能领域。

## 文件结构和依赖关系

### 核心文件

#### 1. `genetic_algorithm.go` - 主调度器
**职责**: 核心调度逻辑、数据初始化、主要算法流程
**依赖**:
- `individual.go` (Individual 结构体和相关方法)
- `fitness.go` (适应度评估方法)
- `constraints.go` (约束检查和冲突处理)
- `config.go` (配置结构体和验证)
- `scheduler_interface.go` (接口定义)

**主要结构体**:
```go
type GeneticAlgorithmScheduler struct {
    // 核心数据
    mapModel             imap.MapModel
    lawyersList          []uint
    trialsList           []uint
    
    // GA参数
    populationSize       int
    generations          int
    mutationRate         float64
    crossoverRate        float64
    
    // 权重配置
    distanceWeight       float64
    costWeight           float64
    timeWeight           float64
    balanceWeight        float64
    
    // 数据映射
    lawyerMap map[uint]*model.Lawyer
    courtMap  map[uint]*model.Court
    trialMap  map[uint]*model.Trial
    routeMap  map[string]*model.Route
    
    // 优化数据结构
    conflicts map[string]bool  // 内存优化的冲突映射
    currentCosts [][]float64   // 预计算费用矩阵
}
```

**主要方法**:
- `NewGeneticAlgorithmScheduler()` - 构造函数
- `Schedule()` - 主调度方法
- `ScheduleWithCustomWeights()` - 自定义权重调度
- `runGeneticAlgorithm()` - 遗传算法核心逻辑

#### 2. `individual.go` - 个体和种群管理
**职责**: 遗传算法个体的创建、变异、交叉操作
**依赖**: 
- `genetic_algorithm.go` (GeneticAlgorithmScheduler 结构体)
- `config.go` (随机数种子管理)

**主要结构体**:
```go
type Individual struct {
    Genes   []int   // 基因：案件分配给律师的映射
    Fitness float64 // 适应度值
}
```

**主要方法**:
- `createRandomIndividual()` - 创建随机个体
- `mutate()` - 变异操作
- `crossover()` - 交叉操作
- `selectParent()` - 父代选择
- `extractMatching()` - 提取匹配结果

#### 3. `fitness.go` - 适应度评估
**职责**: 个体适应度计算、方案评估、指标计算
**依赖**:
- `genetic_algorithm.go` (调度器数据和方法)
- `constraints.go` (冲突检查方法)

**主要方法**:
- `evaluateIndividual()` - 评估个体适应度
- `calculateMetrics()` - 计算方案指标
- `calculateActualCost()` - 计算真实费用

**适应度计算逻辑**:
```go
// 适应度 = 基础费用 + 惩罚项 + 负载均衡费用 - 匹配奖励
fitness = totalCost + penalty + balanceCost - matchingBonus
```

#### 4. `constraints.go` - 约束检查和冲突处理
**职责**: 时间冲突检测、约束验证、冲突解决
**依赖**:
- `genetic_algorithm.go` (调度器数据和时间计算方法)

**主要方法**:
- `validateNoTimeConflicts()` - 验证无时间冲突
- `hasTimeConflict()` - 检查两个案件是否冲突
- `validateSingleLawyerSchedule()` - 验证单个律师时间安排
- `enforceNoTimeConflicts()` - 强制执行无冲突
- `selectNonConflictingTrials()` - 选择无冲突案件
- `performDetailedValidation()` - 详细验证

**冲突检测逻辑**:
```go
// 时间窗口计算：考虑出行时间和缓冲
startTime = trial.StartTime - travelTime * Buffer
endTime = trial.EndTime + travelTime * Buffer

// 冲突检测：时间窗口重叠
conflict = start1.Before(end2) && end1.After(start2)
```

#### 5. `config.go` - 配置管理
**职责**: 配置结构体定义、参数验证、随机数管理
**依赖**: 无（基础配置模块）

**主要结构体**:
```go
type GAWeightConfig struct {
    Name          string
    DistWeight    float64
    CostWeight    float64
    TimeWeight    float64
    BalanceWeight float64
}

type PerformanceMetrics struct {
    ExecutionTime    time.Duration
    GenerationsRun   int
    BestFitness      float64
    TotalSolutions   int
    ValidSolutions   int
}

type InternalState struct {
    LawyersCount    int
    CourtsCount     int
    TrialsCount     int
    RoutesCount     int
    ConflictsMatrix bool
    CostMatrix      bool
}
```

**主要方法**:
- `validateConfig()` - 验证GA配置
- `SetRandomSeed()` - 设置随机数种子
- `SetGAParameters()` - 设置GA参数
- `GetMemoryUsage()` - 获取内存使用情况
- `OptimizeForLargeDataset()` - 大数据集优化
- `ValidateDataIntegrity()` - 数据完整性验证

#### 6. `scheduler_interface.go` - 接口定义
**职责**: 定义调度器接口和数据结构
**依赖**: 无（接口定义模块）

**主要接口**:
```go
type Scheduler interface {
    Schedule() (ScheduleResult, error)
    ScheduleWithMetrics() (ScheduleResult, PerformanceMetrics, error)
    SetConfig(config SchedulerConfig) error
    GetMetrics() ScheduleMetrics
    ValidateConfig() error
}

type GeneticScheduler interface {
    Scheduler
    SetWeights(weights WeightConfig) error
    GetLastSolutions() []GAScheduleSolution
    SetRandomSeed(seed int64)
    GetInternalState() InternalState
}
```

**数据结构**:
- `ScheduleResult` - 调度结果
- `ScheduleMetadata` - 调度元数据
- `SchedulerConfig` - 调度器配置
- `ScheduleMetrics` - 调度指标

#### 7. `scheduler_impl.go` - 接口实现
**职责**: 实现新的调度器接口
**依赖**:
- `genetic_algorithm.go` (主调度器)
- `scheduler_interface.go` (接口定义)
- `config.go` (配置和验证方法)

**主要方法**:
- `ScheduleWithMetrics()` - 带性能指标的调度
- `SetConfig()` - 设置调度器配置
- `GetMetrics()` - 获取调度指标
- `SetWeights()` - 设置权重配置
- `ValidateConfig()` - 验证配置

## 依赖关系图

```
genetic_algorithm.go (核心)
├── individual.go (个体管理)
├── fitness.go (适应度评估)
├── constraints.go (约束检查)
├── config.go (配置管理)
└── scheduler_interface.go (接口定义)

scheduler_impl.go (接口实现)
├── genetic_algorithm.go
├── scheduler_interface.go
└── config.go

genetic_algorithm_test.go (测试)
├── genetic_algorithm.go
├── scheduler_impl.go
├── config.go
└── 所有其他模块
```

## 数据流

### 1. 初始化流程
```
NewGeneticAlgorithmScheduler()
├── initMaps() - 初始化数据映射
├── initListsAndConflicts() - 初始化冲突矩阵
└── 返回调度器实例
```

### 2. 调度流程
```
Schedule()
├── ScheduleWithCustomWeights()
│   ├── 设置权重配置
│   ├── 计算费用矩阵
│   ├── runGeneticAlgorithm()
│   │   ├── createRandomIndividual() (individual.go)
│   │   ├── evaluateIndividual() (fitness.go)
│   │   ├── selectParent() (individual.go)
│   │   ├── crossover() (individual.go)
│   │   └── mutate() (individual.go)
│   ├── extractMatching() (individual.go)
│   ├── enforceNoTimeConflicts() (constraints.go)
│   └── validateNoTimeConflicts() (constraints.go)
└── selectBestGASolutions()
```

### 3. 验证流程
```
validateNoTimeConflicts() (constraints.go)
├── hasTimeConflict() (constraints.go)
├── calculateTravelTime() (genetic_algorithm.go)
└── isTimeOverlap() (genetic_algorithm.go)
```

## 内存优化

### 1. 冲突矩阵优化
- **原来**: `[][][]bool` 三维数组
- **现在**: `map[string]bool` 哈希映射
- **优势**: 大幅减少内存使用，特别是稀疏数据

### 2. 键生成策略
```go
func getConflictKey(lawyerID uint, trial1, trial2 int) string {
    if trial1 > trial2 {
        trial1, trial2 = trial2, trial1 // 确保键的一致性
    }
    return fmt.Sprintf("%d:%d:%d", lawyerID, trial1, trial2)
}
```

## 错误处理改进

### 1. 安全费用计算
```go
type CostResult struct {
    Cost  float64
    Valid bool
    Error error
}
```

### 2. 配置验证
- 参数范围检查
- 数据完整性验证
- 详细错误消息

## 性能监控

### 1. 性能指标收集
- 执行时间
- 内存使用
- 方案质量指标

### 2. 数据集自适应优化
- 根据数据集大小调整GA参数
- 内存使用监控和优化建议

## 测试覆盖

### 1. 单元测试
- 配置验证测试
- 错误处理测试
- 内存优化测试

### 2. 集成测试
- 完整调度流程测试
- 性能指标测试
- 接口兼容性测试

## 扩展性

### 1. 接口设计
- 支持多种调度算法
- 可插拔的组件架构

### 2. 配置灵活性
- 多种权重配置
- 运行时参数调整

### 3. 并发支持
- 为未来并发处理预留接口
- 线程安全的随机数管理

## 总结

重构后的架构具有以下优势：

1. **模块化**: 每个文件职责明确，便于维护
2. **可扩展**: 接口设计支持未来功能扩展
3. **高效**: 内存优化和性能监控
4. **健壮**: 完善的错误处理和验证
5. **可测试**: 良好的测试覆盖和模块隔离

这种架构设计使得代码更加清晰、可维护，并为未来的功能扩展奠定了良好的基础。