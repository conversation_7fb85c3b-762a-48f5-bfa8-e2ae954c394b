package schedule

import (
	"external-tool/internal/config"
	"external-tool/internal/model"
	"external-tool/internal/model/imap"
	"fmt"
	"testing"
)

func newOptimizedBFS() BestSchedule {
	db := config.GetDB()
	mapModel := imap.NewMapModel("tencent")

	var lawyers []model.Lawyer
	db.Model(&model.Lawyer{}).Where("lat != 0 AND lng != 0 AND status = 1 AND formatted_address <> '广东省深圳市福田区深南大道6025'").Order("id ASC").Limit(10).Find(&lawyers)
	var courts []model.Court
	db.Model(&model.Court{}).Where("lat != 0 AND lng != 0 AND status = 1").Order("id ASC").Find(&courts)
	var trials []model.Trial
	db.Model(&model.Trial{}).Where("status = 1").Order("id ASC").Limit(5).Find(&trials)
	var routes []model.Routes
	db.Model(&model.Routes{}).Where("status = 1").Order("id ASC").Find(&routes)

	bfs := NewOptimizedBFS(mapModel, lawyers, courts, trials, routes)
	return bfs
}

func TestRealOptimizedBFS(t *testing.T) {
	bfs := newOptimizedBFS().(*OptimizedBFS)

	// 输出 NewOptimizedBFS 之后的值
	fmt.Printf("NewOptimizedBFS 返回的对象类型: %T\n", bfs)

	// 尝试转换为具体类型以便查看内部结构
	fmt.Printf("OptimizedBFS 结构体内容:\n")
	fmt.Printf("  mapModel: %T\n", bfs.mapModel)
	fmt.Printf("  lawyerMap 长度: %d\n", len(bfs.lawyerMap))
	fmt.Printf("  courtMap 长度: %d\n", len(bfs.courtMap))
	fmt.Printf("  trialMap 长度: %d\n", len(bfs.trialMap))
	fmt.Printf("  routeMap 长度: %d\n", len(bfs.routeMap))

	r := bfs.Schedule()
	fmt.Printf("OptimizedBFS Schedule 结果: %v\n", r)
}

func TestOptimizedBFSComparison(t *testing.T) {
	// 创建DFS实例
	dfs := newDFS().(*DFS)

	// 创建OptimizedBFS实例
	bfs := newOptimizedBFS().(*OptimizedBFS)

	fmt.Printf("===== DFS vs OptimizedBFS 对比测试 =====\n")

	// 运行DFS
	fmt.Printf("开始运行DFS...\n")
	dfsResult := dfs.Schedule()

	fmt.Printf("\n开始运行OptimizedBFS...\n")
	bfsResult := bfs.Schedule()

	// 比较结果
	fmt.Printf("\n===== 结果对比 =====\n")
	fmt.Printf("DFS 结果数量: %d\n", len(dfsResult))
	fmt.Printf("OptimizedBFS 结果数量: %d\n", len(bfsResult))

	// 比较最大匹配数量
	var dfsMaxMatches, bfsMaxMatches uint
	for matchCount := range dfsResult {
		if matchCount > dfsMaxMatches {
			dfsMaxMatches = matchCount
		}
	}
	for matchCount := range bfsResult {
		if matchCount > bfsMaxMatches {
			bfsMaxMatches = matchCount
		}
	}

	fmt.Printf("DFS 最大匹配数量: %d\n", dfsMaxMatches)
	fmt.Printf("OptimizedBFS 最大匹配数量: %d\n", bfsMaxMatches)

	// 输出详细的匹配数量分布
	fmt.Printf("\nDFS 匹配数量分布:\n")
	for matchCount := uint(0); matchCount <= dfsMaxMatches; matchCount++ {
		if schedules, exists := dfsResult[matchCount]; exists {
			fmt.Printf("  匹配数量 %d: %d 个方案\n", matchCount, len(schedules))
		}
	}

	fmt.Printf("\nOptimizedBFS 匹配数量分布:\n")
	for matchCount := uint(0); matchCount <= bfsMaxMatches; matchCount++ {
		if schedules, exists := bfsResult[matchCount]; exists {
			fmt.Printf("  匹配数量 %d: %d 个方案\n", matchCount, len(schedules))
		}
	}
}
