package schedule

import (
	"external-tool/internal/config"
	"external-tool/internal/model"
	"external-tool/internal/model/imap"
	"fmt"
	"testing"
	"time"
)

func TestEarlyTermination(t *testing.T) {
	// 创建一个小数据集来测试早期终止
	db := config.GetDB()
	mapModel := imap.NewMapModel("tencent")

	var lawyers []model.Lawyer
	db.Model(&model.Lawyer{}).Where("lat != 0 AND lng != 0 AND status = 1").Order("id ASC").Limit(8).Find(&lawyers)
	var courts []model.Court
	db.Model(&model.Court{}).Where("lat != 0 AND lng != 0 AND status = 1").Order("id ASC").Limit(3).Find(&courts)
	var trials []model.Trial
	db.Model(&model.Trial{}).Where("status = 1").Order("id ASC").Limit(5).Find(&trials)
	var routes []model.Routes
	db.Model(&model.Routes{}).Where("status = 1").Order("id ASC").Find(&routes)

	// 创建优化版本的 DFS
	optimizedDFS := NewOptimizedDFS(mapModel, lawyers, courts, trials, routes).(*OptimizedDFS)

	fmt.Printf("=== 早期终止测试 ===\n")
	fmt.Printf("律师数量: %d\n", len(optimizedDFS.lawyerMap))
	fmt.Printf("法院数量: %d\n", len(optimizedDFS.courtMap))
	fmt.Printf("庭审数量: %d\n", len(optimizedDFS.trialMap))
	fmt.Printf("路径数量: %d\n", len(optimizedDFS.routeMap))

	// 计算执行时间
	start := time.Now()
	result := optimizedDFS.Schedule()
	duration := time.Since(start)

	fmt.Printf("执行时间: %v\n", duration)
	fmt.Printf("最大匹配数量: %d\n", optimizedDFS.maxTotalMatches)
	fmt.Printf("总解决方案数量: %d\n", optimizedDFS.solutionCount)

	// 验证解决方案数量不超过20
	totalSolutions := 0
	for _, solutions := range result {
		totalSolutions += len(solutions)
	}
	fmt.Printf("实际返回的解决方案数量: %d\n", totalSolutions)

	// 验证解决方案数量不超过20
	if totalSolutions > 20 {
		t.Errorf("解决方案数量超过限制: 期望 <= 20, 实际 = %d", totalSolutions)
	}

	// 验证 solutionCount 不超过20
	if optimizedDFS.solutionCount > 20 {
		t.Errorf("solutionCount 超过限制: 期望 <= 20, 实际 = %d", optimizedDFS.solutionCount)
	}

	fmt.Printf("=== 早期终止测试完成 ===\n")
}

// 性能对比测试：比较有无早期终止的性能差异
func TestEarlyTerminationPerformance(t *testing.T) {
	// 创建一个较大的数据集
	db := config.GetDB()
	mapModel := imap.NewMapModel("tencent")

	var lawyers []model.Lawyer
	db.Model(&model.Lawyer{}).Where("lat != 0 AND lng != 0 AND status = 1").Order("id ASC").Limit(10).Find(&lawyers)
	var courts []model.Court
	db.Model(&model.Court{}).Where("lat != 0 AND lng != 0 AND status = 1").Order("id ASC").Limit(5).Find(&courts)
	var trials []model.Trial
	db.Model(&model.Trial{}).Where("status = 1").Order("id ASC").Limit(6).Find(&trials)
	var routes []model.Routes
	db.Model(&model.Routes{}).Where("status = 1").Order("id ASC").Find(&routes)

	// 创建优化版本的 DFS
	optimizedDFS := NewOptimizedDFS(mapModel, lawyers, courts, trials, routes).(*OptimizedDFS)

	fmt.Printf("=== 性能对比测试 ===\n")
	fmt.Printf("律师数量: %d\n", len(optimizedDFS.lawyerMap))
	fmt.Printf("法院数量: %d\n", len(optimizedDFS.courtMap))
	fmt.Printf("庭审数量: %d\n", len(optimizedDFS.trialMap))
	fmt.Printf("路径数量: %d\n", len(optimizedDFS.routeMap))

	// 计算执行时间
	start := time.Now()
	result := optimizedDFS.Schedule()
	duration := time.Since(start)

	fmt.Printf("执行时间: %v\n", duration)
	fmt.Printf("最大匹配数量: %d\n", optimizedDFS.maxTotalMatches)
	fmt.Printf("总解决方案数量: %d\n", optimizedDFS.solutionCount)

	// 验证解决方案数量
	totalSolutions := 0
	for _, solutions := range result {
		totalSolutions += len(solutions)
	}
	fmt.Printf("实际返回的解决方案数量: %d\n", totalSolutions)

	// 验证早期终止是否生效
	if optimizedDFS.solutionCount > 20 {
		t.Errorf("早期终止没有生效: solutionCount = %d > 20", optimizedDFS.solutionCount)
	}

	// 验证执行时间是否合理（应该比之前快很多）
	if duration > 5*time.Second {
		t.Logf("注意：执行时间较长 (%v)，可能需要进一步优化", duration)
	}

	fmt.Printf("=== 性能对比测试完成 ===\n")
}
