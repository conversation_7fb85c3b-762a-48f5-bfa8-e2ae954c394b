package schedule

import (
	"container/list"
	"external-tool/internal/model"
	"external-tool/internal/model/imap"
	"fmt"
	"time"
)

// ScheduleState 表示调度状态
type ScheduleState struct {
	assignment   map[uint][]uint // 律师ID -> 案件ID列表
	usedTrials   map[uint]bool   // 已使用的案件ID
	trialIndex   int             // 当前处理的案件索引
	totalMatches uint            // 当前匹配数量
}

type OptimizedBFS struct {
	mapModel        imap.MapModel
	lawyerMap       map[uint]*model.Lawyer
	courtMap        map[uint]*model.Court
	trialMap        map[uint]*model.Trial
	routeMap        map[string]*model.Route
	maxTotalMatches uint // 当前最大的匹配数量
	solutionCount   int  // 当前匹配数量的解决方案计数
}

func NewOptimizedBFS(mapModel imap.MapModel, lawyers []model.Lawyer, courts []model.Court, trials []model.Trial, routes []model.Routes) BestSchedule {
	bfs := &OptimizedBFS{}
	bfs.mapModel = mapModel
	bfs.InitMaps(lawyers, courts, trials, routes)
	return bfs
}

func (s *OptimizedBFS) key(from, to *model.Location) string {
	return fmt.Sprintf("%s:%s", from.String(), to.String())
}

func (s *OptimizedBFS) InitMaps(lawyers []model.Lawyer, courts []model.Court, trials []model.Trial, routes []model.Routes) {
	// 初始化律师映射
	s.lawyerMap = make(map[uint]*model.Lawyer)
	for _, lawyer := range lawyers {
		lawyer := lawyer // 创建局部副本
		s.lawyerMap[lawyer.ID] = &lawyer
	}

	// 初始化法院映射
	s.courtMap = make(map[uint]*model.Court)
	for _, court := range courts {
		court := court // 创建局部副本
		s.courtMap[court.ID] = &court
	}

	// 初始化案件映射
	s.trialMap = make(map[uint]*model.Trial)
	for _, trial := range trials {
		trial := trial // 创建局部副本
		s.trialMap[trial.ID] = &trial
	}

	s.routeMap = make(map[string]*model.Route)
	for _, route := range routes {
		route := route // 创建局部副本
		key := s.key(route.GetFromLocation(), route.GetToLocation())
		s.routeMap[key] = &model.Route{
			Distance: route.Distance,
			Duration: route.Duration,
			Price:    route.Price,
		}
	}
}

// Schedule 使用广度优先算法计算调度方案
func (s *OptimizedBFS) Schedule() map[uint][]map[uint]uint {
	result := make(map[uint][]map[uint]uint)

	// 初始化
	s.maxTotalMatches = 0
	s.solutionCount = 0

	if len(s.lawyerMap) == 0 || len(s.trialMap) == 0 {
		result[0] = []map[uint]uint{{}}
		return result
	}

	// 预处理：为每个律师创建可选的案件列表
	lawyerTrials := s.buildLawyerTrialMap()

	// 收集所有案件ID
	trialIDs := make([]uint, 0, len(s.trialMap))
	for trialID := range s.trialMap {
		trialIDs = append(trialIDs, trialID)
	}

	// 使用BFS进行调度
	s.bfsSchedule(trialIDs, lawyerTrials, result)

	// 如果没有找到任何匹配，添加空匹配
	if len(result) == 0 {
		result[0] = []map[uint]uint{{}}
	}

	// 只保留前10个结果
	s.limitResults(result, 10)

	// 输出结果
	s.printFinalResults(result)

	return result
}

// bfsSchedule 使用BFS进行调度
func (s *OptimizedBFS) bfsSchedule(trialIDs []uint, lawyerTrials map[uint][]uint, result map[uint][]map[uint]uint) {
	// 初始状态
	initialState := &ScheduleState{
		assignment:   make(map[uint][]uint),
		usedTrials:   make(map[uint]bool),
		trialIndex:   0,
		totalMatches: 0,
	}

	// 使用队列
	queue := list.New()
	queue.PushBack(initialState)

	for queue.Len() > 0 {
		// 取出队列前端的状态
		element := queue.Front()
		queue.Remove(element)
		currentState := element.Value.(*ScheduleState)

		// 如果已经处理完所有案件，保存结果
		if currentState.trialIndex >= len(trialIDs) {
			s.saveScheduleFromState(currentState, result)
			continue
		}

		currentTrialID := trialIDs[currentState.trialIndex]

		// 选项1：跳过当前案件
		skipState := s.copyState(currentState)
		skipState.trialIndex++
		queue.PushBack(skipState)

		// 选项2：分配当前案件给多个律师（修改：返回多个结果而不是只有一个）
		if !currentState.usedTrials[currentTrialID] {
			topLawyers := s.findTopLawyers(currentTrialID, lawyerTrials, currentState, 10)
			for _, lawyerID := range topLawyers {
				// 创建新状态
				newState := s.copyState(currentState)
				newState.trialIndex++
				newState.usedTrials[currentTrialID] = true
				newState.totalMatches++

				if newState.assignment[lawyerID] == nil {
					newState.assignment[lawyerID] = []uint{}
				}
				newState.assignment[lawyerID] = append(newState.assignment[lawyerID], currentTrialID)

				queue.PushBack(newState)
			}
		}

		// // 限制队列大小防止内存溢出
		// if queue.Len() > 10000 {
		// 	fmt.Printf("queue.Len() > 10000\n")
		// 	// 直接从队列前端移除多余元素，保留后 5000 个状态
		// 	elementsToRemove := queue.Len() - 5000
		// 	for i := 0; i < elementsToRemove; i++ {
		// 		queue.Remove(queue.Front())
		// 	}
		// }
	}
}

// findTopLawyers 找到处理指定案件的前N个最佳律师
func (s *OptimizedBFS) findTopLawyers(trialID uint, lawyerTrials map[uint][]uint, state *ScheduleState, topN int) []uint {
	availableLawyers := s.getLawyersForTrial(trialID, lawyerTrials)

	if len(availableLawyers) == 0 {
		return []uint{}
	}

	// 过滤时间兼容的律师
	compatibleLawyers := make([]uint, 0)
	for _, lawyerID := range availableLawyers {
		if s.isTimeCompatibleMulti(lawyerID, trialID, state.assignment) {
			compatibleLawyers = append(compatibleLawyers, lawyerID)
		}
	}

	if len(compatibleLawyers) == 0 {
		return []uint{}
	}

	// 按时间和分配数量排序，选择前topN个
	sortedLawyers := s.sortLawyersByTimeAndAssignments(compatibleLawyers, trialID, state.assignment)

	// 返回前topN个律师
	maxCount := topN
	if len(sortedLawyers) < maxCount {
		maxCount = len(sortedLawyers)
	}

	return sortedLawyers[:maxCount]
}

// copyState 复制状态
func (s *OptimizedBFS) copyState(original *ScheduleState) *ScheduleState {
	newState := &ScheduleState{
		assignment:   make(map[uint][]uint),
		usedTrials:   make(map[uint]bool),
		trialIndex:   original.trialIndex,
		totalMatches: original.totalMatches,
	}

	// 复制assignment
	for k, v := range original.assignment {
		newSlice := make([]uint, len(v))
		copy(newSlice, v)
		newState.assignment[k] = newSlice
	}

	// 复制usedTrials
	for k, v := range original.usedTrials {
		newState.usedTrials[k] = v
	}

	return newState
}

// saveScheduleFromState 从状态保存调度方案
func (s *OptimizedBFS) saveScheduleFromState(state *ScheduleState, result map[uint][]map[uint]uint) {
	totalMatches := state.totalMatches

	// 如果当前匹配数量小于最大值，直接返回
	if totalMatches < s.maxTotalMatches {
		return
	}

	// 如果找到了更大的匹配数量，清空结果并重置计数
	if totalMatches > s.maxTotalMatches {
		s.maxTotalMatches = totalMatches
		s.solutionCount = 0
		// 清空之前的结果
		for key := range result {
			delete(result, key)
		}
	}

	// 如果当前匹配数量的解决方案已经达到100个，返回
	if s.solutionCount >= 100 {
		return
	}

	// 如果这是第一个具有该匹配数量的方案，初始化数组
	if result[totalMatches] == nil {
		result[totalMatches] = make([]map[uint]uint, 0)
	}

	// 转换格式：从 map[律师ID][]开庭ID 转换为 map[开庭ID]律师ID
	trialAssignment := make(map[uint]uint)
	for lawyerID, trialIDs := range state.assignment {
		for _, trialID := range trialIDs {
			trialAssignment[trialID] = lawyerID
		}
	}

	// 检查是否已经存在相同的方案（避免重复）
	for _, existingSchedule := range result[totalMatches] {
		if s.isScheduleEqualNew(trialAssignment, existingSchedule) {
			return // 跳过重复的方案
		}
	}

	// 添加当前方案
	result[totalMatches] = append(result[totalMatches], trialAssignment)
	s.solutionCount++
}

// limitResults 限制结果数量
func (s *OptimizedBFS) limitResults(result map[uint][]map[uint]uint, limit int) {
	for matchCount, schedules := range result {
		if len(schedules) > limit {
			result[matchCount] = schedules[:limit]
		}
	}
}

// 以下方法直接复用DFS的实现
func (s *OptimizedBFS) buildLawyerTrialMap() map[uint][]uint {
	lawyerTrials := make(map[uint][]uint)

	// 为每个律师找到所有可行的案件
	for _, lawyer := range s.lawyerMap {
		var availableTrials []uint

		for _, trial := range s.trialMap {
			// 检查律师是否能够到达该法院（距离限制）
			if s.canLawyerReachCourt(lawyer.ID, trial.CourtID) {
				availableTrials = append(availableTrials, trial.ID)
			}
		}

		lawyerTrials[lawyer.ID] = availableTrials
	}

	return lawyerTrials
}

func (s *OptimizedBFS) canLawyerReachCourt(lawyerID, courtID uint) bool {
	lawyer := s.lawyerMap[lawyerID]
	court := s.courtMap[courtID]

	if lawyer == nil || court == nil {
		return false
	}

	key := s.key(lawyer.GetLocation(), court.GetLocation())
	route, ok := s.routeMap[key]
	if !ok {
		return false
	}

	return route.Available()
}

func (s *OptimizedBFS) getLawyersForTrial(trialID uint, lawyerTrials map[uint][]uint) []uint {
	var availableLawyers []uint

	for lawyerID, trials := range lawyerTrials {
		for _, trial := range trials {
			if trial == trialID {
				availableLawyers = append(availableLawyers, lawyerID)
				break
			}
		}
	}

	return availableLawyers
}

func (s *OptimizedBFS) sortLawyersByTimeAndAssignments(lawyerIDs []uint, trialID uint, currentAssignment map[uint][]uint) []uint {
	// 创建副本避免修改原始数据
	sortedLawyers := make([]uint, len(lawyerIDs))
	copy(sortedLawyers, lawyerIDs)

	// 获取开庭对应的法院
	trial := s.trialMap[trialID]
	if trial == nil {
		return sortedLawyers
	}

	// 按照分配数量优先，然后按时间排序
	for i := 0; i < len(sortedLawyers); i++ {
		for j := i + 1; j < len(sortedLawyers); j++ {
			lawyerI := sortedLawyers[i]
			lawyerJ := sortedLawyers[j]

			// 获取当前分配的案件数量
			trialsI := len(currentAssignment[lawyerI])
			trialsJ := len(currentAssignment[lawyerJ])

			// 先按分配数量排序（数量少的优先）
			if trialsI != trialsJ {
				if trialsI > trialsJ {
					sortedLawyers[i], sortedLawyers[j] = sortedLawyers[j], sortedLawyers[i]
				}
				continue
			}

			// 分配数量相等时，按路程时间排序
			durationI := s.getLawyerToCourtDuration(lawyerI, trial.CourtID)
			durationJ := s.getLawyerToCourtDuration(lawyerJ, trial.CourtID)

			// 处理不可达的情况（0）
			if durationI == 0 && durationJ != 0 {
				sortedLawyers[i], sortedLawyers[j] = sortedLawyers[j], sortedLawyers[i]
			} else if durationI != 0 && durationJ == 0 {
				continue
			} else if durationI != 0 && durationJ != 0 {
				// 都可达，按时间排序（时间短的优先）
				if durationI > durationJ {
					sortedLawyers[i], sortedLawyers[j] = sortedLawyers[j], sortedLawyers[i]
				}
			}
			// 都不可达的情况（durationI == 0 && durationJ == 0）保持原顺序
		}
	}

	return sortedLawyers
}

func (s *OptimizedBFS) isTimeCompatibleMulti(lawyerID, trialID uint, currentAssignment map[uint][]uint) bool {
	// 获取当前案件的时间和法院
	currentTrial := s.trialMap[trialID]
	if currentTrial == nil {
		return false
	}

	// 获取本次任务往返出行时间
	currentStart, currentEnd, err := s.getTravelTime(lawyerID, currentTrial)
	if err != nil {
		return false
	}

	// 检查是否与该律师已分配的其他案件时间冲突
	assignedTrials, exists := currentAssignment[lawyerID]
	if !exists {
		return true // 没有已分配的案件，不会冲突
	}

	for _, assignedTrialID := range assignedTrials {
		assignedTrial := s.trialMap[assignedTrialID]
		if assignedTrial == nil {
			return false
		}

		// 获取律师到已分配案件法院的出行时间
		assignedStart, assignedEnd, err := s.getTravelTime(lawyerID, assignedTrial)
		if err != nil {
			return false
		}

		// 检查时间是否重叠
		if s.isTimeOverlap(currentStart, currentEnd, assignedStart, assignedEnd) {
			return false
		}
	}

	return true
}

func (s *OptimizedBFS) getTravelTime(lawyerID uint, trial *model.Trial) (time.Time, time.Time, error) {
	emptyTime := time.Time{}
	lawyer := s.lawyerMap[lawyerID]
	if lawyer == nil {
		return emptyTime, emptyTime, fmt.Errorf("no route by missing lawyer %d", lawyerID)
	}

	court := s.courtMap[trial.CourtID]
	if court == nil {
		return emptyTime, emptyTime, fmt.Errorf("no route by missing court %d", trial.CourtID)
	}

	key := s.key(lawyer.GetLocation(), court.GetLocation())
	route, ok := s.routeMap[key]
	if !ok {
		return emptyTime, emptyTime, fmt.Errorf("no route by missing route %s", key)
	}

	// 使用实际的出行时间
	travelDuration := time.Duration(float64(route.Duration)*Buffer) * time.Minute
	startTime := trial.StartTime.Add(-travelDuration)
	endTime := trial.EndTime.Add(travelDuration)

	return startTime, endTime, nil
}

func (s *OptimizedBFS) isTimeOverlap(start1, end1, start2, end2 time.Time) bool {
	return start1.Before(end2) && start2.Before(end1)
}

func (s *OptimizedBFS) getLawyerToCourtDuration(lawyerID, courtID uint) int {
	lawyer := s.lawyerMap[lawyerID]
	court := s.courtMap[courtID]

	if lawyer == nil || court == nil {
		return 0
	}

	key := s.key(lawyer.GetLocation(), court.GetLocation())
	route, ok := s.routeMap[key]
	if !ok || !route.Available() {
		return 0
	}

	return route.Duration // 直接返回分钟
}

func (s *OptimizedBFS) isScheduleEqualNew(schedule1, schedule2 map[uint]uint) bool {
	if len(schedule1) != len(schedule2) {
		return false
	}

	for trialID, lawyerID1 := range schedule1 {
		lawyerID2, exists := schedule2[trialID]
		if !exists || lawyerID1 != lawyerID2 {
			return false
		}
	}

	return true
}

func (s *OptimizedBFS) calculateTotalDistanceAndDuration(trialAssignment map[uint]uint) (int, int) {
	totalDistance := 0
	totalDuration := 0

	for trialID, lawyerID := range trialAssignment {
		trial := s.trialMap[trialID]
		if trial == nil {
			continue
		}

		// 获取律师到法院的距离和时间
		distance := s.getLawyerToCourtDistance(lawyerID, trial.CourtID)
		duration := s.getLawyerToCourtDuration(lawyerID, trial.CourtID)

		// 跳过不可达的情况
		if distance == DistanceNotAvailable {
			continue
		}

		// 往返距离和时间
		totalDistance += distance * 2
		totalDuration += duration * 2

		// 加上庭审时间
		trialDuration := int(trial.EndTime.Sub(trial.StartTime).Minutes())
		totalDuration += trialDuration
	}

	return totalDistance, totalDuration
}

func (s *OptimizedBFS) getLawyerToCourtDistance(lawyerID, courtID uint) int {
	lawyer := s.lawyerMap[lawyerID]
	court := s.courtMap[courtID]

	if lawyer == nil || court == nil {
		return DistanceNotAvailable
	}

	key := s.key(lawyer.GetLocation(), court.GetLocation())
	route, ok := s.routeMap[key]
	if !ok || !route.Available() {
		return DistanceNotAvailable
	}

	return route.Distance
}

func (s *OptimizedBFS) printFinalResults(result map[uint][]map[uint]uint) {
	fmt.Printf("===== OptimizedBFS 调度完成 =====\n")

	if len(result) == 0 {
		fmt.Printf("未找到任何匹配方案\n")
		return
	}

	// 按匹配数量排序输出
	for matchCount := uint(0); matchCount <= s.maxTotalMatches; matchCount++ {
		schedules, exists := result[matchCount]
		if !exists || len(schedules) == 0 {
			continue
		}

		fmt.Printf("匹配数量 %d: 找到 %d 个方案\n", matchCount, len(schedules))

		// 输出每个方案的详细信息
		for i, schedule := range schedules {
			if matchCount == 0 {
				fmt.Printf("  方案 %d: 无匹配\n", i+1)
				continue
			}

			totalDistance, totalDuration := s.calculateTotalDistanceAndDuration(schedule)
			fmt.Printf("  方案 %d: %v 总路程: %d米 总时间: %d分钟\n",
				i+1, schedule, totalDistance, totalDuration)

			// 详细匹配信息已移除以简化输出
		}
	}

	fmt.Printf("===== 调度结果输出完成 =====\n")
}

// calculateSingleMatchDistanceAndDuration 计算单个匹配的距离和时间
func (s *OptimizedBFS) calculateSingleMatchDistanceAndDuration(trialID, lawyerID uint) (int, int) {
	trial := s.trialMap[trialID]
	if trial == nil {
		return 0, 0
	}

	// 获取律师到法院的距离和时间
	distance := s.getLawyerToCourtDistance(lawyerID, trial.CourtID)
	duration := s.getLawyerToCourtDuration(lawyerID, trial.CourtID)

	// 跳过不可达的情况
	if distance == DistanceNotAvailable {
		return 0, 0
	}

	// 往返距离和时间
	totalDistance := distance * 2
	totalDuration := duration * 2

	// 加上庭审时间
	trialDuration := int(trial.EndTime.Sub(trial.StartTime).Minutes())
	totalDuration += trialDuration

	return totalDistance, totalDuration
}

// printDetailedMatchInfo 输出单个匹配的详细信息 (已禁用以简化输出)
func (s *OptimizedBFS) printDetailedMatchInfo(trialID, lawyerID uint) {
	// 详细匹配信息输出已被移除以简化日志
}

// formatLocation 格式化位置信息
func (s *OptimizedBFS) formatLocation(location *model.Location) string {
	if location == nil {
		return "位置信息缺失"
	}
	return fmt.Sprintf("%.6f, %.6f", location.Lat, location.Lng)
}
