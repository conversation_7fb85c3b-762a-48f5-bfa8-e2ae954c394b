package schedule

import (
	"external-tool/internal/model"
	"external-tool/internal/model/imap"
	"fmt"
	"sort"
	"strings"
	"time"
)

// MaxMatchingScheduler 基于图论最大匹配算法的调度器
type MaxMatchingScheduler struct {
	mapModel        imap.MapModel
	lawyerMap       map[uint]*model.Lawyer
	courtMap        map[uint]*model.Court
	trialMap        map[uint]*model.Trial
	routeMap        map[string]*model.Route
	maxTotalMatches uint
	solutionCount   int

	// 图匹配相关
	bipartiteGraph map[uint][]uint // 案件ID -> 可处理的律师ID列表
	lawyerTrials   map[uint][]uint // 律师ID -> 可处理的案件ID列表

	// 枚举所有最大匹配的相关字段
	allMaxMatchings    []map[uint]uint // 所有找到的最大匹配
	maxMatchingSize    int             // 最大匹配的大小
	enableAllMatchings bool            // 是否启用枚举所有最大匹配
}

// NewMaxMatchingScheduler 创建新的图论匹配调度器
func NewMaxMatchingScheduler(mapModel imap.MapModel, lawyers []model.Lawyer, courts []model.Court, trials []model.Trial, routes []model.Routes) BestSchedule {
	scheduler := &MaxMatchingScheduler{}
	scheduler.mapModel = mapModel
	scheduler.InitMaps(lawyers, courts, trials, routes)
	return scheduler
}

// SetEnableAllMatchings 设置是否启用枚举所有最大匹配
func (s *MaxMatchingScheduler) SetEnableAllMatchings(enable bool) {
	s.enableAllMatchings = enable
	if enable {
		s.allMaxMatchings = make([]map[uint]uint, 0)
		s.maxMatchingSize = 0
	}
}

// GetAllMaxMatchings 获取所有找到的最大匹配
func (s *MaxMatchingScheduler) GetAllMaxMatchings() []map[uint]uint {
	return s.allMaxMatchings
}

// enumerateAllMaxMatchings 枚举所有可能的最大匹配
func (s *MaxMatchingScheduler) enumerateAllMaxMatchings() []map[uint]uint {
	if !s.enableAllMatchings {
		return nil
	}

	fmt.Printf("===== 开始枚举所有最大匹配 =====\n")

	// 先找到一个最大匹配以确定最大匹配大小
	trialIDs := s.getSortedTrialIDs()
	initialMatching := s.findMaximumMatching(trialIDs)
	s.maxMatchingSize = len(initialMatching)

	fmt.Printf("最大匹配大小: %d\n", s.maxMatchingSize)

	// 如果最大匹配大小为0，直接返回
	if s.maxMatchingSize == 0 {
		fmt.Printf("没有找到任何匹配，跳过枚举\n")
		return []map[uint]uint{}
	}

	// 重置结果存储
	s.allMaxMatchings = make([]map[uint]uint, 0)

	// 多起点增量枚举算法
	s.multiStartIncrementalEnumeration(trialIDs)

	fmt.Printf("找到 %d 个最大匹配方案\n", len(s.allMaxMatchings))
	fmt.Printf("===== 枚举完成 =====\n")

	return s.allMaxMatchings
}

// multiStartIncrementalEnumeration 多起点增量枚举算法
func (s *MaxMatchingScheduler) multiStartIncrementalEnumeration(trialIDs []uint) {
	seen := make(map[string]bool)

	// 方法1: 使用不同的排序策略生成多个初始匹配
	sortStrategies := []string{"time", "distance", "random1", "random2", "reverse"}

	for _, strategy := range sortStrategies {
		if len(s.allMaxMatchings) >= 30 {
			break
		}

		fmt.Printf("尝试策略: %s\n", strategy)
		initialMatching := s.findMaximumMatchingWithStrategy(trialIDs, strategy)

		if len(initialMatching) == s.maxMatchingSize {
			signature := s.generateMatchingSignature(initialMatching)
			if !seen[signature] {
				s.incrementalEnumeration(initialMatching, seen)
			}
		}
	}

	// 方法2: 基于已找到的匹配生成新的起点
	if len(s.allMaxMatchings) > 0 && len(s.allMaxMatchings) < 30 {
		fmt.Printf("基于现有匹配生成更多起点...\n")
		existingMatchings := make([]map[uint]uint, len(s.allMaxMatchings))
		copy(existingMatchings, s.allMaxMatchings)

		for _, matching := range existingMatchings {
			if len(s.allMaxMatchings) >= 30 {
				break
			}
			s.generateAdditionalStartPoints(matching, seen)
		}
	}
}

// findMaximumMatchingWithStrategy 使用不同策略找到最大匹配
func (s *MaxMatchingScheduler) findMaximumMatchingWithStrategy(trialIDs []uint, strategy string) map[uint]uint {
	// 根据策略排序案件
	sortedTrials := make([]uint, len(trialIDs))
	copy(sortedTrials, trialIDs)

	switch strategy {
	case "time":
		// 默认按时间排序（已实现）
	case "distance":
		// 按距离复杂度排序（可达律师数量少的优先）
		sort.Slice(sortedTrials, func(i, j int) bool {
			return len(s.bipartiteGraph[sortedTrials[i]]) < len(s.bipartiteGraph[sortedTrials[j]])
		})
	case "random1", "random2":
		// 使用伪随机排序
		seed := 42
		if strategy == "random2" {
			seed = 123
		}
		s.shuffleWithSeed(sortedTrials, seed)
	case "reverse":
		// 逆序
		for i := 0; i < len(sortedTrials)/2; i++ {
			j := len(sortedTrials) - 1 - i
			sortedTrials[i], sortedTrials[j] = sortedTrials[j], sortedTrials[i]
		}
	}

	return s.findMaximumMatching(sortedTrials)
}

// shuffleWithSeed 使用种子的伪随机洗牌
func (s *MaxMatchingScheduler) shuffleWithSeed(slice []uint, seed int) {
	// 简单的线性同余生成器
	rng := seed
	for i := len(slice) - 1; i > 0; i-- {
		rng = (rng*1664525 + 1013904223) % (1 << 31)
		j := rng % (i + 1)
		slice[i], slice[j] = slice[j], slice[i]
	}
}

// generateAdditionalStartPoints 基于现有匹配生成额外起点
func (s *MaxMatchingScheduler) generateAdditionalStartPoints(baseMatching map[uint]uint, seen map[string]bool) {
	// 对于每个案件，尝试分配给不同的律师
	for trialID, currentLawyerID := range baseMatching {
		availableLawyers := s.bipartiteGraph[trialID]

		for _, newLawyerID := range availableLawyers {
			if newLawyerID == currentLawyerID || len(s.allMaxMatchings) >= 30 {
				continue
			}

			// 创建新的起点匹配
			newMatching := make(map[uint]uint)
			for k, v := range baseMatching {
				newMatching[k] = v
			}
			newMatching[trialID] = newLawyerID

			// 检查新匹配是否有效
			if s.isValidMatching(newMatching) {
				signature := s.generateMatchingSignature(newMatching)
				if !seen[signature] {
					s.incrementalEnumeration(newMatching, seen)
				}
			}
		}
	}
}

// incrementalEnumeration 增量枚举算法：基于初始匹配生成所有最大匹配
func (s *MaxMatchingScheduler) incrementalEnumeration(initialMatching map[uint]uint, seen map[string]bool) {
	// seen 参数从外部传入以便多起点共享

	// 用于BFS遍历的队列
	queue := []map[uint]uint{initialMatching}

	for len(queue) > 0 && len(s.allMaxMatchings) < 30 {
		currentMatching := queue[0]
		queue = queue[1:]

		// 生成匹配的签名
		signature := s.generateMatchingSignature(currentMatching)
		if seen[signature] {
			continue
		}
		seen[signature] = true

		// 添加到结果中
		matchingCopy := make(map[uint]uint)
		for k, v := range currentMatching {
			matchingCopy[k] = v
		}
		s.allMaxMatchings = append(s.allMaxMatchings, matchingCopy)
		fmt.Printf("找到第 %d 个最大匹配方案\n", len(s.allMaxMatchings))

		// 生成邻居匹配（通过增强路径变换）
		neighbors := s.generateNeighborMatchings(currentMatching)
		for _, neighbor := range neighbors {
			neighborSig := s.generateMatchingSignature(neighbor)
			if !seen[neighborSig] && len(neighbor) == s.maxMatchingSize {
				queue = append(queue, neighbor)
			}
		}
	}
}

// generateMatchingSignature 生成匹配的唯一签名
func (s *MaxMatchingScheduler) generateMatchingSignature(matching map[uint]uint) string {
	// 将匹配转换为排序的字符串
	pairs := make([]string, 0, len(matching))
	for trialID, lawyerID := range matching {
		pairs = append(pairs, fmt.Sprintf("%d-%d", trialID, lawyerID))
	}

	// 排序以确保一致性
	sort.Strings(pairs)

	return fmt.Sprintf("[%s]", strings.Join(pairs, ","))
}

// generateNeighborMatchings 通过增强路径变换生成邻居匹配
func (s *MaxMatchingScheduler) generateNeighborMatchings(matching map[uint]uint) []map[uint]uint {
	neighbors := make([]map[uint]uint, 0)

	// 构建当前匹配的律师分配表
	currentAssignments := make(map[uint][]uint)
	for trialID, lawyerID := range matching {
		currentAssignments[lawyerID] = append(currentAssignments[lawyerID], trialID)
	}

	// 方法1: 交换律师分配
	neighbors = append(neighbors, s.generateSwapVariations(matching, currentAssignments)...)

	// 方法2: 替换单个分配
	neighbors = append(neighbors, s.generateReplacementVariations(matching, currentAssignments)...)

	return neighbors
}

// generateSwapVariations 通过交换律师分配生成变化
func (s *MaxMatchingScheduler) generateSwapVariations(matching map[uint]uint, currentAssignments map[uint][]uint) []map[uint]uint {
	variations := make([]map[uint]uint, 0)

	// 获取所有已分配的案件
	assignedTrials := make([]uint, 0, len(matching))
	for trialID := range matching {
		assignedTrials = append(assignedTrials, trialID)
	}

	// 尝试交换任意两个案件的律师分配
	for i := 0; i < len(assignedTrials); i++ {
		for j := i + 1; j < len(assignedTrials); j++ {
			trialA := assignedTrials[i]
			trialB := assignedTrials[j]
			lawyerA := matching[trialA]
			lawyerB := matching[trialB]

			// 创建交换后的匹配
			newMatching := make(map[uint]uint)
			for k, v := range matching {
				newMatching[k] = v
			}

			// 交换分配
			newMatching[trialA] = lawyerB
			newMatching[trialB] = lawyerA

			// 检查交换后的匹配是否有效
			if s.isValidMatching(newMatching) {
				variations = append(variations, newMatching)
			}
		}
	}

	return variations
}

// generateReplacementVariations 通过替换单个分配生成变化
func (s *MaxMatchingScheduler) generateReplacementVariations(matching map[uint]uint, currentAssignments map[uint][]uint) []map[uint]uint {
	variations := make([]map[uint]uint, 0)

	// 对每个已分配的案件，尝试分配给其他可用律师
	for trialID, currentLawyerID := range matching {
		availableLawyers := s.bipartiteGraph[trialID]

		for _, newLawyerID := range availableLawyers {
			if newLawyerID == currentLawyerID {
				continue // 跳过当前分配
			}

			// 创建新的分配
			newMatching := make(map[uint]uint)
			newAssignments := make(map[uint][]uint)

			// 复制当前匹配
			for k, v := range matching {
				newMatching[k] = v
			}
			for k, v := range currentAssignments {
				newAssignments[k] = make([]uint, len(v))
				copy(newAssignments[k], v)
			}

			// 移除旧分配
			s.removeTrialFromAssignments(trialID, currentLawyerID, newMatching, newAssignments)

			// 添加新分配
			newMatching[trialID] = newLawyerID
			if newAssignments[newLawyerID] == nil {
				newAssignments[newLawyerID] = make([]uint, 0)
			}
			newAssignments[newLawyerID] = append(newAssignments[newLawyerID], trialID)

			// 检查新匹配是否有效
			if s.isValidMatchingWithAssignments(newMatching, newAssignments) {
				variations = append(variations, newMatching)
			}
		}
	}

	return variations
}

// removeTrialFromAssignments 从分配中移除案件
func (s *MaxMatchingScheduler) removeTrialFromAssignments(trialID, lawyerID uint, matching map[uint]uint, assignments map[uint][]uint) {
	delete(matching, trialID)

	if trials, exists := assignments[lawyerID]; exists {
		for i, tid := range trials {
			if tid == trialID {
				assignments[lawyerID] = append(trials[:i], trials[i+1:]...)
				break
			}
		}
		if len(assignments[lawyerID]) == 0 {
			delete(assignments, lawyerID)
		}
	}
}

// isValidMatching 检查匹配是否有效（简化版）
func (s *MaxMatchingScheduler) isValidMatching(matching map[uint]uint) bool {
	// 构建律师分配表
	assignments := make(map[uint][]uint)
	for trialID, lawyerID := range matching {
		assignments[lawyerID] = append(assignments[lawyerID], trialID)
	}

	return s.isValidMatchingWithAssignments(matching, assignments)
}

// isValidMatchingWithAssignments 检查匹配和分配是否有效
func (s *MaxMatchingScheduler) isValidMatchingWithAssignments(matching map[uint]uint, assignments map[uint][]uint) bool {
	// 检查每个律师的分配是否存在时间冲突
	for lawyerID, trialIDs := range assignments {
		for _, trialID := range trialIDs {
			if !s.isTimeCompatibleMulti(lawyerID, trialID, assignments) {
				return false
			}
		}
	}

	return true
}

// key 生成路径的唯一键
func (s *MaxMatchingScheduler) key(from, to *model.Location) string {
	return fmt.Sprintf("%s:%s", from.String(), to.String())
}

// InitMaps 初始化各种映射表
func (s *MaxMatchingScheduler) InitMaps(lawyers []model.Lawyer, courts []model.Court, trials []model.Trial, routes []model.Routes) {
	// 初始化律师映射
	s.lawyerMap = make(map[uint]*model.Lawyer)
	for _, lawyer := range lawyers {
		lawyer := lawyer
		s.lawyerMap[lawyer.ID] = &lawyer
	}

	// 初始化法院映射
	s.courtMap = make(map[uint]*model.Court)
	for _, court := range courts {
		court := court
		s.courtMap[court.ID] = &court
	}

	// 初始化案件映射
	s.trialMap = make(map[uint]*model.Trial)
	for _, trial := range trials {
		trial := trial
		s.trialMap[trial.ID] = &trial
	}

	// 初始化路径映射
	s.routeMap = make(map[string]*model.Route)
	for _, route := range routes {
		route := route
		key := s.key(route.GetFromLocation(), route.GetToLocation())
		s.routeMap[key] = &model.Route{
			Distance: route.Distance,
			Duration: route.Duration,
			Price:    route.Price,
		}
	}

	// 构建二分图
	s.buildBipartiteGraph()
}

// buildBipartiteGraph 构建二分图，建立案件和律师之间的连接关系
func (s *MaxMatchingScheduler) buildBipartiteGraph() {
	s.bipartiteGraph = make(map[uint][]uint)
	s.lawyerTrials = make(map[uint][]uint)

	// 为每个案件找到所有可达的律师
	for _, trial := range s.trialMap {
		var availableLawyers []uint

		for _, lawyer := range s.lawyerMap {
			if s.canLawyerReachCourt(lawyer.ID, trial.CourtID) {
				availableLawyers = append(availableLawyers, lawyer.ID)
			}
		}

		s.bipartiteGraph[trial.ID] = availableLawyers
	}

	// 反向构建律师到案件的映射
	for trialID, lawyerIDs := range s.bipartiteGraph {
		for _, lawyerID := range lawyerIDs {
			if s.lawyerTrials[lawyerID] == nil {
				s.lawyerTrials[lawyerID] = []uint{}
			}
			s.lawyerTrials[lawyerID] = append(s.lawyerTrials[lawyerID], trialID)
		}
	}
}

// canLawyerReachCourt 检查律师是否能到达指定法院
func (s *MaxMatchingScheduler) canLawyerReachCourt(lawyerID, courtID uint) bool {
	lawyer := s.lawyerMap[lawyerID]
	court := s.courtMap[courtID]

	if lawyer == nil || court == nil {
		return false
	}

	key := s.key(lawyer.GetLocation(), court.GetLocation())
	route, ok := s.routeMap[key]
	if !ok {
		return false
	}

	return route.Available()
}

// Schedule 主调度方法，使用图论最大匹配算法
func (s *MaxMatchingScheduler) Schedule() map[uint][]map[uint]uint {
	result := make(map[uint][]map[uint]uint)
	s.maxTotalMatches = 0
	s.solutionCount = 0

	if len(s.lawyerMap) == 0 || len(s.trialMap) == 0 {
		result[0] = []map[uint]uint{{}}
		return result
	}

	// 构建二分图
	s.buildBipartiteGraph()

	// 如果启用了枚举所有最大匹配
	if s.enableAllMatchings {
		fmt.Printf("启用了枚举所有最大匹配模式\n")
		allMaxMatchings := s.enumerateAllMaxMatchings()

		// 将所有最大匹配保存到结果中
		for _, matching := range allMaxMatchings {
			s.saveMatching(matching, result)
		}
	} else {
		// 原有的单一最大匹配逻辑
		// 收集所有案件ID并按开庭时间排序
		trialIDs := s.getSortedTrialIDs()

		// 使用增强的匈牙利算法进行最大匹配
		bestMatching := s.findMaximumMatching(trialIDs)

		// 在最大匹配基础上进行负载均衡优化
		optimizedMatchings := s.optimizeLoadBalancing(bestMatching)

		// 保存结果
		for _, matching := range optimizedMatchings {
			s.saveMatching(matching, result)
		}
	}

	// 如果没有找到任何匹配，添加空匹配
	if len(result) == 0 {
		result[0] = []map[uint]uint{{}}
	}

	// 根据模式设置结果数量限制
	limit := 10
	if s.enableAllMatchings {
		limit = 50 // 枚举所有匹配时允许更多结果
	}
	s.limitResults(result, limit)

	// 输出最终结果
	s.printFinalResults(result)

	return result
}

// getSortedTrialIDs 获取按开庭时间排序的案件ID列表
func (s *MaxMatchingScheduler) getSortedTrialIDs() []uint {
	type trialTime struct {
		id   uint
		time time.Time
	}

	var trials []trialTime
	for trialID, trial := range s.trialMap {
		trials = append(trials, trialTime{id: trialID, time: trial.StartTime})
	}

	// 按开庭时间排序
	sort.Slice(trials, func(i, j int) bool {
		return trials[i].time.Before(trials[j].time)
	})

	trialIDs := make([]uint, len(trials))
	for i, trial := range trials {
		trialIDs[i] = trial.id
	}

	return trialIDs
}

// findMaximumMatching 使用增强的匈牙利算法找到最大匹配
func (s *MaxMatchingScheduler) findMaximumMatching(trialIDs []uint) map[uint]uint {
	// 当前最佳匹配：案件ID -> 律师ID
	bestMatching := make(map[uint]uint)

	// 每个律师当前分配的案件列表（用于时间冲突检查）
	lawyerAssignments := make(map[uint][]uint)

	// 逐个案件尝试匹配
	for _, trialID := range trialIDs {
		// 获取可处理该案件的律师列表
		availableLawyers := s.bipartiteGraph[trialID]

		// 按优先级排序律师
		sortedLawyers := s.sortLawyersByPriority(availableLawyers, trialID, lawyerAssignments)

		// 尝试为该案件找到合适的律师
		matched := false
		for _, lawyerID := range sortedLawyers {
			if s.canAssignTrialToLawyer(trialID, lawyerID, lawyerAssignments) {
				// 分配案件给律师
				bestMatching[trialID] = lawyerID
				if lawyerAssignments[lawyerID] == nil {
					lawyerAssignments[lawyerID] = []uint{}
				}
				lawyerAssignments[lawyerID] = append(lawyerAssignments[lawyerID], trialID)
				matched = true
				break
			}
		}

		// 如果直接分配失败，尝试通过重新分配已有案件来增加匹配
		if !matched {
			if s.tryAugmentingPath(trialID, availableLawyers, bestMatching, lawyerAssignments) {
				matched = true
			}
		}
	}

	return bestMatching
}

// sortLawyersByPriority 根据优先级排序律师
func (s *MaxMatchingScheduler) sortLawyersByPriority(lawyerIDs []uint, trialID uint, lawyerAssignments map[uint][]uint) []uint {
	if len(lawyerIDs) == 0 {
		return lawyerIDs
	}

	sortedLawyers := make([]uint, len(lawyerIDs))
	copy(sortedLawyers, lawyerIDs)

	trial := s.trialMap[trialID]
	if trial == nil {
		return sortedLawyers
	}

	// 排序优先级：1. 当前分配案件数 2. 到法院的距离
	sort.Slice(sortedLawyers, func(i, j int) bool {
		lawyerI := sortedLawyers[i]
		lawyerJ := sortedLawyers[j]

		// 当前分配的案件数量
		assignmentsI := len(lawyerAssignments[lawyerI])
		assignmentsJ := len(lawyerAssignments[lawyerJ])

		if assignmentsI != assignmentsJ {
			return assignmentsI < assignmentsJ // 分配少的优先
		}

		// 如果分配数量相同，按距离排序
		durationI := s.getLawyerToCourtDuration(lawyerI, trial.CourtID)
		durationJ := s.getLawyerToCourtDuration(lawyerJ, trial.CourtID)

		return durationI < durationJ // 距离近的优先
	})

	return sortedLawyers
}

// canAssignTrialToLawyer 检查是否可以将案件分配给律师
func (s *MaxMatchingScheduler) canAssignTrialToLawyer(trialID, lawyerID uint, lawyerAssignments map[uint][]uint) bool {
	// 检查时间兼容性
	return s.isTimeCompatibleMulti(lawyerID, trialID, lawyerAssignments)
}

// tryAugmentingPath 尝试通过增广路径增加匹配
func (s *MaxMatchingScheduler) tryAugmentingPath(trialID uint, availableLawyers []uint, matching map[uint]uint, lawyerAssignments map[uint][]uint) bool {
	// 记录已访问的律师，避免无限循环
	visited := make(map[uint]bool)

	// 尝试为每个可用律师找增广路径
	for _, lawyerID := range availableLawyers {
		if visited[lawyerID] {
			continue
		}

		if s.findAugmentingPath(trialID, lawyerID, matching, lawyerAssignments, visited) {
			return true
		}
	}

	return false
}

// findAugmentingPath 递归查找增广路径
func (s *MaxMatchingScheduler) findAugmentingPath(trialID, lawyerID uint, matching map[uint]uint, lawyerAssignments map[uint][]uint, visited map[uint]bool) bool {
	if visited[lawyerID] {
		return false
	}
	visited[lawyerID] = true

	// 如果律师没有时间冲突，直接分配
	if s.canAssignTrialToLawyer(trialID, lawyerID, lawyerAssignments) {
		// 分配案件给律师
		matching[trialID] = lawyerID
		if lawyerAssignments[lawyerID] == nil {
			lawyerAssignments[lawyerID] = []uint{}
		}
		lawyerAssignments[lawyerID] = append(lawyerAssignments[lawyerID], trialID)
		return true
	}

	// 如果有时间冲突，尝试重新分配律师的其他案件
	conflictingTrials := s.findConflictingTrials(trialID, lawyerID, lawyerAssignments)

	for _, conflictTrialID := range conflictingTrials {
		// 尝试为冲突的案件找到其他律师
		alternativeLawyers := s.bipartiteGraph[conflictTrialID]

		for _, altLawyerID := range alternativeLawyers {
			if altLawyerID == lawyerID {
				continue // 跳过当前律师
			}

			// 临时移除冲突案件
			s.removeTrialFromLawyer(conflictTrialID, lawyerID, matching, lawyerAssignments)

			// 尝试为冲突案件找新的律师
			if s.findAugmentingPath(conflictTrialID, altLawyerID, matching, lawyerAssignments, visited) {
				// 成功重新分配冲突案件，现在可以分配新案件
				matching[trialID] = lawyerID
				if lawyerAssignments[lawyerID] == nil {
					lawyerAssignments[lawyerID] = []uint{}
				}
				lawyerAssignments[lawyerID] = append(lawyerAssignments[lawyerID], trialID)
				return true
			}

			// 恢复原来的分配
			matching[conflictTrialID] = lawyerID
			lawyerAssignments[lawyerID] = append(lawyerAssignments[lawyerID], conflictTrialID)
		}
	}

	return false
}

// findConflictingTrials 找到与指定案件时间冲突的律师已分配案件
func (s *MaxMatchingScheduler) findConflictingTrials(trialID, lawyerID uint, lawyerAssignments map[uint][]uint) []uint {
	var conflictingTrials []uint

	currentTrial := s.trialMap[trialID]
	if currentTrial == nil {
		return conflictingTrials
	}

	currentStart, currentEnd, err := s.getTravelTime(lawyerID, currentTrial)
	if err != nil {
		return conflictingTrials
	}

	assignedTrials := lawyerAssignments[lawyerID]
	for _, assignedTrialID := range assignedTrials {
		assignedTrial := s.trialMap[assignedTrialID]
		if assignedTrial == nil {
			continue
		}

		assignedStart, assignedEnd, err := s.getTravelTime(lawyerID, assignedTrial)
		if err != nil {
			continue
		}

		if s.isTimeOverlap(currentStart, currentEnd, assignedStart, assignedEnd) {
			conflictingTrials = append(conflictingTrials, assignedTrialID)
		}
	}

	return conflictingTrials
}

// removeTrialFromLawyer 从律师的分配中移除指定案件
func (s *MaxMatchingScheduler) removeTrialFromLawyer(trialID, lawyerID uint, matching map[uint]uint, lawyerAssignments map[uint][]uint) {
	delete(matching, trialID)

	assignments := lawyerAssignments[lawyerID]
	for i, assignedTrialID := range assignments {
		if assignedTrialID == trialID {
			// 移除该案件
			lawyerAssignments[lawyerID] = append(assignments[:i], assignments[i+1:]...)
			break
		}
	}
}

// optimizeLoadBalancing 在最大匹配基础上进行负载均衡优化
func (s *MaxMatchingScheduler) optimizeLoadBalancing(matching map[uint]uint) []map[uint]uint {
	optimizedMatchings := []map[uint]uint{matching}

	// TODO: 实现负载均衡优化算法
	// 可以通过重新分配案件来平衡律师的工作负载和耗时

	return optimizedMatchings
}

// saveMatching 保存匹配结果
func (s *MaxMatchingScheduler) saveMatching(matching map[uint]uint, result map[uint][]map[uint]uint) {
	totalMatches := uint(len(matching))

	if totalMatches > s.maxTotalMatches {
		s.maxTotalMatches = totalMatches
		s.solutionCount = 0
		// 清空之前的结果
		for key := range result {
			delete(result, key)
		}
	}

	if totalMatches < s.maxTotalMatches {
		return
	}

	if s.solutionCount >= 100 {
		return
	}

	if result[totalMatches] == nil {
		result[totalMatches] = make([]map[uint]uint, 0)
	}

	// 检查是否已经存在相同的方案
	for _, existingSchedule := range result[totalMatches] {
		if s.isScheduleEqual(matching, existingSchedule) {
			return
		}
	}

	result[totalMatches] = append(result[totalMatches], matching)
	s.solutionCount++
}

// limitResults 限制结果数量
func (s *MaxMatchingScheduler) limitResults(result map[uint][]map[uint]uint, limit int) {
	for matchCount, schedules := range result {
		if len(schedules) > limit {
			result[matchCount] = schedules[:limit]
		}
	}
}

// 以下是辅助方法，复用 bfs.go 中的实现

func (s *MaxMatchingScheduler) isTimeCompatibleMulti(lawyerID, trialID uint, lawyerAssignments map[uint][]uint) bool {
	currentTrial := s.trialMap[trialID]
	if currentTrial == nil {
		return false
	}

	currentStart, currentEnd, err := s.getTravelTime(lawyerID, currentTrial)
	if err != nil {
		return false
	}

	assignedTrials, exists := lawyerAssignments[lawyerID]
	if !exists {
		return true
	}

	for _, assignedTrialID := range assignedTrials {
		assignedTrial := s.trialMap[assignedTrialID]
		if assignedTrial == nil {
			return false
		}

		assignedStart, assignedEnd, err := s.getTravelTime(lawyerID, assignedTrial)
		if err != nil {
			return false
		}

		if s.isTimeOverlap(currentStart, currentEnd, assignedStart, assignedEnd) {
			return false
		}
	}

	return true
}

func (s *MaxMatchingScheduler) getTravelTime(lawyerID uint, trial *model.Trial) (time.Time, time.Time, error) {
	emptyTime := time.Time{}
	lawyer := s.lawyerMap[lawyerID]
	if lawyer == nil {
		return emptyTime, emptyTime, fmt.Errorf("no route by missing lawyer %d", lawyerID)
	}

	court := s.courtMap[trial.CourtID]
	if court == nil {
		return emptyTime, emptyTime, fmt.Errorf("no route by missing court %d", trial.CourtID)
	}

	key := s.key(lawyer.GetLocation(), court.GetLocation())
	route, ok := s.routeMap[key]
	if !ok {
		return emptyTime, emptyTime, fmt.Errorf("no route by missing route %s", key)
	}

	travelDuration := time.Duration(float64(route.Duration)*Buffer) * time.Minute
	startTime := trial.StartTime.Add(-travelDuration)
	endTime := trial.EndTime.Add(travelDuration)

	return startTime, endTime, nil
}

func (s *MaxMatchingScheduler) isTimeOverlap(start1, end1, start2, end2 time.Time) bool {
	return start1.Before(end2) && start2.Before(end1)
}

func (s *MaxMatchingScheduler) getLawyerToCourtDuration(lawyerID, courtID uint) int {
	lawyer := s.lawyerMap[lawyerID]
	court := s.courtMap[courtID]

	if lawyer == nil || court == nil {
		return 0
	}

	key := s.key(lawyer.GetLocation(), court.GetLocation())
	route, ok := s.routeMap[key]
	if !ok || !route.Available() {
		return 0
	}

	return route.Duration
}

func (s *MaxMatchingScheduler) getLawyerToCourtDistance(lawyerID, courtID uint) int {
	lawyer := s.lawyerMap[lawyerID]
	court := s.courtMap[courtID]

	if lawyer == nil || court == nil {
		return DistanceNotAvailable
	}

	key := s.key(lawyer.GetLocation(), court.GetLocation())
	route, ok := s.routeMap[key]
	if !ok || !route.Available() {
		return DistanceNotAvailable
	}

	return route.Distance
}

func (s *MaxMatchingScheduler) isScheduleEqual(schedule1, schedule2 map[uint]uint) bool {
	if len(schedule1) != len(schedule2) {
		return false
	}

	for trialID, lawyerID1 := range schedule1 {
		lawyerID2, exists := schedule2[trialID]
		if !exists || lawyerID1 != lawyerID2 {
			return false
		}
	}

	return true
}

// CalculateTotalDistanceAndDuration 计算总距离和总时间（公有方法）
func (s *MaxMatchingScheduler) CalculateTotalDistanceAndDuration(trialAssignment map[uint]uint) (int, int) {
	return s.calculateTotalDistanceAndDuration(trialAssignment)
}

func (s *MaxMatchingScheduler) calculateTotalDistanceAndDuration(trialAssignment map[uint]uint) (int, int) {
	totalDistance := 0
	totalDuration := 0

	for trialID, lawyerID := range trialAssignment {
		trial := s.trialMap[trialID]
		if trial == nil {
			continue
		}

		distance := s.getLawyerToCourtDistance(lawyerID, trial.CourtID)
		duration := s.getLawyerToCourtDuration(lawyerID, trial.CourtID)

		if distance == DistanceNotAvailable {
			continue
		}

		totalDistance += distance * 2
		totalDuration += duration * 2

		trialDuration := int(trial.EndTime.Sub(trial.StartTime).Minutes())
		totalDuration += trialDuration
	}

	return totalDistance, totalDuration
}

func (s *MaxMatchingScheduler) printFinalResults(result map[uint][]map[uint]uint) {
	fmt.Printf("===== MaxMatchingScheduler (图论最大匹配) 调度完成 =====\n")

	if len(result) == 0 {
		fmt.Printf("未找到任何匹配方案\n")
		return
	}

	// 按匹配数量排序输出
	for matchCount := uint(0); matchCount <= s.maxTotalMatches; matchCount++ {
		schedules, exists := result[matchCount]
		if !exists || len(schedules) == 0 {
			continue
		}

		fmt.Printf("匹配数量 %d: 找到 %d 个方案\n", matchCount, len(schedules))

		// 输出每个方案的详细信息
		for i, schedule := range schedules {
			if matchCount == 0 {
				fmt.Printf("  方案 %d: 无匹配\n", i+1)
				continue
			}

			totalDistance, totalDuration := s.calculateTotalDistanceAndDuration(schedule)
			fmt.Printf("  方案 %d: %v 总路程: %d米 总时间: %d分钟\n",
				i+1, schedule, totalDistance, totalDuration)

			// 详细匹配信息已移除以简化输出

			// 计算负载均衡统计信息
			s.printLoadBalanceStats(schedule)
		}
	}

	fmt.Printf("===== 调度结果输出完成 =====\n")
}

func (s *MaxMatchingScheduler) printLoadBalanceStats(schedule map[uint]uint) {
	// 统计每个律师的工作负载
	lawyerWorkload := make(map[uint]int)
	lawyerTotalTime := make(map[uint]int)

	for trialID, lawyerID := range schedule {
		lawyerWorkload[lawyerID]++

		trial := s.trialMap[trialID]
		if trial != nil {
			// 计算该案件的总耗时
			travelTime := s.getLawyerToCourtDuration(lawyerID, trial.CourtID) * 2
			trialTime := int(trial.EndTime.Sub(trial.StartTime).Minutes())
			lawyerTotalTime[lawyerID] += travelTime + trialTime
		}
	}

	if len(lawyerWorkload) > 0 {
		fmt.Printf("    负载均衡统计:\n")
		for lawyerID, caseCount := range lawyerWorkload {
			lawyer := s.lawyerMap[lawyerID]
			lawyerName := fmt.Sprintf("律师%d", lawyerID)
			if lawyer != nil {
				lawyerName = lawyer.Name
			}
			fmt.Printf("      %s: %d个案件, %d分钟\n",
				lawyerName, caseCount, lawyerTotalTime[lawyerID])
		}
	}
}

// printDetailedMatchInfo 输出单个匹配的详细信息 (已禁用以简化输出)
func (s *MaxMatchingScheduler) printDetailedMatchInfo(trialID, lawyerID uint) {
	// 详细匹配信息输出已被移除以简化日志
}

// calculateSingleMatchDistanceAndDuration 计算单个匹配的距离和时间
func (s *MaxMatchingScheduler) calculateSingleMatchDistanceAndDuration(trialID, lawyerID uint) (int, int) {
	trial := s.trialMap[trialID]
	if trial == nil {
		return 0, 0
	}

	// 获取律师到法院的距离和时间
	distance := s.getLawyerToCourtDistance(lawyerID, trial.CourtID)
	duration := s.getLawyerToCourtDuration(lawyerID, trial.CourtID)

	// 跳过不可达的情况
	if distance == DistanceNotAvailable {
		return 0, 0
	}

	// 往返距离和时间
	totalDistance := distance * 2
	totalDuration := duration * 2

	// 加上庭审时间
	trialDuration := int(trial.EndTime.Sub(trial.StartTime).Minutes())
	totalDuration += trialDuration

	return totalDistance, totalDuration
}

// formatLocation 格式化位置信息
func (s *MaxMatchingScheduler) formatLocation(location *model.Location) string {
	if location == nil {
		return "位置信息缺失"
	}
	return fmt.Sprintf("%.6f, %.6f", location.Lat, location.Lng)
}
