package schedule

import (
	"external-tool/internal/model"
	"external-tool/internal/model/imap"
	"fmt"
	"strconv"
	"strings"
	"time"
)

// OptimizedDFS 优化版本的 DFS 实现
type OptimizedDFS struct {
	mapModel        imap.MapModel
	lawyerMap       map[uint]*model.Lawyer
	courtMap        map[uint]*model.Court
	trialMap        map[uint]*model.Trial
	routeMap        map[uint64]*model.Route // 使用数字键
	maxTotalMatches uint
	solutionCount   int

	// 缓存相关
	locationStringCache map[uint64]string // 位置字符串缓存
	distanceCache       map[uint64]int    // 距离缓存
	durationCache       map[uint64]int    // 时间缓存
	reachabilityCache   map[uint64]bool   // 可达性缓存

	// 预计算数据
	lawyerTrialsCache  map[uint][]uint // 律师可处理案件缓存
	sortedLawyersCache map[uint][]uint // 排序后的律师缓存
}

// NewOptimizedDFS 创建优化版本的 DFS
func NewOptimizedDFS(mapModel imap.MapModel, lawyers []model.Lawyer, courts []model.Court, trials []model.Trial, routes []model.Routes) BestSchedule {
	dfs := &OptimizedDFS{
		mapModel:            mapModel,
		locationStringCache: make(map[uint64]string),
		distanceCache:       make(map[uint64]int),
		durationCache:       make(map[uint64]int),
		reachabilityCache:   make(map[uint64]bool),
		lawyerTrialsCache:   make(map[uint][]uint),
		sortedLawyersCache:  make(map[uint][]uint),
	}

	dfs.InitMaps(lawyers, courts, trials, routes)
	dfs.PrecomputeData()

	return dfs
}

// locationKey 生成位置的数字键
func (d *OptimizedDFS) locationKey(location *model.Location) uint64 {
	if location == nil {
		return 0
	}

	// 将 float64 转换为 uint64，使用位操作
	latBits := uint64(location.Lat * 10000000) // 保留7位小数
	lngBits := uint64(location.Lng * 10000000) // 保留7位小数
	return (latBits << 32) | lngBits
}

// routeKey 生成路线的数字键
func (d *OptimizedDFS) routeKey(from, to *model.Location) uint64 {
	fromKey := d.locationKey(from)
	toKey := d.locationKey(to)

	// 使用位操作组合两个键
	if fromKey < toKey {
		return (fromKey << 32) | toKey
	}
	return (toKey << 32) | fromKey
}

// getCachedLocationString 获取缓存的位置字符串
func (d *OptimizedDFS) getCachedLocationString(location *model.Location) string {
	if location == nil {
		return ""
	}

	key := d.locationKey(location)
	if cached, exists := d.locationStringCache[key]; exists {
		return cached
	}

	// 使用更高效的字符串构建方式
	var builder strings.Builder
	builder.WriteString(strconv.FormatFloat(location.Lat, 'f', 7, 64))
	builder.WriteString(",")
	builder.WriteString(strconv.FormatFloat(location.Lng, 'f', 7, 64))

	result := builder.String()
	d.locationStringCache[key] = result
	return result
}

// InitMaps 初始化映射
func (d *OptimizedDFS) InitMaps(lawyers []model.Lawyer, courts []model.Court, trials []model.Trial, routes []model.Routes) {
	// 初始化律师映射
	d.lawyerMap = make(map[uint]*model.Lawyer)
	for _, lawyer := range lawyers {
		lawyer := lawyer
		d.lawyerMap[lawyer.ID] = &lawyer
	}

	// 初始化法院映射
	d.courtMap = make(map[uint]*model.Court)
	for _, court := range courts {
		court := court
		d.courtMap[court.ID] = &court
	}

	// 初始化案件映射
	d.trialMap = make(map[uint]*model.Trial)
	for _, trial := range trials {
		trial := trial
		d.trialMap[trial.ID] = &trial
	}

	// 初始化路线映射 - 使用数字键
	d.routeMap = make(map[uint64]*model.Route)
	for _, route := range routes {
		route := route
		key := d.routeKey(route.GetFromLocation(), route.GetToLocation())
		d.routeMap[key] = &model.Route{
			Distance: route.Distance,
			Duration: route.Duration,
			Price:    int(route.Cost),
		}
	}
}

// PrecomputeData 预计算数据
func (d *OptimizedDFS) PrecomputeData() {
	// 预计算律师可处理的案件
	d.buildLawyerTrialMap()

	// 预计算距离、时间和可达性
	d.precomputeDistanceAndDuration()
}

// buildLawyerTrialMap 构建律师到可选案件的映射
func (d *OptimizedDFS) buildLawyerTrialMap() {
	for _, lawyer := range d.lawyerMap {
		var availableTrials []uint

		for _, trial := range d.trialMap {
			if d.canLawyerReachCourtOptimized(lawyer.ID, trial.CourtID) {
				availableTrials = append(availableTrials, trial.ID)
			}
		}

		d.lawyerTrialsCache[lawyer.ID] = availableTrials
	}
}

// precomputeDistanceAndDuration 预计算距离和时间
func (d *OptimizedDFS) precomputeDistanceAndDuration() {
	for lawyerID, lawyer := range d.lawyerMap {
		for courtID, court := range d.courtMap {
			key := d.routeKey(lawyer.GetLocation(), court.GetLocation())

			if route, exists := d.routeMap[key]; exists && route.Available() {
				// 缓存距离
				d.distanceCache[d.lawyerCourtKey(lawyerID, courtID)] = route.Distance
				// 缓存时间
				d.durationCache[d.lawyerCourtKey(lawyerID, courtID)] = route.Duration
				// 缓存可达性
				d.reachabilityCache[d.lawyerCourtKey(lawyerID, courtID)] = true
			} else {
				// 缓存不可达
				d.distanceCache[d.lawyerCourtKey(lawyerID, courtID)] = DistanceNotAvailable
				d.durationCache[d.lawyerCourtKey(lawyerID, courtID)] = 0
				d.reachabilityCache[d.lawyerCourtKey(lawyerID, courtID)] = false
			}
		}
	}
}

// lawyerCourtKey 生成律师-法院组合的键
func (d *OptimizedDFS) lawyerCourtKey(lawyerID, courtID uint) uint64 {
	return (uint64(lawyerID) << 32) | uint64(courtID)
}

// canLawyerReachCourtOptimized 优化版本的可达性检查
func (d *OptimizedDFS) canLawyerReachCourtOptimized(lawyerID, courtID uint) bool {
	key := d.lawyerCourtKey(lawyerID, courtID)
	if reachable, exists := d.reachabilityCache[key]; exists {
		return reachable
	}

	// 如果缓存中没有，直接检查
	lawyer := d.lawyerMap[lawyerID]
	court := d.courtMap[courtID]
	if lawyer == nil || court == nil {
		return false
	}

	routeKey := d.routeKey(lawyer.GetLocation(), court.GetLocation())
	route, ok := d.routeMap[routeKey]
	if !ok {
		return false
	}

	return route.Available()
}

// getLawyerToCourtDistanceOptimized 优化版本的距离获取
func (d *OptimizedDFS) getLawyerToCourtDistanceOptimized(lawyerID, courtID uint) int {
	key := d.lawyerCourtKey(lawyerID, courtID)
	if distance, exists := d.distanceCache[key]; exists {
		return distance
	}
	return DistanceNotAvailable
}

// getLawyerToCourtDurationOptimized 优化版本的时间获取
func (d *OptimizedDFS) getLawyerToCourtDurationOptimized(lawyerID, courtID uint) int {
	key := d.lawyerCourtKey(lawyerID, courtID)
	if duration, exists := d.durationCache[key]; exists {
		return duration
	}
	return 0
}

// Schedule 优化版本的调度方法
func (d *OptimizedDFS) Schedule() map[uint][]map[uint]uint {
	result := make(map[uint][]map[uint]uint)

	d.maxTotalMatches = 0
	d.solutionCount = 0

	if len(d.lawyerMap) == 0 || len(d.trialMap) == 0 {
		result[0] = []map[uint]uint{{}}
		return result
	}

	// 收集所有案件ID
	trialIDs := make([]uint, 0, len(d.trialMap))
	for trialID := range d.trialMap {
		trialIDs = append(trialIDs, trialID)
	}

	// 使用优化版本的 DFS
	_ = d.dfsHelperOptimized(trialIDs, 0, make(map[uint][]uint), make(map[uint]bool), result)

	if len(result) == 0 {
		result[0] = []map[uint]uint{{}}
	}

	d.printFinalResults(result)
	return result
}

// dfsHelperOptimized 优化版本的 DFS 辅助方法
// 返回值：true 表示应该停止递归，false 表示继续递归
func (d *OptimizedDFS) dfsHelperOptimized(trialIDs []uint, trialIndex int,
	currentAssignment map[uint][]uint, usedTrials map[uint]bool,
	result map[uint][]map[uint]uint) bool {

	// 移除全局解决方案数量限制，让算法继续搜索更高匹配数量的解决方案

	if trialIndex >= len(trialIDs) {
		shouldStop := d.saveScheduleMultiOptimized(currentAssignment, result)
		return shouldStop
	}

	currentTrialID := trialIDs[trialIndex]

	// 选项1: 跳过当前案件
	shouldStop := d.dfsHelperOptimized(trialIDs, trialIndex+1, currentAssignment, usedTrials, result)
	if shouldStop {
		return true
	}

	// 选项2: 分配当前案件
	if !usedTrials[currentTrialID] {
		availableLawyers := d.lawyerTrialsCache[currentTrialID]
		if len(availableLawyers) == 0 {
			// 如果没有律师能处理这个案件，从其他律师中查找
			availableLawyers = d.getLawyersForTrialOptimized(currentTrialID)
		}

		// 使用缓存的排序结果
		sortedLawyers := d.getSortedLawyersOptimized(availableLawyers, currentTrialID, currentAssignment)

		for _, lawyerID := range sortedLawyers {
			if d.isTimeCompatibleOptimized(lawyerID, currentTrialID, currentAssignment) {
				// 创建新的状态 - 优化内存分配
				newAssignment := d.copyAssignmentOptimized(currentAssignment)
				newUsedTrials := d.copyUsedTrialsOptimized(usedTrials)

				if newAssignment[lawyerID] == nil {
					newAssignment[lawyerID] = []uint{}
				}
				newAssignment[lawyerID] = append(newAssignment[lawyerID], currentTrialID)
				newUsedTrials[currentTrialID] = true

				shouldStop := d.dfsHelperOptimized(trialIDs, trialIndex+1, newAssignment, newUsedTrials, result)
				if shouldStop {
					return true
				}
			}
		}
	}

	return false
}

// getLawyersForTrialOptimized 优化版本的律师查找
func (d *OptimizedDFS) getLawyersForTrialOptimized(trialID uint) []uint {
	var availableLawyers []uint

	for lawyerID := range d.lawyerTrialsCache {
		trials := d.lawyerTrialsCache[lawyerID]
		for _, trial := range trials {
			if trial == trialID {
				availableLawyers = append(availableLawyers, lawyerID)
				break
			}
		}
	}

	return availableLawyers
}

// getSortedLawyersOptimized 优化版本的律师排序
func (d *OptimizedDFS) getSortedLawyersOptimized(lawyerIDs []uint, trialID uint, currentAssignment map[uint][]uint) []uint {
	// 创建副本
	sortedLawyers := make([]uint, len(lawyerIDs))
	copy(sortedLawyers, lawyerIDs)

	trial := d.trialMap[trialID]
	if trial == nil {
		return d.sortLawyersByCurrentAssignmentsOptimized(sortedLawyers, currentAssignment)
	}

	// 使用插入排序，对于小数组更高效
	for i := 1; i < len(sortedLawyers); i++ {
		key := sortedLawyers[i]
		j := i - 1

		for j >= 0 && d.compareLawyers(sortedLawyers[j], key, trial.CourtID, currentAssignment) > 0 {
			sortedLawyers[j+1] = sortedLawyers[j]
			j--
		}
		sortedLawyers[j+1] = key
	}

	return sortedLawyers
}

// compareLawyers 比较两个律师的优先级
func (d *OptimizedDFS) compareLawyers(lawyer1, lawyer2 uint, courtID uint, currentAssignment map[uint][]uint) int {
	duration1 := d.getLawyerToCourtDurationOptimized(lawyer1, courtID)
	duration2 := d.getLawyerToCourtDurationOptimized(lawyer2, courtID)

	// 处理不可达的情况
	if duration1 == 0 && duration2 != 0 {
		return 1 // lawyer1 不可达，lawyer2 可达
	} else if duration1 != 0 && duration2 == 0 {
		return -1 // lawyer1 可达，lawyer2 不可达
	} else if duration1 == 0 && duration2 == 0 {
		// 都不可达，按当前分配数量排序
		trials1 := len(currentAssignment[lawyer1])
		trials2 := len(currentAssignment[lawyer2])
		return trials1 - trials2
	} else {
		// 都可达，按时间排序
		if duration1 != duration2 {
			return duration1 - duration2
		}
		// 时间相同，按分配数量排序
		trials1 := len(currentAssignment[lawyer1])
		trials2 := len(currentAssignment[lawyer2])
		return trials1 - trials2
	}
}

// sortLawyersByCurrentAssignmentsOptimized 优化版本的按分配数量排序
func (d *OptimizedDFS) sortLawyersByCurrentAssignmentsOptimized(lawyerIDs []uint, currentAssignment map[uint][]uint) []uint {
	// 使用插入排序
	for i := 1; i < len(lawyerIDs); i++ {
		key := lawyerIDs[i]
		j := i - 1

		trialsKey := len(currentAssignment[key])
		for j >= 0 {
			trialsJ := len(currentAssignment[lawyerIDs[j]])
			if trialsJ <= trialsKey {
				break
			}
			lawyerIDs[j+1] = lawyerIDs[j]
			j--
		}
		lawyerIDs[j+1] = key
	}

	return lawyerIDs
}

// isTimeCompatibleOptimized 优化版本的时间兼容性检查
func (d *OptimizedDFS) isTimeCompatibleOptimized(lawyerID, trialID uint, currentAssignment map[uint][]uint) bool {
	currentTrial := d.trialMap[trialID]
	if currentTrial == nil {
		return false
	}

	currentStart, currentEnd, err := d.getTravelTimeOptimized(lawyerID, currentTrial)
	if err != nil {
		return false
	}

	assignedTrials, exists := currentAssignment[lawyerID]
	if !exists {
		return true
	}

	for _, assignedTrialID := range assignedTrials {
		assignedTrial := d.trialMap[assignedTrialID]
		if assignedTrial == nil {
			return false
		}

		assignedStart, assignedEnd, err := d.getTravelTimeOptimized(lawyerID, assignedTrial)
		if err != nil {
			return false
		}

		if d.isTimeOverlap(currentStart, currentEnd, assignedStart, assignedEnd) {
			return false
		}
	}

	return true
}

// getTravelTimeOptimized 优化版本的出行时间计算
func (d *OptimizedDFS) getTravelTimeOptimized(lawyerID uint, trial *model.Trial) (time.Time, time.Time, error) {
	emptyTime := time.Time{}

	duration := d.getLawyerToCourtDurationOptimized(lawyerID, trial.CourtID)
	if duration == 0 {
		return emptyTime, emptyTime, fmt.Errorf("no route available")
	}

	travelDuration := time.Duration(float64(duration)*Buffer) * time.Minute
	startTime := trial.StartTime.Add(-travelDuration)
	endTime := trial.EndTime.Add(travelDuration)

	return startTime, endTime, nil
}

// isTimeOverlap 检查时间重叠
func (d *OptimizedDFS) isTimeOverlap(start1, end1, start2, end2 time.Time) bool {
	return start1.Before(end2) && start2.Before(end1)
}

// copyAssignmentOptimized 优化版本的分配复制
func (d *OptimizedDFS) copyAssignmentOptimized(original map[uint][]uint) map[uint][]uint {
	copyMap := make(map[uint][]uint, len(original))
	for k, v := range original {
		if len(v) > 0 {
			newSlice := make([]uint, len(v))
			copy(newSlice, v)
			copyMap[k] = newSlice
		}
	}
	return copyMap
}

// copyUsedTrialsOptimized 优化版本的已使用案件复制
func (d *OptimizedDFS) copyUsedTrialsOptimized(original map[uint]bool) map[uint]bool {
	copyMap := make(map[uint]bool, len(original))
	for k, v := range original {
		if v {
			copyMap[k] = v
		}
	}
	return copyMap
}

// saveScheduleMultiOptimized 优化版本的保存调度方案
// 返回值：true 表示应该停止递归，false 表示继续递归
func (d *OptimizedDFS) saveScheduleMultiOptimized(assignment map[uint][]uint, result map[uint][]map[uint]uint) bool {
	totalMatches := uint(0)
	for _, trials := range assignment {
		totalMatches += uint(len(trials))
	}

	if totalMatches < d.maxTotalMatches {
		return false
	}

	if totalMatches > d.maxTotalMatches {
		fmt.Printf("totalMatches: %d\n", totalMatches)
		d.maxTotalMatches = totalMatches
		d.solutionCount = 0
		for key := range result {
			delete(result, key)
		}
	}

	if result[totalMatches] == nil {
		result[totalMatches] = make([]map[uint]uint, 0)
	}

	// 如果当前匹配数量的解决方案已经达到100个，不再添加新的解决方案
	if len(result[totalMatches]) >= 100 {
		if d.maxTotalMatches == uint(len(d.trialMap)) {
			return true
		}
		return false // 但继续递归寻找更高匹配数量的解决方案
	}

	trialAssignment := make(map[uint]uint)
	for lawyerID, trialIDs := range assignment {
		for _, trialID := range trialIDs {
			trialAssignment[trialID] = lawyerID
		}
	}

	for _, existingSchedule := range result[totalMatches] {
		if d.isScheduleEqualOptimized(trialAssignment, existingSchedule) {
			return false
		}
	}

	result[totalMatches] = append(result[totalMatches], trialAssignment)
	d.solutionCount++

	// 不要在这里停止递归，让算法继续寻找更高匹配数量的解决方案
	return false
}

// isScheduleEqualOptimized 优化版本的调度方案比较
func (d *OptimizedDFS) isScheduleEqualOptimized(schedule1, schedule2 map[uint]uint) bool {
	if len(schedule1) != len(schedule2) {
		return false
	}

	for trialID, lawyerID1 := range schedule1 {
		if lawyerID2, exists := schedule2[trialID]; !exists || lawyerID1 != lawyerID2 {
			return false
		}
	}

	return true
}

// printFinalResults 输出最终结果
func (d *OptimizedDFS) printFinalResults(result map[uint][]map[uint]uint) {
	fmt.Printf("===== 优化版 DFS 调度完成 =====\n")

	if len(result) == 0 {
		fmt.Printf("未找到任何匹配方案\n")
		return
	}

	for matchCount := uint(0); matchCount <= d.maxTotalMatches; matchCount++ {
		schedules, exists := result[matchCount]
		if !exists || len(schedules) == 0 {
			continue
		}

		fmt.Printf("匹配数量 %d: 找到 %d 个方案\n", matchCount, len(schedules))

		for i, schedule := range schedules {
			if matchCount == 0 {
				fmt.Printf("  方案 %d: 无匹配\n", i+1)
				continue
			}

			totalDistance, totalDuration := d.calculateTotalDistanceAndDurationOptimized(schedule)
			fmt.Printf("  方案 %d: %v 总路程: %d米 总时间: %d分钟\n",
				i+1, schedule, totalDistance, totalDuration)
		}
	}

	fmt.Printf("===== 优化版调度结果输出完成 =====\n")
}

// calculateTotalDistanceAndDurationOptimized 优化版本的总距离和时间计算
func (d *OptimizedDFS) calculateTotalDistanceAndDurationOptimized(trialAssignment map[uint]uint) (int, int) {
	totalDistance := 0
	totalDuration := 0

	for trialID, lawyerID := range trialAssignment {
		trial := d.trialMap[trialID]
		if trial == nil {
			continue
		}

		distance := d.getLawyerToCourtDistanceOptimized(lawyerID, trial.CourtID)
		duration := d.getLawyerToCourtDurationOptimized(lawyerID, trial.CourtID)

		if distance == DistanceNotAvailable {
			continue
		}

		totalDistance += distance * 2
		totalDuration += duration * 2

		trialDuration := int(trial.EndTime.Sub(trial.StartTime).Minutes())
		totalDuration += trialDuration
	}

	return totalDistance, totalDuration
}
