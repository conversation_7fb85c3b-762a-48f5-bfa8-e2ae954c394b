package schedule

import (
	"external-tool/internal/config"
	"external-tool/internal/model"
	"external-tool/internal/model/imap"
	"fmt"
	"os"
	"runtime"
	"runtime/pprof"
	"testing"
	"time"
)

// 创建测试数据
func createTestData() ([]model.Lawyer, []model.Court, []model.Trial, []model.Routes) {
	db := config.GetDB()

	var lawyers []model.Lawyer
	db.Model(&model.Lawyer{}).Where("lat != 0 AND lng != 0 AND status = 1 AND formatted_address <> '广东省深圳市福田区深南大道6025'").Order("id ASC").Limit(20).Find(&lawyers)

	var courts []model.Court
	db.Model(&model.Court{}).Where("lat != 0 AND lng != 0 AND status = 1").Order("id ASC").Find(&courts)

	var trials []model.Trial
	db.Model(&model.Trial{}).Where("status = 1").Order("id ASC").Limit(10).Find(&trials)

	var routes []model.Routes
	db.Model(&model.Routes{}).Where("status = 1").Order("id ASC").Find(&routes)

	return lawyers, courts, trials, routes
}

// 测试原始DFS性能
func TestOriginalDFSPerformance(t *testing.T) {
	// 生成带时间戳的文件名
	cpuFileName := "./pprof/cpu_profile.prof"
	memFileName := "./pprof/mem_profile.prof"

	// 创建 CPU profile 文件
	cpuFile, err := os.Create(cpuFileName)
	if err != nil {
		t.Fatalf("无法创建 CPU profile 文件: %v", err)
	}
	defer cpuFile.Close()

	// 开始 CPU profiling
	if err := pprof.StartCPUProfile(cpuFile); err != nil {
		t.Fatalf("无法启动 CPU profile: %v", err)
	}
	defer pprof.StopCPUProfile()

	// 获取测试数据
	lawyers, courts, trials, routes := createTestData()

	// 创建原始DFS
	mapModel := imap.NewMapModel("tencent")
	dfs := NewDFS(mapModel, lawyers, courts, trials, routes).(*DFS)

	// 输出基本信息
	fmt.Printf("=== 原始 DFS 性能测试 ===\n")
	fmt.Printf("时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Printf("律师数量: %d\n", len(dfs.lawyerMap))
	fmt.Printf("法院数量: %d\n", len(dfs.courtMap))
	fmt.Printf("庭审数量: %d\n", len(dfs.trialMap))
	fmt.Printf("路径数量: %d\n", len(dfs.routeMap))

	// 记录内存使用情况
	var m1 runtime.MemStats
	runtime.ReadMemStats(&m1)
	fmt.Printf("初始内存使用: %d KB\n", m1.Alloc/1024)

	// 测量执行时间
	start := time.Now()
	r := dfs.Schedule()
	duration := time.Since(start)

	// 记录最终内存使用情况
	var m2 runtime.MemStats
	runtime.ReadMemStats(&m2)
	fmt.Printf("最终内存使用: %d KB\n", m2.Alloc/1024)
	fmt.Printf("内存增量: %d KB\n", (m2.Alloc-m1.Alloc)/1024)

	fmt.Printf("执行时间: %v\n", duration)
	fmt.Printf("结果数量: %d\n", len(r))

	// 创建内存 profile 文件
	memFile, err := os.Create(memFileName)
	if err != nil {
		t.Fatalf("无法创建内存 profile 文件: %v", err)
	}
	defer memFile.Close()

	// 记录内存 profile
	if err := pprof.WriteHeapProfile(memFile); err != nil {
		t.Fatalf("无法写入内存 profile: %v", err)
	}

	// 输出 profile 文件信息
	fmt.Printf("=== 原始版本 Profile 文件已生成 ===\n")
	fmt.Printf("CPU Profile: %s\n", cpuFileName)
	fmt.Printf("Memory Profile: %s\n", memFileName)
}

// 测试优化版DFS性能
func TestOptimizedDFSPerformance(t *testing.T) {
	// 生成带时间戳的文件名
	cpuFileName := "./pprof/cpu_profile_optimized.prof"
	memFileName := "./pprof/mem_profile_optimized.prof"

	// 创建 CPU profile 文件
	cpuFile, err := os.Create(cpuFileName)
	if err != nil {
		t.Fatalf("无法创建 CPU profile 文件: %v", err)
	}
	defer cpuFile.Close()

	// 开始 CPU profiling
	if err := pprof.StartCPUProfile(cpuFile); err != nil {
		t.Fatalf("无法启动 CPU profile: %v", err)
	}
	defer pprof.StopCPUProfile()

	// 获取测试数据
	lawyers, courts, trials, routes := createTestData()

	// 创建优化版DFS
	mapModel := imap.NewMapModel("tencent")
	dfs := NewOptimizedDFS(mapModel, lawyers, courts, trials, routes).(*OptimizedDFS)

	// 输出基本信息
	fmt.Printf("=== 优化版 DFS 性能测试 ===\n")
	fmt.Printf("时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Printf("律师数量: %d\n", len(dfs.lawyerMap))
	fmt.Printf("法院数量: %d\n", len(dfs.courtMap))
	fmt.Printf("庭审数量: %d\n", len(dfs.trialMap))
	fmt.Printf("路径数量: %d\n", len(dfs.routeMap))

	// 记录内存使用情况
	var m1 runtime.MemStats
	runtime.ReadMemStats(&m1)
	fmt.Printf("初始内存使用: %d KB\n", m1.Alloc/1024)

	// 测量执行时间
	start := time.Now()
	r := dfs.Schedule()
	duration := time.Since(start)

	// 记录最终内存使用情况
	var m2 runtime.MemStats
	runtime.ReadMemStats(&m2)
	fmt.Printf("最终内存使用: %d KB\n", m2.Alloc/1024)
	fmt.Printf("内存增量: %d KB\n", (m2.Alloc-m1.Alloc)/1024)

	fmt.Printf("执行时间: %v\n", duration)
	fmt.Printf("结果数量: %d\n", len(r))

	// 创建内存 profile 文件
	memFile, err := os.Create(memFileName)
	if err != nil {
		t.Fatalf("无法创建内存 profile 文件: %v", err)
	}
	defer memFile.Close()

	// 记录内存 profile
	if err := pprof.WriteHeapProfile(memFile); err != nil {
		t.Fatalf("无法写入内存 profile: %v", err)
	}

	// 输出 profile 文件信息
	fmt.Printf("=== 优化版本 Profile 文件已生成 ===\n")
	fmt.Printf("CPU Profile: %s\n", cpuFileName)
	fmt.Printf("Memory Profile: %s\n", memFileName)
}

// 比较性能测试
func TestPerformanceComparison(t *testing.T) {
	// 获取测试数据
	lawyers, courts, trials, routes := createTestData()
	mapModel := imap.NewMapModel("tencent")

	fmt.Printf("=== 性能对比测试 ===\n")
	fmt.Printf("测试数据: 律师%d, 法院%d, 庭审%d, 路径%d\n",
		len(lawyers), len(courts), len(trials), len(routes))

	// 测试原始版本
	fmt.Printf("\n--- 原始版本 ---\n")
	originalDFS := NewDFS(mapModel, lawyers, courts, trials, routes).(*DFS)

	var m1 runtime.MemStats
	runtime.ReadMemStats(&m1)
	originalStart := time.Now()
	originalResult := originalDFS.Schedule()
	originalDuration := time.Since(originalStart)
	var m2 runtime.MemStats
	runtime.ReadMemStats(&m2)
	originalMemory := m2.Alloc - m1.Alloc

	fmt.Printf("执行时间: %v\n", originalDuration)
	fmt.Printf("内存使用: %d KB\n", originalMemory/1024)
	fmt.Printf("结果数量: %d\n", len(originalResult))

	// 测试优化版本
	fmt.Printf("\n--- 优化版本 ---\n")
	optimizedDFS := NewOptimizedDFS(mapModel, lawyers, courts, trials, routes).(*OptimizedDFS)

	var m3 runtime.MemStats
	runtime.ReadMemStats(&m3)
	optimizedStart := time.Now()
	optimizedResult := optimizedDFS.Schedule()
	optimizedDuration := time.Since(optimizedStart)
	var m4 runtime.MemStats
	runtime.ReadMemStats(&m4)
	optimizedMemory := m4.Alloc - m3.Alloc

	fmt.Printf("执行时间: %v\n", optimizedDuration)
	fmt.Printf("内存使用: %d KB\n", optimizedMemory/1024)
	fmt.Printf("结果数量: %d\n", len(optimizedResult))

	// 性能提升对比
	fmt.Printf("\n--- 性能提升 ---\n")
	if originalDuration > 0 {
		speedup := float64(originalDuration) / float64(optimizedDuration)
		fmt.Printf("速度提升: %.2fx\n", speedup)
	}

	if originalMemory > 0 {
		memoryReduction := float64(originalMemory-optimizedMemory) / float64(originalMemory) * 100
		fmt.Printf("内存节省: %.1f%%\n", memoryReduction)
	}

	// 验证结果一致性
	fmt.Printf("\n--- 结果验证 ---\n")
	if len(originalResult) == len(optimizedResult) {
		fmt.Printf("✓ 结果数量一致\n")
	} else {
		fmt.Printf("✗ 结果数量不一致: 原始%d vs 优化%d\n", len(originalResult), len(optimizedResult))
	}

	// 简单的结果内容验证
	consistent := true
	for matchCount, originalSchedules := range originalResult {
		if optimizedSchedules, exists := optimizedResult[matchCount]; exists {
			if len(originalSchedules) != len(optimizedSchedules) {
				consistent = false
				fmt.Printf("✗ 匹配数量%d的方案数不一致: 原始%d vs 优化%d\n",
					matchCount, len(originalSchedules), len(optimizedSchedules))
			}
		} else {
			consistent = false
			fmt.Printf("✗ 优化版本缺少匹配数量%d的结果\n", matchCount)
		}
	}

	if consistent {
		fmt.Printf("✓ 结果内容基本一致\n")
	}
}

// 基准测试 - 原始版本
func BenchmarkOriginalDFS(b *testing.B) {
	lawyers, courts, trials, routes := createTestData()
	mapModel := imap.NewMapModel("tencent")

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		dfs := NewDFS(mapModel, lawyers, courts, trials, routes)
		_ = dfs.Schedule()
	}
}

// 基准测试 - 优化版本
func BenchmarkOptimizedDFS(b *testing.B) {
	lawyers, courts, trials, routes := createTestData()
	mapModel := imap.NewMapModel("tencent")

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		dfs := NewOptimizedDFS(mapModel, lawyers, courts, trials, routes)
		_ = dfs.Schedule()
	}
}

// 内存基准测试 - 原始版本
func BenchmarkOriginalDFSMemory(b *testing.B) {
	lawyers, courts, trials, routes := createTestData()
	mapModel := imap.NewMapModel("tencent")

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		dfs := NewDFS(mapModel, lawyers, courts, trials, routes)
		_ = dfs.Schedule()
	}
}

// 内存基准测试 - 优化版本
func BenchmarkOptimizedDFSMemory(b *testing.B) {
	lawyers, courts, trials, routes := createTestData()
	mapModel := imap.NewMapModel("tencent")

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		dfs := NewOptimizedDFS(mapModel, lawyers, courts, trials, routes)
		_ = dfs.Schedule()
	}
}
