package schedule

import (
	"external-tool/internal/config"
	"external-tool/internal/model"
	"external-tool/internal/model/imap"
	"fmt"
	"testing"
	"time"
)

// newMaxMatchingScheduler 创建使用真实数据的 MaxMatchingScheduler 实例
func newMaxMatchingScheduler() BestSchedule {
	db := config.GetDB()
	mapModel := imap.NewMapModel("tencent")

	var lawyers []model.Lawyer
	db.Model(&model.Lawyer{}).Where("lat != 0 AND lng != 0 AND status = 1 AND formatted_address <> '广东省深圳市福田区深南大道6025'").Order("id ASC").Find(&lawyers)
	var courts []model.Court
	db.Model(&model.Court{}).Where("lat != 0 AND lng != 0 AND status = 1").Order("id ASC").Find(&courts)
	var trials []model.Trial
	db.Model(&model.Trial{}).Where("status = 1").Order("id ASC").Find(&trials)
	var routes []model.Routes
	db.Model(&model.Routes{}).Where("status = 1").Order("id ASC").Find(&routes)

	scheduler := NewMaxMatchingScheduler(mapModel, lawyers, courts, trials, routes)
	return scheduler
}

// TestRealMaxMatchingScheduler 使用真实数据测试 MaxMatchingScheduler
func TestRealMaxMatchingScheduler(t *testing.T) {
	scheduler := newMaxMatchingScheduler().(*MaxMatchingScheduler)

	// 输出 NewMaxMatchingScheduler 之后的值
	fmt.Printf("NewMaxMatchingScheduler 返回的对象类型: %T\n", scheduler)

	// 尝试转换为具体类型以便查看内部结构
	fmt.Printf("MaxMatchingScheduler 结构体内容:\n")
	fmt.Printf("  mapModel: %T\n", scheduler.mapModel)
	fmt.Printf("  lawyerMap 长度: %d\n", len(scheduler.lawyerMap))
	fmt.Printf("  courtMap 长度: %d\n", len(scheduler.courtMap))
	fmt.Printf("  trialMap 长度: %d\n", len(scheduler.trialMap))
	fmt.Printf("  routeMap 长度: %d\n", len(scheduler.routeMap))
	fmt.Printf("  bipartiteGraph 长度: %d\n", len(scheduler.bipartiteGraph))
	fmt.Printf("  lawyerTrials 长度: %d\n", len(scheduler.lawyerTrials))

	result := scheduler.Schedule()
	fmt.Printf("MaxMatchingScheduler Schedule 结果: %v\n", result)
}

// TestAllMaxMatchings 测试枚举所有最大匹配功能
func TestAllMaxMatchings(t *testing.T) {
	scheduler := newMaxMatchingScheduler().(*MaxMatchingScheduler)

	fmt.Printf("\n===== 测试枚举所有最大匹配功能 =====\n")

	// 启用枚举所有最大匹配
	scheduler.SetEnableAllMatchings(true)

	fmt.Printf("已启用枚举所有最大匹配模式\n")

	// 执行调度
	result := scheduler.Schedule()

	// 获取所有最大匹配
	allMaxMatchings := scheduler.GetAllMaxMatchings()

	fmt.Printf("\n===== 枚举结果分析 =====\n")
	fmt.Printf("找到的最大匹配数量: %d\n", len(allMaxMatchings))

	if len(allMaxMatchings) > 0 {
		fmt.Printf("最大匹配大小: %d\n", len(allMaxMatchings[0]))

		// 分析不同匹配方案的差异
		fmt.Printf("\n===== 各方案对比 =====\n")
		for i, matching := range allMaxMatchings {
			if i >= 10 { // 限制输出数量
				fmt.Printf("... 还有 %d 个方案 (仅显示前10个)\n", len(allMaxMatchings)-10)
				break
			}

			fmt.Printf("方案 %d: %d个匹配\n", i+1, len(matching))

			// 计算该方案的总距离和时间
			totalDistance, totalDuration := scheduler.calculateTotalDistanceAndDuration(matching)
			fmt.Printf("  总距离: %dm, 总时间: %d分钟\n", totalDistance, totalDuration)

			// 统计律师工作负载
			lawyerWorkload := make(map[uint]int)
			for _, lawyerID := range matching {
				lawyerWorkload[lawyerID]++
			}

			fmt.Printf("  律师分配: ")
			for lawyerID, count := range lawyerWorkload {
				lawyer := scheduler.lawyerMap[lawyerID]
				lawyerName := fmt.Sprintf("律师%d", lawyerID)
				if lawyer != nil {
					lawyerName = lawyer.Name
				}
				fmt.Printf("%s(%d件) ", lawyerName, count)
			}
			fmt.Printf("\n")
		}

		// 比较不同方案的优劣
		if len(allMaxMatchings) > 1 {
			fmt.Printf("\n===== 方案对比分析 =====\n")
			bestDistance := int(^uint(0) >> 1) // 最大int值
			bestDuration := int(^uint(0) >> 1)
			bestDistanceIndex := 0
			bestDurationIndex := 0

			for i, matching := range allMaxMatchings {
				distance, duration := scheduler.calculateTotalDistanceAndDuration(matching)

				if distance < bestDistance {
					bestDistance = distance
					bestDistanceIndex = i
				}

				if duration < bestDuration {
					bestDuration = duration
					bestDurationIndex = i
				}
			}

			fmt.Printf("最短总距离方案: 方案%d (%dm)\n", bestDistanceIndex+1, bestDistance)
			fmt.Printf("最短总时间方案: 方案%d (%d分钟)\n", bestDurationIndex+1, bestDuration)
		}
	}

	fmt.Printf("\n===== 原始调度结果 =====\n")
	fmt.Printf("调度结果包含 %d 个匹配数级别\n", len(result))
	for matchCount, schedules := range result {
		fmt.Printf("匹配数 %d: %d 个方案\n", matchCount, len(schedules))
	}
}

// TestIncrementalEnumerationPerformance 测试增量枚举算法的性能
func TestIncrementalEnumerationPerformance(t *testing.T) {
	// 创建简单的mock数据来验证算法性能
	mapModel := &MockMapModel{}

	// 创建6个律师
	lawyers := []model.Lawyer{
		{ID: 1, Name: "张三", Lat: 39.9042, Lng: 116.4074}, // 北京
		{ID: 2, Name: "李四", Lat: 31.2304, Lng: 121.4737}, // 上海
		{ID: 3, Name: "王五", Lat: 23.1291, Lng: 113.2644}, // 广州
		{ID: 4, Name: "赵六", Lat: 30.5728, Lng: 104.0668}, // 成都
		{ID: 5, Name: "钱七", Lat: 36.0611, Lng: 120.3773}, // 青岛
		{ID: 6, Name: "孙八", Lat: 29.5630, Lng: 106.5516}, // 重庆
	}

	// 创建3个法院
	courts := []model.Court{
		{ID: 100, Name: "北京法院", Lat: 39.9042, Lng: 116.4074},
		{ID: 101, Name: "上海法院", Lat: 31.2304, Lng: 121.4737},
		{ID: 102, Name: "广州法院", Lat: 23.1291, Lng: 113.2644},
	}

	// 创建3个案件，时间不冲突，每个案件都可以被多个律师处理
	trials := []model.Trial{
		// 3个不同时间段的案件，保证没有时间冲突
		{ID: 201, CourtID: 100, StartTime: time.Date(2024, 7, 15, 9, 0, 0, 0, time.UTC), EndTime: time.Date(2024, 7, 15, 11, 0, 0, 0, time.UTC)},  // 北京法院，上午
		{ID: 202, CourtID: 101, StartTime: time.Date(2024, 7, 15, 14, 0, 0, 0, time.UTC), EndTime: time.Date(2024, 7, 15, 16, 0, 0, 0, time.UTC)}, // 上海法院，下午
		{ID: 203, CourtID: 102, StartTime: time.Date(2024, 7, 16, 9, 0, 0, 0, time.UTC), EndTime: time.Date(2024, 7, 16, 11, 0, 0, 0, time.UTC)},  // 广州法院，第二天上午
	}

	// 创建路线数据（所有律师都能到达所有法院）
	routes := []model.Routes{}
	for _, lawyer := range lawyers {
		for _, court := range courts {
			routes = append(routes, model.Routes{
				ID:       uint(len(routes) + 1),
				FromLat:  lawyer.Lat,
				FromLng:  lawyer.Lng,
				ToLat:    court.Lat,
				ToLng:    court.Lng,
				Distance: 1000, // 1km
				Duration: 600,  // 10分钟
				Status:   1,
			})
		}
	}

	// 创建调度器
	scheduler := NewMaxMatchingScheduler(mapModel, lawyers, courts, trials, routes).(*MaxMatchingScheduler)

	fmt.Printf("\n===== 增量枚举算法性能测试 =====\n")
	fmt.Printf("数据规模: %d律师 × %d案件 × %d法院\n", len(lawyers), len(trials), len(courts))
	fmt.Printf("理论最大匹配数: %d (每个案件匹配一个律师)\n", len(trials))
	fmt.Printf("理论组合数: %d × %d × %d = %d (每个案件有6个可选律师)\n",
		len(lawyers), len(lawyers), len(lawyers), len(lawyers)*len(lawyers)*len(lawyers))

	// 启用枚举所有最大匹配
	scheduler.SetEnableAllMatchings(true)

	// 记录开始时间
	startTime := time.Now()

	// 执行调度
	result := scheduler.Schedule()

	// 记录结束时间
	endTime := time.Now()
	duration := endTime.Sub(startTime)

	// 获取所有最大匹配
	allMaxMatchings := scheduler.GetAllMaxMatchings()

	fmt.Printf("\n===== 性能结果 =====\n")
	fmt.Printf("执行时间: %v\n", duration)
	fmt.Printf("找到的最大匹配数量: %d\n", len(allMaxMatchings))

	if len(allMaxMatchings) > 0 {
		fmt.Printf("最大匹配大小: %d\n", len(allMaxMatchings[0]))

		fmt.Printf("\n前几个匹配方案:\n")
		for i, matching := range allMaxMatchings {
			if i >= 8 { // 显示前8个
				fmt.Printf("... 还有 %d 个方案\n", len(allMaxMatchings)-8)
				break
			}

			fmt.Printf("方案 %d: ", i+1)
			for trialID, lawyerID := range matching {
				lawyerName := fmt.Sprintf("律师%d", lawyerID)
				if lawyer := scheduler.lawyerMap[lawyerID]; lawyer != nil {
					lawyerName = lawyer.Name
				}
				fmt.Printf("案件%d→%s ", trialID, lawyerName)
			}
			fmt.Printf("\n")
		}

		// 验证算法正确性
		fmt.Printf("\n===== 正确性验证 =====\n")
		expectedMaxSize := len(trials) // 理论上应该能匹配所有案件
		if len(allMaxMatchings[0]) == expectedMaxSize {
			fmt.Printf("✓ 匹配大小正确 (应为%d，实际为%d)\n", expectedMaxSize, len(allMaxMatchings[0]))
		} else {
			fmt.Printf("✗ 匹配大小错误 (应为%d，实际为%d)\n", expectedMaxSize, len(allMaxMatchings[0]))
		}

		// 检查是否有重复方案
		signatures := make(map[string]bool)
		duplicates := 0
		for _, matching := range allMaxMatchings {
			sig := scheduler.generateMatchingSignature(matching)
			if signatures[sig] {
				duplicates++
			} else {
				signatures[sig] = true
			}
		}

		if duplicates == 0 {
			fmt.Printf("✓ 无重复方案\n")
		} else {
			fmt.Printf("✗ 发现 %d 个重复方案\n", duplicates)
		}

		// 分析方案多样性
		lawyerUsageCount := make(map[uint]int)
		for _, matching := range allMaxMatchings {
			for _, lawyerID := range matching {
				lawyerUsageCount[lawyerID]++
			}
		}

		fmt.Printf("\n律师使用统计:\n")
		for lawyerID, count := range lawyerUsageCount {
			lawyer := scheduler.lawyerMap[lawyerID]
			lawyerName := fmt.Sprintf("律师%d", lawyerID)
			if lawyer != nil {
				lawyerName = lawyer.Name
			}
			percentage := float64(count) / float64(len(allMaxMatchings)) * 100
			fmt.Printf("  %s: 在 %d 个方案中被使用 (%.1f%%)\n", lawyerName, count, percentage)
		}

		fmt.Printf("\n算法复杂度分析:\n")
		fmt.Printf("实际复杂度: O(30 × %d × %d) ≈ O(%d)\n", len(lawyers), len(trials), 30*len(lawyers)*len(trials))

		backtrackComplexity := 1
		for i := 0; i < len(trials); i++ {
			backtrackComplexity *= len(lawyers)
		}
		fmt.Printf("回溯复杂度: O(%d^%d) ≈ O(%d)\n", len(lawyers), len(trials), backtrackComplexity)

		improvement := float64(backtrackComplexity) / float64(30*len(lawyers)*len(trials))
		fmt.Printf("性能改进倍数: %.1fx\n", improvement)
	}

	fmt.Printf("\n调度结果包含 %d 个匹配数级别\n", len(result))
}

// MockMapModel 简单的mock实现
type MockMapModel struct{}

func (m *MockMapModel) GetRoute(from, to *model.Location) (*model.Route, error) {
	return &model.Route{
		Distance: 1000,
		Duration: 600,
	}, nil
}

func (m *MockMapModel) Geocoder(address string) (model.Location, model.AddressComponents, error) {
	return model.Location{Lat: 39.9042, Lng: 116.4074}, model.AddressComponents{}, nil
}

func (m *MockMapModel) DirectionDriving(from, to, policy string, departureTime time.Time) ([]model.Route, error) {
	return []model.Route{
		{
			Distance: 1000,
			Duration: 600,
		},
	}, nil
}

func (m *MockMapModel) DirectionTransit(from, to *model.Location, policy string, departureTime time.Time) ([]model.Route, string, error) {
	return []model.Route{
		{
			Distance: 1000,
			Duration: 600,
		},
	}, "", nil
}
