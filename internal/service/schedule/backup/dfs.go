package schedule

import (
	"external-tool/internal/model"
	"external-tool/internal/model/imap"
	"fmt"
	"time"
)

type BestSchedule interface {
	Schedule() map[uint][]map[uint]uint
}

type DFS struct {
	mapModel        imap.MapModel
	lawyerMap       map[uint]*model.Lawyer
	courtMap        map[uint]*model.Court
	trialMap        map[uint]*model.Trial
	routeMap        map[string]*model.Route
	maxTotalMatches uint // 当前最大的匹配数量
	solutionCount   int  // 当前匹配数量的解决方案计数
}

const (
	Buffer               = 1.5
	DistanceNotAvailable = -1 // 表示不可达的距离
)

func NewDFS(mapModel imap.MapModel, lawyers []model.Lawyer, courts []model.Court, trials []model.Trial, routes []model.Routes) BestSchedule {
	dfs := &DFS{}

	dfs.mapModel = mapModel
	dfs.InitMaps(lawyers, courts, trials, routes)
	// dfs.InitRoutes(routes)

	return dfs
}

func (d *DFS) key(from, to *model.Location) string {
	return fmt.Sprintf("%s:%s", from.String(), to.String())
}

func (d *DFS) InitMaps(lawyers []model.Lawyer, courts []model.Court, trials []model.Trial, routes []model.Routes) {
	// 初始化律师映射
	d.lawyerMap = make(map[uint]*model.Lawyer)
	for _, lawyer := range lawyers {
		lawyer := lawyer // 创建局部副本
		d.lawyerMap[lawyer.ID] = &lawyer
	}

	// 初始化法院映射
	d.courtMap = make(map[uint]*model.Court)
	for _, court := range courts {
		court := court // 创建局部副本
		d.courtMap[court.ID] = &court
	}

	// 初始化案件映射
	d.trialMap = make(map[uint]*model.Trial)
	for _, trial := range trials {
		trial := trial // 创建局部副本
		d.trialMap[trial.ID] = &trial
	}

	d.routeMap = make(map[string]*model.Route)
	for _, route := range routes {
		route := route // 创建局部副本
		key := d.key(route.GetFromLocation(), route.GetToLocation())
		d.routeMap[key] = &model.Route{
			Distance: route.Distance,
			Duration: route.Duration,
			Price:    route.Price,
		}
	}
}

// Schedule 计算所有可能的律师-案件匹配组合
// 返回格式: map[匹配数量][]map[开庭ID]律师ID
func (d *DFS) Schedule() map[uint][]map[uint]uint {
	result := make(map[uint][]map[uint]uint)

	// 初始化最大匹配数量和解决方案计数
	d.maxTotalMatches = 0
	d.solutionCount = 0

	// 如果没有律师或案件，返回空结果
	if len(d.lawyerMap) == 0 || len(d.trialMap) == 0 {
		result[0] = []map[uint]uint{{}}
		return result
	}

	// 预处理：为每个律师创建可选的案件列表（考虑距离限制）
	lawyerTrials := d.buildLawyerTrialMap()

	// 收集所有案件ID
	trialIDs := make([]uint, 0, len(d.trialMap))
	for trialID := range d.trialMap {
		trialIDs = append(trialIDs, trialID)
	}

	// 使用 DFS 生成所有可能的匹配
	d.dfsHelperByTrials(trialIDs, 0, lawyerTrials, make(map[uint][]uint), make(map[uint]bool), result)

	// 如果没有找到任何匹配，添加空匹配
	if len(result) == 0 {
		result[0] = []map[uint]uint{{}}
	}

	// 输出最终结果
	d.printFinalResults(result)

	return result
}

// buildLawyerTrialMap 构建律师到可选案件的映射，考虑距离和时间约束
func (d *DFS) buildLawyerTrialMap() map[uint][]uint {
	lawyerTrials := make(map[uint][]uint)

	// 为每个律师找到所有可行的案件
	for _, lawyer := range d.lawyerMap {
		var availableTrials []uint

		for _, trial := range d.trialMap {
			// 检查律师是否能够到达该法院（距离限制）
			if d.canLawyerReachCourt(lawyer.ID, trial.CourtID) {
				availableTrials = append(availableTrials, trial.ID)
			}
		}

		lawyerTrials[lawyer.ID] = availableTrials
	}

	return lawyerTrials
}

// canLawyerReachCourt 检查律师是否能到达指定法院
func (d *DFS) canLawyerReachCourt(lawyerID, courtID uint) bool {
	lawyer := d.lawyerMap[lawyerID]
	court := d.courtMap[courtID]

	// 检查律师和法院是否存在
	if lawyer == nil || court == nil {
		return false
	}

	key := d.key(lawyer.GetLocation(), court.GetLocation())
	route, ok := d.routeMap[key]
	if !ok {
		return false
	}

	return route.Available()
}

// dfsHelperByTrials 按案件遍历进行递归匹配
func (d *DFS) dfsHelperByTrials(trialIDs []uint, trialIndex int, lawyerTrials map[uint][]uint,
	currentAssignment map[uint][]uint, usedTrials map[uint]bool,
	result map[uint][]map[uint]uint) {

	// 如果已经处理完所有案件，保存当前匹配结果
	if trialIndex >= len(trialIDs) {
		d.saveScheduleMulti(currentAssignment, result)
		return
	}

	currentTrialID := trialIDs[trialIndex]

	// 选项1: 跳过当前案件（不分配给任何律师）
	d.dfsHelperByTrials(trialIDs, trialIndex+1, lawyerTrials, currentAssignment, usedTrials, result)

	// 选项2: 将当前案件分配给某个律师
	if !usedTrials[currentTrialID] {
		// 找到所有能够处理当前案件的律师
		availableLawyers := d.getLawyersForTrial(currentTrialID, lawyerTrials)

		// 按照时间和当前排庭数量排序，优先选择时间少且排庭少的律师
		sortedLawyers := d.sortLawyersByTimeAndAssignments(availableLawyers, currentTrialID, currentAssignment)

		// 尝试将案件分配给每个可用的律师
		for _, lawyerID := range sortedLawyers {
			// 检查时间是否兼容
			if d.isTimeCompatibleMulti(lawyerID, currentTrialID, currentAssignment) {
				// 创建新的状态副本
				newAssignment := d.copyAssignmentMulti(currentAssignment)
				newUsedTrials := d.copyUsedTrials(usedTrials)

				// 分配案件
				if newAssignment[lawyerID] == nil {
					newAssignment[lawyerID] = []uint{}
				}
				newAssignment[lawyerID] = append(newAssignment[lawyerID], currentTrialID)
				newUsedTrials[currentTrialID] = true

				// 递归处理下一个案件
				d.dfsHelperByTrials(trialIDs, trialIndex+1, lawyerTrials, newAssignment, newUsedTrials, result)
			}
		}
	}
}

// getLawyersForTrial 获取能够处理指定案件的律师列表
func (d *DFS) getLawyersForTrial(trialID uint, lawyerTrials map[uint][]uint) []uint {
	var availableLawyers []uint

	for lawyerID, trials := range lawyerTrials {
		for _, trial := range trials {
			if trial == trialID {
				availableLawyers = append(availableLawyers, lawyerID)
				break
			}
		}
	}

	return availableLawyers
}

// sortLawyersByCurrentAssignments 按照当前分配的案件数量对律师进行排序
func (d *DFS) sortLawyersByCurrentAssignments(lawyerIDs []uint, currentAssignment map[uint][]uint) []uint {
	// 创建副本避免修改原始数据
	sortedLawyers := make([]uint, len(lawyerIDs))
	copy(sortedLawyers, lawyerIDs)

	// 按照当前分配的案件数量排序（从少到多）
	for i := 0; i < len(sortedLawyers); i++ {
		for j := i + 1; j < len(sortedLawyers); j++ {
			trialsI := len(currentAssignment[sortedLawyers[i]])
			trialsJ := len(currentAssignment[sortedLawyers[j]])
			if trialsI > trialsJ {
				sortedLawyers[i], sortedLawyers[j] = sortedLawyers[j], sortedLawyers[i]
			}
		}
	}

	return sortedLawyers
}

// sortLawyersByTimeAndAssignments 按照时间和当前分配的案件数量对律师进行排序
// 优先级：1. 时间最少 2. 时间相同时，当前分配案件最少
func (d *DFS) sortLawyersByTimeAndAssignments(lawyerIDs []uint, trialID uint, currentAssignment map[uint][]uint) []uint {
	// 创建副本避免修改原始数据
	sortedLawyers := make([]uint, len(lawyerIDs))
	copy(sortedLawyers, lawyerIDs)

	// 获取开庭对应的法院
	trial := d.trialMap[trialID]
	if trial == nil {
		// 如果获取不到开庭信息，使用原来的排序方法
		return d.sortLawyersByCurrentAssignments(lawyerIDs, currentAssignment)
	}

	// 按照时间和当前分配数量排序
	for i := 0; i < len(sortedLawyers); i++ {
		for j := i + 1; j < len(sortedLawyers); j++ {
			lawyerI := sortedLawyers[i]
			lawyerJ := sortedLawyers[j]

			// 获取时间
			durationI := d.getLawyerToCourtDuration(lawyerI, trial.CourtID)
			durationJ := d.getLawyerToCourtDuration(lawyerJ, trial.CourtID)

			// 处理不可达的情况（0）
			if durationI == 0 && durationJ != 0 {
				// I 不可达，J 可达，J 排在前面
				sortedLawyers[i], sortedLawyers[j] = sortedLawyers[j], sortedLawyers[i]
			} else if durationI != 0 && durationJ == 0 {
				// I 可达，J 不可达，I 排在前面（不需要交换）
				continue
			} else if durationI == 0 && durationJ == 0 {
				// 都不可达，按当前分配的案件数量排序
				trialsI := len(currentAssignment[lawyerI])
				trialsJ := len(currentAssignment[lawyerJ])
				if trialsI > trialsJ {
					sortedLawyers[i], sortedLawyers[j] = sortedLawyers[j], sortedLawyers[i]
				}
			} else {
				// 都可达，按时间排序
				if durationI > durationJ {
					sortedLawyers[i], sortedLawyers[j] = sortedLawyers[j], sortedLawyers[i]
				} else if durationI == durationJ {
					// 时间相同时，按当前分配的案件数量排序
					trialsI := len(currentAssignment[lawyerI])
					trialsJ := len(currentAssignment[lawyerJ])
					if trialsI > trialsJ {
						sortedLawyers[i], sortedLawyers[j] = sortedLawyers[j], sortedLawyers[i]
					}
				}
			}
		}
	}

	return sortedLawyers
}

// getLawyerToCourtDistance 获取律师到法院的距离
func (d *DFS) getLawyerToCourtDistance(lawyerID, courtID uint) int {
	lawyer := d.lawyerMap[lawyerID]
	court := d.courtMap[courtID]

	if lawyer == nil || court == nil {
		return DistanceNotAvailable // 返回 -1 表示不可达
	}

	key := d.key(lawyer.GetLocation(), court.GetLocation())
	route, ok := d.routeMap[key]
	if !ok || !route.Available() {
		return DistanceNotAvailable // 返回 -1 表示不可达
	}

	return route.Distance
}

// isTimeCompatibleMulti 检查律师的时间是否与已分配的案件冲突（支持多案件）
func (d *DFS) isTimeCompatibleMulti(lawyerID, trialID uint, currentAssignment map[uint][]uint) bool {
	// 获取当前案件的时间和法院
	currentTrial := d.trialMap[trialID]
	if currentTrial == nil {
		return false
	}

	// 获取本次任务往返出行时间
	currentStart, currentEnd, err := d.getTravelTime(lawyerID, currentTrial)
	if err != nil {
		return false
	}

	// 检查是否与该律师已分配的其他案件时间冲突
	assignedTrials, exists := currentAssignment[lawyerID]
	if !exists {
		return true // 没有已分配的案件，不会冲突
	}

	for _, assignedTrialID := range assignedTrials {
		assignedTrial := d.trialMap[assignedTrialID]
		if assignedTrial == nil {
			return false
		}

		// 获取律师到已分配案件法院的出行时间
		assignedStart, assignedEnd, err := d.getTravelTime(lawyerID, assignedTrial)
		if err != nil {
			return false
		}

		// 检查时间是否重叠
		if d.isTimeOverlap(currentStart, currentEnd, assignedStart, assignedEnd) {
			return false
		}
	}

	return true
}

// getTravelTime 获取本次任务的总耗时, 律师到法院的往返时间 + buffer + 庭审时间
func (d *DFS) getTravelTime(lawyerID uint, trial *model.Trial) (time.Time, time.Time, error) {
	emptyTime := time.Time{}
	lawyer := d.lawyerMap[lawyerID]
	if lawyer == nil {
		return emptyTime, emptyTime, fmt.Errorf("no route by missing lawyer %d", lawyerID)
	}

	// 获取律师到法院的路线
	court := d.courtMap[trial.CourtID]
	if court == nil {
		return emptyTime, emptyTime, fmt.Errorf("no route by missing court %d", trial.CourtID)
	}

	key := d.key(lawyer.GetLocation(), court.GetLocation())

	route, ok := d.routeMap[key]
	if !ok {
		return emptyTime, emptyTime, fmt.Errorf("no route by missing route %s", key)
	}

	// 使用实际的出行时间
	travelDuration := time.Duration(float64(route.Duration)*Buffer) * time.Minute
	startTime := trial.StartTime.Add(-travelDuration)
	endTime := trial.EndTime.Add(travelDuration)

	return startTime, endTime, nil
}

// isTimeOverlap 检查两个时间段是否重叠
func (d *DFS) isTimeOverlap(start1, end1, start2, end2 time.Time) bool {
	return start1.Before(end2) && start2.Before(end1)
}

// copyAssignmentMulti 创建分配映射的副本（支持多案件）
func (d *DFS) copyAssignmentMulti(original map[uint][]uint) map[uint][]uint {
	copyMap := make(map[uint][]uint)
	for k, v := range original {
		newSlice := make([]uint, len(v))
		copy(newSlice, v)
		copyMap[k] = newSlice
	}
	return copyMap
}

// copyUsedTrials 创建已使用案件映射的副本
func (d *DFS) copyUsedTrials(original map[uint]bool) map[uint]bool {
	copy := make(map[uint]bool)
	for k, v := range original {
		copy[k] = v
	}
	return copy
}

// saveScheduleMulti 保存一个调度方案到结果中（支持多案件）
func (d *DFS) saveScheduleMulti(assignment map[uint][]uint, result map[uint][]map[uint]uint) {
	totalMatches := uint(0)
	for _, trials := range assignment {
		totalMatches += uint(len(trials))
	}

	// 如果当前匹配数量小于最大值，直接返回
	if totalMatches < d.maxTotalMatches {
		return
	}

	// 如果找到了更大的匹配数量，清空结果并重置计数
	if totalMatches > d.maxTotalMatches {
		d.maxTotalMatches = totalMatches
		d.solutionCount = 0
		// 清空之前的结果
		for key := range result {
			delete(result, key)
		}
	}

	// 如果当前匹配数量的解决方案已经达到100个，返回
	if d.solutionCount >= 100 {
		return
	}

	// 如果这是第一个具有该匹配数量的方案，初始化数组
	if result[totalMatches] == nil {
		result[totalMatches] = make([]map[uint]uint, 0)
	}

	// 转换格式：从 map[律师ID][]开庭ID 转换为 map[开庭ID]律师ID
	trialAssignment := make(map[uint]uint)
	for lawyerID, trialIDs := range assignment {
		for _, trialID := range trialIDs {
			trialAssignment[trialID] = lawyerID
		}
	}

	// 检查是否已经存在相同的方案（避免重复）
	for _, existingSchedule := range result[totalMatches] {
		if d.isScheduleEqualNew(trialAssignment, existingSchedule) {
			return // 跳过重复的方案
		}
	}

	// 添加当前方案
	result[totalMatches] = append(result[totalMatches], trialAssignment)
	d.solutionCount++
}

// calculateTotalDistanceAndDuration 计算调度方案的总路程和总时间
func (d *DFS) calculateTotalDistanceAndDuration(trialAssignment map[uint]uint) (int, int) {
	totalDistance := 0
	totalDuration := 0

	for trialID, lawyerID := range trialAssignment {
		trial := d.trialMap[trialID]
		if trial == nil {
			continue
		}

		// 获取律师到法院的距离和时间
		distance := d.getLawyerToCourtDistance(lawyerID, trial.CourtID)
		duration := d.getLawyerToCourtDuration(lawyerID, trial.CourtID)

		// 跳过不可达的情况
		if distance == DistanceNotAvailable {
			continue
		}

		// 往返距离和时间
		totalDistance += distance * 2
		totalDuration += duration * 2

		// 加上庭审时间
		trialDuration := int(trial.EndTime.Sub(trial.StartTime).Minutes())
		totalDuration += trialDuration
	}

	return totalDistance, totalDuration
}

// getLawyerToCourtDuration 获取律师到法院的时间（分钟）
func (d *DFS) getLawyerToCourtDuration(lawyerID, courtID uint) int {
	lawyer := d.lawyerMap[lawyerID]
	court := d.courtMap[courtID]

	if lawyer == nil || court == nil {
		return 0
	}

	key := d.key(lawyer.GetLocation(), court.GetLocation())
	route, ok := d.routeMap[key]
	if !ok || !route.Available() {
		return 0
	}

	return route.Duration // 直接返回分钟
}

// calculateSingleMatchDistanceAndDuration 计算单个匹配的距离和时间
func (d *DFS) calculateSingleMatchDistanceAndDuration(trialID, lawyerID uint) (int, int) {
	trial := d.trialMap[trialID]
	if trial == nil {
		return 0, 0
	}

	// 获取律师到法院的距离和时间
	distance := d.getLawyerToCourtDistance(lawyerID, trial.CourtID)
	duration := d.getLawyerToCourtDuration(lawyerID, trial.CourtID)

	// 跳过不可达的情况
	if distance == DistanceNotAvailable {
		return 0, 0
	}

	// 往返距离和时间
	totalDistance := distance * 2
	totalDuration := duration * 2

	// 加上庭审时间
	trialDuration := int(trial.EndTime.Sub(trial.StartTime).Minutes())
	totalDuration += trialDuration

	return totalDistance, totalDuration
}

// printDetailedMatchInfo 输出单个匹配的详细信息 (已禁用以简化输出)
func (d *DFS) printDetailedMatchInfo(trialID, lawyerID uint) {
	// 详细匹配信息输出已被移除以简化日志
}

// formatLocation 格式化位置信息
func (d *DFS) formatLocation(location *model.Location) string {
	if location == nil {
		return "位置信息缺失"
	}
	return fmt.Sprintf("%.6f, %.6f", location.Lat, location.Lng)
}

// isScheduleEqualNew 检查两个调度方案是否相等（新格式：map[开庭ID]律师ID）
func (d *DFS) isScheduleEqualNew(schedule1, schedule2 map[uint]uint) bool {
	if len(schedule1) != len(schedule2) {
		return false
	}

	for trialID, lawyerID1 := range schedule1 {
		lawyerID2, exists := schedule2[trialID]
		if !exists || lawyerID1 != lawyerID2 {
			return false
		}
	}

	return true
}

// printFinalResults 输出最终的调度结果
func (d *DFS) printFinalResults(result map[uint][]map[uint]uint) {
	fmt.Printf("===== DFS 调度完成 =====\n")

	if len(result) == 0 {
		fmt.Printf("未找到任何匹配方案\n")
		return
	}

	// 按匹配数量排序输出
	for matchCount := uint(0); matchCount <= d.maxTotalMatches; matchCount++ {
		schedules, exists := result[matchCount]
		if !exists || len(schedules) == 0 {
			continue
		}

		fmt.Printf("匹配数量 %d: 找到 %d 个方案\n", matchCount, len(schedules))

		// 输出每个方案的详细信息
		for i, schedule := range schedules {
			if matchCount == 0 {
				fmt.Printf("  方案 %d: 无匹配\n", i+1)
				continue
			}

			totalDistance, totalDuration := d.calculateTotalDistanceAndDuration(schedule)
			fmt.Printf("  方案 %d: %v 总路程: %d米 总时间: %d分钟\n",
				i+1, schedule, totalDistance, totalDuration)

			// 详细匹配信息已移除以简化输出
		}
	}

	fmt.Printf("===== 调度结果输出完成 =====\n")
}
