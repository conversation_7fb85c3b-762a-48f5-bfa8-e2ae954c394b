package schedule

import (
	"external-tool/internal/model/imap"
	"fmt"
	"testing"
)

// 调试测试：分析为什么只能匹配3个庭审
func TestDebugTrialMatching(t *testing.T) {
	// 获取测试数据
	lawyers, courts, trials, routes := createTestData()

	fmt.Printf("=== 调试庭审匹配分析 ===\n")
	fmt.Printf("律师数量: %d, 法院数量: %d, 庭审数量: %d, 路径数量: %d\n",
		len(lawyers), len(courts), len(trials), len(routes))

	// 输出所有庭审的详细信息
	fmt.Printf("\n=== 庭审详细信息 ===\n")
	for i, trial := range trials {
		fmt.Printf("庭审 %d (ID: %d):\n", i+1, trial.ID)
		fmt.Printf("  案件ID: %d\n", trial.CaseID)
		fmt.Printf("  法院ID: %d\n", trial.CourtID)
		fmt.Printf("  开始时间: %s\n", trial.StartTime.Format("2006-01-02 15:04:05"))
		fmt.Printf("  结束时间: %s\n", trial.EndTime.Format("2006-01-02 15:04:05"))
		fmt.Printf("  持续时间: %d分钟\n", int(trial.EndTime.Sub(trial.StartTime).Minutes()))
		fmt.Printf("  状态: %d\n", trial.Status)

		// 找到对应的法院
		for _, court := range courts {
			if court.ID == trial.CourtID {
				fmt.Printf("  法院: %s\n", court.Name)
				fmt.Printf("  法院地址: %s\n", court.FormattedAddress)
				fmt.Printf("  法院坐标: (%.6f, %.6f)\n", court.Lat, court.Lng)
				break
			}
		}
		fmt.Printf("\n")
	}

	// 输出律师的详细信息
	fmt.Printf("=== 律师详细信息 ===\n")
	for i, lawyer := range lawyers {
		fmt.Printf("律师 %d (ID: %d):\n", i+1, lawyer.ID)
		fmt.Printf("  姓名: %s\n", lawyer.Name)
		fmt.Printf("  地址: %s\n", lawyer.FormattedAddress)
		fmt.Printf("  坐标: (%.6f, %.6f)\n", lawyer.Lat, lawyer.Lng)
		fmt.Printf("  状态: %d\n", lawyer.Status)
		fmt.Printf("\n")
	}

	// 创建DFS分析可达性
	mapModel := imap.NewMapModel("tencent")
	dfs := NewDFS(mapModel, lawyers, courts, trials, routes).(*DFS)

	fmt.Printf("=== 律师-法院可达性分析 ===\n")
	for _, lawyer := range lawyers {
		fmt.Printf("律师 %s (ID: %d) 可达性:\n", lawyer.Name, lawyer.ID)
		for _, trial := range trials {
			reachable := dfs.canLawyerReachCourt(lawyer.ID, trial.CourtID)
			distance := dfs.getLawyerToCourtDistance(lawyer.ID, trial.CourtID)
			duration := dfs.getLawyerToCourtDuration(lawyer.ID, trial.CourtID)

			fmt.Printf("  -> 庭审%d (法院%d): 可达=%t, 距离=%d米, 时间=%d分钟\n",
				trial.ID, trial.CourtID, reachable, distance, duration)
		}
		fmt.Printf("\n")
	}

	// 时间兼容性分析
	fmt.Printf("=== 时间兼容性分析 ===\n")
	for i, trial1 := range trials {
		for j, trial2 := range trials {
			if i >= j {
				continue
			}

			fmt.Printf("庭审%d vs 庭审%d:\n", trial1.ID, trial2.ID)
			fmt.Printf("  庭审%d: %s ~ %s\n", trial1.ID,
				trial1.StartTime.Format("15:04:05"), trial1.EndTime.Format("15:04:05"))
			fmt.Printf("  庭审%d: %s ~ %s\n", trial2.ID,
				trial2.StartTime.Format("15:04:05"), trial2.EndTime.Format("15:04:05"))

			// 检查时间是否重叠
			overlap := trial1.StartTime.Before(trial2.EndTime) && trial2.StartTime.Before(trial1.EndTime)
			fmt.Printf("  时间重叠: %t\n", overlap)

			// 检查同一律师是否能处理两个案件
			for _, lawyer := range lawyers {
				canHandle1 := dfs.canLawyerReachCourt(lawyer.ID, trial1.CourtID)
				canHandle2 := dfs.canLawyerReachCourt(lawyer.ID, trial2.CourtID)

				if canHandle1 && canHandle2 {
					// 计算出行时间
					start1, end1, err1 := dfs.getTravelTime(lawyer.ID, &trial1)
					start2, end2, err2 := dfs.getTravelTime(lawyer.ID, &trial2)

					if err1 == nil && err2 == nil {
						travelOverlap := start1.Before(end2) && start2.Before(end1)
						fmt.Printf("  律师%s可达两个庭审，考虑出行时间重叠: %t\n", lawyer.Name, travelOverlap)
						if !travelOverlap {
							fmt.Printf("    律师%s可以同时处理这两个案件\n", lawyer.Name)
						}
					}
				}
			}
			fmt.Printf("\n")
		}
	}

	// 运行调度算法
	fmt.Printf("=== 调度算法结果 ===\n")
	result := dfs.Schedule()

	// 分析结果
	fmt.Printf("\n=== 结果分析 ===\n")
	for matchCount, schedules := range result {
		fmt.Printf("匹配数量 %d: %d个方案\n", matchCount, len(schedules))
		if matchCount > 0 && len(schedules) > 0 {
			// 分析第一个方案
			schedule := schedules[0]
			fmt.Printf("  分析方案1: %v\n", schedule)

			// 检查时间冲突
			for trialID1, lawyerID1 := range schedule {
				for trialID2, lawyerID2 := range schedule {
					if trialID1 >= trialID2 {
						continue
					}

					if lawyerID1 == lawyerID2 {
						fmt.Printf("    发现同一律师%d处理两个案件: %d和%d\n", lawyerID1, trialID1, trialID2)

						// 检查时间是否真的兼容
						compatible := dfs.isTimeCompatibleMulti(lawyerID1, trialID2,
							map[uint][]uint{lawyerID1: {trialID1}})
						fmt.Printf("    时间兼容性: %t\n", compatible)
					}
				}
			}
		}
	}

	// 手动尝试找到更多匹配
	fmt.Printf("=== 手动匹配尝试 ===\n")
	fmt.Printf("尝试找到所有可能的单独匹配:\n")

	for _, trial := range trials {
		fmt.Printf("庭审%d的可能匹配:\n", trial.ID)
		for _, lawyer := range lawyers {
			if dfs.canLawyerReachCourt(lawyer.ID, trial.CourtID) {
				fmt.Printf("  律师%s (ID: %d) 可以处理\n", lawyer.Name, lawyer.ID)
			}
		}
	}
}
