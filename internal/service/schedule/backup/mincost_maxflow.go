package schedule

import (
	"container/heap"
	"external-tool/internal/model"
	"external-tool/internal/model/imap"
	"fmt"
	"math"
	"sort"
	"time"
)

// FlowEdge 网络流中的边
type FlowEdge struct {
	to       int     // 目标节点
	rev      int     // 反向边在目标节点中的索引
	capacity int     // 容量
	cost     float64 // 单位费用
	flow     int     // 当前流量
}

// MinCostMaxFlow 最小费用最大流网络
type MinCostMaxFlow struct {
	n     int          // 节点数
	graph [][]FlowEdge // 邻接表
}

// NewMinCostMaxFlow 创建最小费用最大流网络
func NewMinCostMaxFlow(n int) *MinCostMaxFlow {
	return &MinCostMaxFlow{
		n:     n,
		graph: make([][]FlowEdge, n),
	}
}

// AddEdge 添加有向边
func (mcf *MinCostMaxFlow) AddEdge(from, to int, capacity int, cost float64) {
	mcf.graph[from] = append(mcf.graph[from], FlowEdge{
		to:       to,
		rev:      len(mcf.graph[to]),
		capacity: capacity,
		cost:     cost,
		flow:     0,
	})
	mcf.graph[to] = append(mcf.graph[to], FlowEdge{
		to:       from,
		rev:      len(mcf.graph[from]) - 1,
		capacity: 0,
		cost:     -cost,
		flow:     0,
	})
}

// PriorityQueue 优先队列节点
type PriorityQueueNode struct {
	node  int
	dist  float64
	index int
}

// PriorityQueue 优先队列实现
type PriorityQueue []*PriorityQueueNode

func (pq PriorityQueue) Len() int { return len(pq) }

func (pq PriorityQueue) Less(i, j int) bool {
	return pq[i].dist < pq[j].dist
}

func (pq PriorityQueue) Swap(i, j int) {
	pq[i], pq[j] = pq[j], pq[i]
	pq[i].index = i
	pq[j].index = j
}

func (pq *PriorityQueue) Push(x interface{}) {
	n := len(*pq)
	item := x.(*PriorityQueueNode)
	item.index = n
	*pq = append(*pq, item)
}

func (pq *PriorityQueue) Pop() interface{} {
	old := *pq
	n := len(old)
	item := old[n-1]
	old[n-1] = nil
	item.index = -1
	*pq = old[0 : n-1]
	return item
}

// update 更新优先队列中的节点
func (pq *PriorityQueue) update(item *PriorityQueueNode, dist float64) {
	item.dist = dist
	heap.Fix(pq, item.index)
}

// dijkstra 使用Dijkstra算法寻找最短路径
func (mcf *MinCostMaxFlow) dijkstra(source, sink int, potential []float64) ([]float64, []int, []int) {
	const INF = 1e18
	dist := make([]float64, mcf.n)
	parent := make([]int, mcf.n)
	parentEdge := make([]int, mcf.n)
	visited := make([]bool, mcf.n)

	// 初始化
	for i := range dist {
		dist[i] = INF
		parent[i] = -1
		parentEdge[i] = -1
	}

	dist[source] = 0

	// 优先队列
	pq := make(PriorityQueue, 0)
	heap.Init(&pq)

	// 将源节点加入队列
	heap.Push(&pq, &PriorityQueueNode{node: source, dist: 0})

	for pq.Len() > 0 {
		current := heap.Pop(&pq).(*PriorityQueueNode)
		u := current.node

		if visited[u] {
			continue
		}
		visited[u] = true

		if u == sink {
			break
		}

		for i, edge := range mcf.graph[u] {
			if edge.capacity > edge.flow {
				// 使用potential进行优化
				reducedCost := edge.cost + potential[u] - potential[edge.to]
				newDist := dist[u] + reducedCost

				if newDist < dist[edge.to] {
					dist[edge.to] = newDist
					parent[edge.to] = u
					parentEdge[edge.to] = i

					if !visited[edge.to] {
						heap.Push(&pq, &PriorityQueueNode{node: edge.to, dist: newDist})
					}
				}
			}
		}
	}

	return dist, parent, parentEdge
}

// MinCostMaxFlowAlgorithm 运行最小费用最大流算法
func (mcf *MinCostMaxFlow) MinCostMaxFlowAlgorithm(source, sink int) (int, float64) {
	totalFlow := 0
	totalCost := 0.0

	// 边界检查
	if source == sink || source < 0 || sink < 0 || source >= mcf.n || sink >= mcf.n {
		return 0, 0.0
	}

	// 初始化势能
	potential := make([]float64, mcf.n)
	for i := 0; i < mcf.n; i++ {
		potential[i] = 0.0
	}

	for {
		dist, parent, parentEdge := mcf.dijkstra(source, sink, potential)

		if math.IsInf(dist[sink], 1) {
			break // 没有增广路径
		}

		// 寻找路径上的最小容量
		minCapacity := math.MaxInt32
		node := sink
		for node != source {
			// 边界检查
			if parent[node] < 0 || parentEdge[node] < 0 {
				break // 路径无效
			}
			edge := &mcf.graph[parent[node]][parentEdge[node]]
			if edge.capacity-edge.flow < minCapacity {
				minCapacity = edge.capacity - edge.flow
			}
			node = parent[node]
		}

		// 如果没有找到有效路径，退出
		if minCapacity == math.MaxInt32 {
			break
		}

		// 更新路径上的流量
		node = sink
		for node != source {
			// 边界检查
			if parent[node] < 0 || parentEdge[node] < 0 {
				break // 路径无效
			}
			edge := &mcf.graph[parent[node]][parentEdge[node]]
			edge.flow += minCapacity
			mcf.graph[node][edge.rev].flow -= minCapacity
			node = parent[node]
		}

		totalFlow += minCapacity
		totalCost += float64(minCapacity) * dist[sink]

		// 更新势能
		for i := 0; i < mcf.n; i++ {
			potential[i] += dist[i]
		}
	}

	return totalFlow, totalCost
}

// MinCostMaxFlowScheduler 基于最小费用最大流的调度器
type MinCostMaxFlowScheduler struct {
	mapModel  imap.MapModel
	lawyerMap map[uint]*model.Lawyer
	courtMap  map[uint]*model.Court
	trialMap  map[uint]*model.Trial
	routeMap  map[string]*model.Route

	// 节点映射
	trialNodes  map[uint]int // 案件ID -> 节点
	lawyerNodes map[uint]int // 律师ID -> 节点
	nodeTrials  map[int]uint // 节点 -> 案件ID
	nodeLawyers map[int]uint // 节点 -> 律师ID

	source int // 源点
	sink   int // 汇点

	// 配置参数
	maxCapacityPerLawyer int     // 每个律师最大处理案件数
	distanceWeight       float64 // 距离权重
	timeWeight           float64 // 时间权重
	balanceWeight        float64 // 负载均衡权重
}

// WeightConfig 权重配置结构
type WeightConfig struct {
	Name          string  // 配置名称
	DistWeight    float64 // 距离权重(每公里的费用)
	TimeWeight    float64 // 时间权重(每分钟的费用)
	BalanceWeight float64 // 负载均衡权重
}

// NewMinCostMaxFlowScheduler 创建最小费用最大流调度器
func NewMinCostMaxFlowScheduler(mapModel imap.MapModel, lawyers []model.Lawyer, courts []model.Court, trials []model.Trial, routes []model.Routes) BestSchedule {
	scheduler := &MinCostMaxFlowScheduler{
		mapModel:             mapModel,
		trialNodes:           make(map[uint]int),
		lawyerNodes:          make(map[uint]int),
		nodeTrials:           make(map[int]uint),
		nodeLawyers:          make(map[int]uint),
		maxCapacityPerLawyer: 5,
		distanceWeight:       1.0,
		timeWeight:           10.0,
		balanceWeight:        100.0,
	}

	scheduler.initMaps(lawyers, courts, trials, routes)

	// 初始化源点和汇点
	trialCount := len(trials)
	lawyerCount := len(lawyers)
	scheduler.source = 0
	scheduler.sink = trialCount + lawyerCount + 1

	return scheduler
}

// initMaps 初始化各种映射表
func (s *MinCostMaxFlowScheduler) initMaps(lawyers []model.Lawyer, courts []model.Court, trials []model.Trial, routes []model.Routes) {
	// 初始化律师映射
	s.lawyerMap = make(map[uint]*model.Lawyer)
	for i := range lawyers {
		s.lawyerMap[lawyers[i].ID] = &lawyers[i]
	}

	// 初始化法院映射
	s.courtMap = make(map[uint]*model.Court)
	for i := range courts {
		s.courtMap[courts[i].ID] = &courts[i]
	}

	// 初始化案件映射
	s.trialMap = make(map[uint]*model.Trial)
	for i := range trials {
		s.trialMap[trials[i].ID] = &trials[i]
	}

	// 初始化路径映射
	s.routeMap = make(map[string]*model.Route)
	for i := range routes {
		key := s.getRouteKey(routes[i].GetFromLocation(), routes[i].GetToLocation())
		s.routeMap[key] = &model.Route{
			Distance: routes[i].Distance,
			Duration: routes[i].Duration,
			Price:    routes[i].Price,
		}
	}
}

// getRouteKey 生成路径的唯一键
func (s *MinCostMaxFlowScheduler) getRouteKey(from, to *model.Location) string {
	return fmt.Sprintf("%s:%s", from.String(), to.String())
}

// buildNetwork 构建网络流图
func (s *MinCostMaxFlowScheduler) buildNetwork(distWeight, timeWeight, balanceWeight float64) *MinCostMaxFlow {
	s.distanceWeight = distWeight
	s.timeWeight = timeWeight
	s.balanceWeight = balanceWeight

	trialCount := len(s.trialMap)
	lawyerCount := len(s.lawyerMap)

	// 节点编号: 0=源点, 1~trialCount=案件, trialCount+1~trialCount+lawyerCount=律师, 最后一个=汇点
	nodeCount := trialCount + lawyerCount + 2
	s.source = 0
	s.sink = nodeCount - 1

	mcf := NewMinCostMaxFlow(nodeCount)

	// 建立节点映射
	nodeIndex := 1
	for trialID := range s.trialMap {
		s.trialNodes[trialID] = nodeIndex
		s.nodeTrials[nodeIndex] = trialID
		nodeIndex++
	}

	for lawyerID := range s.lawyerMap {
		s.lawyerNodes[lawyerID] = nodeIndex
		s.nodeLawyers[nodeIndex] = lawyerID
		nodeIndex++
	}

	// 源点到案件：容量1，费用0
	for trialID := range s.trialMap {
		trialNode := s.trialNodes[trialID]
		mcf.AddEdge(s.source, trialNode, 1, 0)
	}

	// 案件到律师：容量1，费用根据计算
	for trialID := range s.trialMap {
		trialNode := s.trialNodes[trialID]
		for lawyerID := range s.lawyerMap {
			lawyerNode := s.lawyerNodes[lawyerID]
			cost := s.calculateAssignmentCost(trialID, lawyerID)
			if !math.IsInf(cost, 1) {
				mcf.AddEdge(trialNode, lawyerNode, 1, cost)
			}
		}
	}

	// 律师到汇点：容量为律师能处理的最大案件数，费用0
	for lawyerID := range s.lawyerMap {
		lawyerNode := s.lawyerNodes[lawyerID]
		maxCapacity := s.calculateMaxCapacityForLawyer(lawyerID)
		mcf.AddEdge(lawyerNode, s.sink, maxCapacity, 0)
	}

	return mcf
}

// calculateAssignmentCost 计算案件-律师分配的费用
func (s *MinCostMaxFlowScheduler) calculateAssignmentCost(trialID, lawyerID uint) float64 {
	trial := s.trialMap[trialID]
	lawyer := s.lawyerMap[lawyerID]
	court := s.courtMap[trial.CourtID]

	if trial == nil || lawyer == nil || court == nil {
		return math.Inf(1)
	}

	// 检查路径是否存在
	routeKey := s.getRouteKey(lawyer.GetLocation(), court.GetLocation())
	route, exists := s.routeMap[routeKey]
	if !exists || !route.Available() {
		return math.Inf(1)
	}

	// 计算往返距离和时间
	distanceKm := float64(route.Distance*2) / 1000.0 // 往返距离转换为公里
	durationMinutes := float64(route.Duration * 2)   // 往返时间(分钟)

	// 应用权重计算费用
	distanceCost := distanceKm * s.distanceWeight // 距离费用
	timeCost := durationMinutes * s.timeWeight    // 时间费用

	// 总费用
	totalCost := distanceCost + timeCost

	return totalCost
}

// calculateMaxCapacityForLawyer 计算律师能处理的最大案件数
func (s *MinCostMaxFlowScheduler) calculateMaxCapacityForLawyer(lawyerID uint) int {
	lawyer := s.lawyerMap[lawyerID]
	if lawyer == nil {
		return 0
	}

	// 使用配置的最大容量
	return s.maxCapacityPerLawyer
}

// updateNetworkCosts 更新网络中的费用
func (s *MinCostMaxFlowScheduler) updateNetworkCosts(mcf *MinCostMaxFlow, distWeight, timeWeight, balanceWeight float64) {
	s.distanceWeight = distWeight
	s.timeWeight = timeWeight
	s.balanceWeight = balanceWeight

	// 更新案件到律师的边费用
	for trialID := range s.trialMap {
		trialNode := s.trialNodes[trialID]
		edgeIndex := 0
		for lawyerID := range s.lawyerMap {
			lawyerNode := s.lawyerNodes[lawyerID]
			// 找到对应的边并更新费用
			for i, edge := range mcf.graph[trialNode] {
				if edge.to == lawyerNode {
					newCost := s.calculateAssignmentCost(trialID, lawyerID)
					if !math.IsInf(newCost, 1) {
						mcf.graph[trialNode][i].cost = newCost
						mcf.graph[lawyerNode][edge.rev].cost = -newCost
					}
					break
				}
			}
			edgeIndex++
		}
	}
}

// SetMaxCapacityPerLawyer 设置每个律师最大处理案件数
func (s *MinCostMaxFlowScheduler) SetMaxCapacityPerLawyer(maxCapacity int) {
	s.maxCapacityPerLawyer = maxCapacity
}

// scheduleWithNetwork 使用指定网络进行调度
func (s *MinCostMaxFlowScheduler) scheduleWithNetwork(mcf *MinCostMaxFlow, configName string) (*Solution, error) {
	// 重置网络流量
	s.resetNetworkFlow(mcf)

	// 运行算法
	maxFlow, minCost := mcf.MinCostMaxFlowAlgorithm(s.source, s.sink)

	if maxFlow == 0 {
		return nil, fmt.Errorf("no feasible solution found")
	}

	// 提取匹配结果
	matching := s.extractMatching(mcf)

	// 过滤时间冲突
	validMatching := s.resolveTimeConflicts(matching)

	if len(validMatching) == 0 {
		return nil, fmt.Errorf("no valid matching after conflict resolution")
	}

	distance, duration := s.calculateMetrics(validMatching)

	solution := &Solution{
		Matching:      validMatching,
		MatchCount:    len(validMatching),
		TotalCost:     minCost,
		TotalDistance: distance,
		TotalDuration: duration,
		ConfigName:    configName,
	}

	return solution, nil
}

// resetNetworkFlow 重置网络中的流量
func (s *MinCostMaxFlowScheduler) resetNetworkFlow(mcf *MinCostMaxFlow) {
	for i := 0; i < mcf.n; i++ {
		for j := range mcf.graph[i] {
			mcf.graph[i][j].flow = 0
		}
	}
}

// Schedule 主调度方法
func (s *MinCostMaxFlowScheduler) Schedule() map[uint][]map[uint]uint {
	fmt.Printf("===== MinCostMaxFlowScheduler 开始调度 =====\n")
	fmt.Printf("律师数量: %d, 法院数量: %d, 案件数量: %d, 路径数量: %d\n",
		len(s.lawyerMap), len(s.courtMap), len(s.trialMap), len(s.routeMap))

	// 使用默认权重配置
	configs := []struct {
		name          string
		distWeight    float64
		timeWeight    float64
		balanceWeight float64
	}{
		{"距离优先", 10.0, 1.0, 0.1},
		{"时间优先", 1.0, 10.0, 0.1},
		{"负载均衡", 2.0, 2.0, 10.0},
		{"均衡优化", 5.0, 5.0, 1.0},
		{"远距离惩罚", 20.0, 2.0, 0.5},
		{"快速到达", 2.0, 15.0, 0.5},
		{"工作量平衡", 3.0, 3.0, 8.0},
		{"距离敏感", 15.0, 3.0, 1.0},
		{"时间敏感", 3.0, 12.0, 1.0},
		{"综合平衡", 6.0, 6.0, 3.0},
	}

	// 使用新的调度方法
	weightConfigs := make([]WeightConfig, len(configs))
	for i, config := range configs {
		weightConfigs[i] = WeightConfig{
			Name:          config.name,
			DistWeight:    config.distWeight,
			TimeWeight:    config.timeWeight,
			BalanceWeight: config.balanceWeight,
		}
	}

	return s.ScheduleWithCustomWeights(weightConfigs)
}

// ScheduleWithCustomWeights 使用自定义权重配置进行调度
func (s *MinCostMaxFlowScheduler) ScheduleWithCustomWeights(customConfigs []WeightConfig) map[uint][]map[uint]uint {
	fmt.Printf("===== MinCostMaxFlowScheduler 自定义权重调度 =====\n")
	fmt.Printf("律师数量: %d, 法院数量: %d, 案件数量: %d, 路径数量: %d\n",
		len(s.lawyerMap), len(s.courtMap), len(s.trialMap), len(s.routeMap))

	if len(customConfigs) == 0 {
		fmt.Printf("❌ 没有提供权重配置\n")
		return map[uint][]map[uint]uint{0: {{}}}
	}

	// 使用第一个配置构建初始网络
	firstConfig := customConfigs[0]
	mcf := s.buildNetwork(firstConfig.DistWeight, firstConfig.TimeWeight, firstConfig.BalanceWeight)

	allSolutions := make([]Solution, 0)

	for i, config := range customConfigs {
		fmt.Printf("\n正在计算自定义方案 %d: %s (权重: 距离=%.1f, 时间=%.1f, 均衡=%.1f)\n",
			i+1, config.Name, config.DistWeight, config.TimeWeight, config.BalanceWeight)

		// 更新网络费用而不重建整个网络
		if i > 0 {
			s.updateNetworkCosts(mcf, config.DistWeight, config.TimeWeight, config.BalanceWeight)
		}

		// 使用网络进行调度
		solution, err := s.scheduleWithNetwork(mcf, config.Name)
		if err != nil {
			fmt.Printf("  ❌ %s\n", err.Error())
			continue
		}

		if !s.isDuplicateSolution(*solution, allSolutions) {
			allSolutions = append(allSolutions, *solution)
			fmt.Printf("  ✅ 找到方案：匹配数=%d，费用=%.2f，距离=%dm，时间=%d分钟\n",
				solution.MatchCount, solution.TotalCost, solution.TotalDistance, solution.TotalDuration)
		} else {
			fmt.Printf("  ⚠️ 方案重复，跳过\n")
		}
	}

	// 排序并选择结果
	return s.selectBestSolutions(allSolutions)
}

// GetDefaultWeightConfigs 获取默认的权重配置示例
func (s *MinCostMaxFlowScheduler) GetDefaultWeightConfigs() []WeightConfig {
	return []WeightConfig{
		{"距离优先", 10.0, 1.0, 0.1},
		{"时间优先", 1.0, 10.0, 0.1},
		{"负载均衡", 2.0, 2.0, 10.0},
		{"均衡优化", 5.0, 5.0, 1.0},
		{"远距离惩罚", 20.0, 2.0, 0.5},
		{"快速到达", 2.0, 15.0, 0.5},
		{"工作量平衡", 3.0, 3.0, 8.0},
		{"距离敏感", 15.0, 3.0, 1.0},
		{"时间敏感", 3.0, 12.0, 1.0},
		{"综合平衡", 6.0, 6.0, 3.0},
	}
}

// Solution 方案结构
type Solution struct {
	Matching      map[uint]uint
	MatchCount    int
	TotalCost     float64
	TotalDistance int
	TotalDuration int
	ConfigName    string
}

// extractMatching 从流网络中提取匹配结果
func (s *MinCostMaxFlowScheduler) extractMatching(mcf *MinCostMaxFlow) map[uint]uint {
	matching := make(map[uint]uint)

	for trialID, trialNode := range s.trialNodes {
		for _, edge := range mcf.graph[trialNode] {
			if edge.flow > 0 {
				if lawyerID, exists := s.nodeLawyers[edge.to]; exists {
					matching[trialID] = lawyerID
					break
				}
			}
		}
	}

	return matching
}

// resolveTimeConflicts 解决时间冲突
func (s *MinCostMaxFlowScheduler) resolveTimeConflicts(matching map[uint]uint) map[uint]uint {
	if len(matching) == 0 {
		return matching
	}

	// 按律师分组
	lawyerTrials := make(map[uint][]uint)
	for trialID, lawyerID := range matching {
		lawyerTrials[lawyerID] = append(lawyerTrials[lawyerID], trialID)
	}

	validMatching := make(map[uint]uint)

	// 为每个律师解决时间冲突
	for lawyerID, trials := range lawyerTrials {
		selectedTrials := s.selectNonConflictingTrials(lawyerID, trials)
		for _, trialID := range selectedTrials {
			validMatching[trialID] = lawyerID
		}
	}

	return validMatching
}

// selectNonConflictingTrials 选择无时间冲突的案件
func (s *MinCostMaxFlowScheduler) selectNonConflictingTrials(lawyerID uint, trials []uint) []uint {
	if len(trials) <= 1 {
		return trials
	}

	type trialInfo struct {
		trialID   uint
		startTime time.Time
		endTime   time.Time
		cost      float64
	}

	// 获取所有案件的时间信息
	timeInfos := make([]trialInfo, 0, len(trials))
	for _, trialID := range trials {
		trial := s.trialMap[trialID]
		if trial == nil {
			continue
		}

		startTime, endTime, err := s.calculateTravelTime(lawyerID, trial)
		if err != nil {
			continue
		}

		cost := s.calculateAssignmentCost(trialID, lawyerID)
		timeInfos = append(timeInfos, trialInfo{
			trialID:   trialID,
			startTime: startTime,
			endTime:   endTime,
			cost:      cost,
		})
	}

	// 按费用排序（优先选择费用低的）
	sort.Slice(timeInfos, func(i, j int) bool {
		return timeInfos[i].cost < timeInfos[j].cost
	})

	// 贪心选择无冲突的案件
	selected := make([]uint, 0)
	selectedTimes := make([][2]time.Time, 0)

	for _, info := range timeInfos {
		hasConflict := false
		for _, selectedTime := range selectedTimes {
			if s.isTimeOverlap(info.startTime, info.endTime, selectedTime[0], selectedTime[1]) {
				hasConflict = true
				break
			}
		}

		if !hasConflict {
			selected = append(selected, info.trialID)
			selectedTimes = append(selectedTimes, [2]time.Time{info.startTime, info.endTime})
		}
	}

	return selected
}

// calculateTravelTime 计算出行时间
func (s *MinCostMaxFlowScheduler) calculateTravelTime(lawyerID uint, trial *model.Trial) (time.Time, time.Time, error) {
	lawyer := s.lawyerMap[lawyerID]
	court := s.courtMap[trial.CourtID]

	if lawyer == nil || court == nil {
		return time.Time{}, time.Time{}, fmt.Errorf("lawyer or court not found")
	}

	routeKey := s.getRouteKey(lawyer.GetLocation(), court.GetLocation())
	route, exists := s.routeMap[routeKey]
	if !exists || !route.Available() {
		return time.Time{}, time.Time{}, fmt.Errorf("route not available")
	}

	// 计算出行时间（包含缓冲）
	travelDuration := time.Duration(float64(route.Duration)*Buffer) * time.Minute
	startTime := trial.StartTime.Add(-travelDuration)
	endTime := trial.EndTime.Add(travelDuration)

	return startTime, endTime, nil
}

// isTimeOverlap 检查时间重叠
func (s *MinCostMaxFlowScheduler) isTimeOverlap(start1, end1, start2, end2 time.Time) bool {
	return start1.Before(end2) && start2.Before(end1)
}

// calculateMetrics 计算方案指标
func (s *MinCostMaxFlowScheduler) calculateMetrics(matching map[uint]uint) (int, int) {
	totalDistance := 0
	totalDuration := 0

	for trialID, lawyerID := range matching {
		trial := s.trialMap[trialID]
		lawyer := s.lawyerMap[lawyerID]
		court := s.courtMap[trial.CourtID]

		if trial == nil || lawyer == nil || court == nil {
			continue
		}

		routeKey := s.getRouteKey(lawyer.GetLocation(), court.GetLocation())
		route, exists := s.routeMap[routeKey]
		if !exists || !route.Available() {
			continue
		}

		// 往返距离和时间
		totalDistance += route.Distance * 2
		totalDuration += route.Duration * 2

		// 庭审时间
		trialDuration := int(trial.EndTime.Sub(trial.StartTime).Minutes())
		totalDuration += trialDuration
	}

	return totalDistance, totalDuration
}

// isDuplicateSolution 检查重复方案
func (s *MinCostMaxFlowScheduler) isDuplicateSolution(solution Solution, solutions []Solution) bool {
	for _, existing := range solutions {
		if s.isMatchingEqual(solution.Matching, existing.Matching) {
			return true
		}
	}
	return false
}

// isMatchingEqual 检查匹配是否相等
func (s *MinCostMaxFlowScheduler) isMatchingEqual(m1, m2 map[uint]uint) bool {
	if len(m1) != len(m2) {
		return false
	}

	for k, v1 := range m1 {
		if v2, exists := m2[k]; !exists || v1 != v2 {
			return false
		}
	}

	return true
}

// selectBestSolutions 选择最佳方案
func (s *MinCostMaxFlowScheduler) selectBestSolutions(solutions []Solution) map[uint][]map[uint]uint {
	if len(solutions) == 0 {
		return map[uint][]map[uint]uint{0: {{}}}
	}

	// 按匹配数量降序，费用升序排序
	sort.Slice(solutions, func(i, j int) bool {
		if solutions[i].MatchCount != solutions[j].MatchCount {
			return solutions[i].MatchCount > solutions[j].MatchCount
		}
		return solutions[i].TotalCost < solutions[j].TotalCost
	})

	// 选择top10
	maxSolutions := 10
	if len(solutions) < maxSolutions {
		maxSolutions = len(solutions)
	}

	topSolutions := solutions[:maxSolutions]

	// 按匹配数量分组
	result := make(map[uint][]map[uint]uint)
	for _, solution := range topSolutions {
		matchCount := uint(solution.MatchCount)
		if result[matchCount] == nil {
			result[matchCount] = make([]map[uint]uint, 0)
		}
		result[matchCount] = append(result[matchCount], solution.Matching)
	}

	// 输出结果摘要
	fmt.Printf("\n===== Top%d 方案摘要 =====\n", maxSolutions)
	for i, solution := range topSolutions {
		fmt.Printf("方案 %d (%s): 匹配数量=%d，总费用=%.2f，总距离=%dm (%.2f公里)，总时间=%d分钟 (%.2f小时)\n",
			i+1, solution.ConfigName, solution.MatchCount, solution.TotalCost,
			solution.TotalDistance, float64(solution.TotalDistance)/1000,
			solution.TotalDuration, float64(solution.TotalDuration)/60)
	}

	// 按匹配数量分组输出
	fmt.Printf("\n===== 按匹配数量分组 =====\n")
	matchCounts := make([]uint, 0, len(result))
	for matchCount := range result {
		matchCounts = append(matchCounts, matchCount)
	}
	sort.Slice(matchCounts, func(i, j int) bool {
		return matchCounts[i] > matchCounts[j]
	})

	for _, matchCount := range matchCounts {
		schedules := result[matchCount]
		fmt.Printf("匹配数量 %d: %d个方案\n", matchCount, len(schedules))
	}

	return result
}
