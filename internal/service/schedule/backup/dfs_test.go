package schedule

import (
	"external-tool/internal/config"
	"external-tool/internal/model"
	"external-tool/internal/model/imap"
	"fmt"
	"os"
	"runtime/pprof"
	"testing"
	"time"
)

func newDFS() BestSchedule {
	db := config.GetDB()
	mapModel := imap.NewMapModel("tencent")

	var lawyers []model.Lawyer
	db.Model(&model.Lawyer{}).Where("lat != 0 AND lng != 0 AND status = 1 AND formatted_address <> '广东省深圳市福田区深南大道6025'").Order("id ASC").Limit(10).Find(&lawyers)
	var courts []model.Court
	db.Model(&model.Court{}).Where("lat != 0 AND lng != 0 AND status = 1").Order("id ASC").Find(&courts)
	var trials []model.Trial
	db.Model(&model.Trial{}).Where("status = 1").Order("id ASC").Limit(5).Find(&trials)
	var routes []model.Routes
	db.Model(&model.Routes{}).Where("status = 1").Order("id ASC").Find(&routes)

	dfs := NewDFS(mapModel, lawyers, courts, trials, routes)
	return dfs
}

func TestRealDFS(t *testing.T) {
	// 生成带时间戳的文件名
	cpuFileName := "./pprof/cpu_dfs.prof"
	memFileName := "./pprof/mem_dfs.prof"

	// 创建 CPU profile 文件
	cpuFile, err := os.Create(cpuFileName)
	if err != nil {
		t.Fatalf("无法创建 CPU profile 文件: %v", err)
	}
	defer cpuFile.Close()

	// 开始 CPU profiling
	if err := pprof.StartCPUProfile(cpuFile); err != nil {
		t.Fatalf("无法启动 CPU profile: %v", err)
	}
	defer pprof.StopCPUProfile()

	dfs := newDFS().(*DFS)

	// 输出基本信息
	fmt.Printf("=== DFS 性能测试 ===\n")
	fmt.Printf("时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Printf("律师数量: %d\n", len(dfs.lawyerMap))
	fmt.Printf("法院数量: %d\n", len(dfs.courtMap))
	fmt.Printf("庭审数量: %d\n", len(dfs.trialMap))
	fmt.Printf("路径数量: %d\n", len(dfs.routeMap))

	// 测量执行时间
	start := time.Now()
	r := dfs.Schedule()
	duration := time.Since(start)

	fmt.Printf("执行时间: %v\n", duration)
	fmt.Printf("结果数量: %d\n", len(r))

	// 创建内存 profile 文件
	memFile, err := os.Create(memFileName)
	if err != nil {
		t.Fatalf("无法创建内存 profile 文件: %v", err)
	}
	defer memFile.Close()

	// 记录内存 profile
	if err := pprof.WriteHeapProfile(memFile); err != nil {
		t.Fatalf("无法写入内存 profile: %v", err)
	}

	// 输出 profile 文件信息
	fmt.Printf("=== Profile 文件已生成 ===\n")
	fmt.Printf("CPU Profile: %s\n", cpuFileName)
	fmt.Printf("Memory Profile: %s\n", memFileName)
	fmt.Printf("使用方法:\n")
	fmt.Printf("  go tool pprof -http=:3000 %s\n", cpuFileName)
	fmt.Printf("  go tool pprof -http=:3001 %s\n", memFileName)
}
