package schedule

import (
	"external-tool/internal/config"
	"external-tool/internal/model"
	"external-tool/internal/model/imap"
	"fmt"
	"testing"
	"time"
)

func newMinCostMaxFlowScheduler() BestSchedule {
	db := config.GetDB()
	mapModel := imap.NewMapModel("tencent")

	var lawyers []model.Lawyer
	db.Model(&model.Lawyer{}).Where("lat != 0 AND lng != 0 AND status = 1 AND formatted_address <> '广东省深圳市福田区深南大道6025'").Order("id ASC").Find(&lawyers)
	var courts []model.Court
	db.Model(&model.Court{}).Where("lat != 0 AND lng != 0 AND status = 1").Order("id ASC").Find(&courts)
	var trials []model.Trial
	db.Model(&model.Trial{}).Where("status = 1").Order("id ASC").Find(&trials)
	var routes []model.Routes
	db.Model(&model.Routes{}).Where("status = 1").Order("id ASC").Find(&routes)

	scheduler := NewMinCostMaxFlowScheduler(mapModel, lawyers, courts, trials, routes)
	return scheduler
}

func TestMinCostMaxFlowBasic(t *testing.T) {
	scheduler := newMinCostMaxFlowScheduler().(*MinCostMaxFlowScheduler)

	fmt.Printf("===== MinCostMaxFlowScheduler 基础测试 =====\n")
	fmt.Printf("调度器类型: %T\n", scheduler)
	fmt.Printf("律师数量: %d\n", len(scheduler.lawyerMap))
	fmt.Printf("法院数量: %d\n", len(scheduler.courtMap))
	fmt.Printf("案件数量: %d\n", len(scheduler.trialMap))
	fmt.Printf("路径数量: %d\n", len(scheduler.routeMap))
	fmt.Printf("源点: %d, 汇点: %d\n", scheduler.source, scheduler.sink)

	result := scheduler.Schedule()

	fmt.Printf("\n===== 调度结果分析 =====\n")
	if len(result) == 0 {
		fmt.Printf("未找到任何匹配方案\n")
		return
	}

	// 验证结果
	totalTrials := len(scheduler.trialMap)
	for matchCount, schedules := range result {
		fmt.Printf("匹配数量 %d: %d个方案\n", matchCount, len(schedules))
		matchRate := float64(matchCount) / float64(totalTrials) * 100
		fmt.Printf("匹配率: %.2f%% (%d/%d)\n", matchRate, matchCount, totalTrials)

		for i, schedule := range schedules {
			fmt.Printf("  方案 %d:\n", i+1)

			// 验证时间冲突
			if !validateMatchingNoConflicts(scheduler, schedule) {
				fmt.Printf("    ❌ 存在时间冲突！\n")
			} else {
				fmt.Printf("    ✅ 无时间冲突\n")
			}

			// 计算费用信息
			distance, duration := scheduler.calculateMetrics(schedule)
			fmt.Printf("    总距离: %d米 (%.2f公里)\n", distance, float64(distance)/1000)
			fmt.Printf("    总时间: %d分钟 (%.2f小时)\n", duration, float64(duration)/60)
		}
	}
}

func TestMinCostMaxFlowTimeConflictValidation(t *testing.T) {
	scheduler := newMinCostMaxFlowScheduler().(*MinCostMaxFlowScheduler)

	fmt.Printf("===== 时间冲突验证测试 =====\n")

	result := scheduler.Schedule()

	// 详细验证每个方案的时间安排
	for matchCount, schedules := range result {
		if matchCount == 0 {
			continue
		}

		fmt.Printf("\n匹配数量 %d 的时间冲突验证:\n", matchCount)

		for i, schedule := range schedules {
			fmt.Printf("  方案 %d:\n", i+1)

			// 按律师分组分析
			lawyerTrials := make(map[uint][]uint)
			for trialID, lawyerID := range schedule {
				lawyerTrials[lawyerID] = append(lawyerTrials[lawyerID], trialID)
			}

			hasAnyConflict := false
			for lawyerID, trials := range lawyerTrials {
				lawyer := scheduler.lawyerMap[lawyerID]
				if lawyer == nil {
					continue
				}

				fmt.Printf("    律师%s(ID:%d): %d个案件\n", lawyer.Name, lawyerID, len(trials))

				if len(trials) > 1 {
					// 检查时间冲突
					hasConflict := checkDetailedTimeConflicts(scheduler, lawyerID, trials)
					if hasConflict {
						fmt.Printf("      ❌ 存在时间冲突！\n")
						hasAnyConflict = true
					} else {
						fmt.Printf("      ✅ 时间安排合理\n")
					}
				} else {
					fmt.Printf("      ✅ 单个案件，无冲突\n")
				}
			}

			if !hasAnyConflict {
				fmt.Printf("    🎉 方案整体无时间冲突\n")
			} else {
				fmt.Printf("    ⚠️  方案存在时间冲突问题\n")
			}
		}
	}
}

func TestMinCostMaxFlowTop10Quality(t *testing.T) {
	scheduler := newMinCostMaxFlowScheduler().(*MinCostMaxFlowScheduler)

	fmt.Printf("===== Top10方案质量测试 =====\n")

	result := scheduler.Schedule()

	for matchCount, schedules := range result {
		if matchCount == 0 {
			continue
		}

		fmt.Printf("\n匹配数量 %d 的方案质量分析:\n", matchCount)
		fmt.Printf("方案数量: %d\n", len(schedules))

		// 统计费用分布
		type solutionMetrics struct {
			index    int
			distance int
			duration int
			avgDist  float64
			avgTime  float64
		}

		metrics := make([]solutionMetrics, 0, len(schedules))

		for i, schedule := range schedules {
			distance, duration := scheduler.calculateMetrics(schedule)
			avgDist := float64(distance) / float64(len(schedule))
			avgTime := float64(duration) / float64(len(schedule))

			metrics = append(metrics, solutionMetrics{
				index:    i + 1,
				distance: distance,
				duration: duration,
				avgDist:  avgDist,
				avgTime:  avgTime,
			})
		}

		// 输出详细信息
		fmt.Printf("  方案费用详情:\n")
		for _, m := range metrics {
			fmt.Printf("    方案%d: 总距离=%d米, 总时间=%d分钟, 平均距离=%.0f米/案件, 平均时间=%.0f分钟/案件\n",
				m.index, m.distance, m.duration, m.avgDist, m.avgTime)
		}

		// 计算优化效果
		if len(metrics) > 1 {
			bestDist := metrics[0].distance
			worstDist := metrics[len(metrics)-1].distance
			bestTime := metrics[0].duration
			worstTime := metrics[len(metrics)-1].duration

			fmt.Printf("  优化效果:\n")
			if worstDist > bestDist {
				distOptimization := float64(worstDist-bestDist) / float64(worstDist) * 100
				fmt.Printf("    距离优化: 最优比最差节省%.2f%% (%d米)\n",
					distOptimization, worstDist-bestDist)
			}
			if worstTime > bestTime {
				timeOptimization := float64(worstTime-bestTime) / float64(worstTime) * 100
				fmt.Printf("    时间优化: 最优比最差节省%.2f%% (%d分钟)\n",
					timeOptimization, worstTime-bestTime)
			}
		}
	}
}

func TestMinCostMaxFlowLawyerLoadBalance(t *testing.T) {
	scheduler := newMinCostMaxFlowScheduler().(*MinCostMaxFlowScheduler)

	fmt.Printf("===== 律师负载均衡测试 =====\n")

	result := scheduler.Schedule()

	for matchCount, schedules := range result {
		if matchCount == 0 {
			continue
		}

		fmt.Printf("\n匹配数量 %d 的负载均衡分析:\n", matchCount)

		for i, schedule := range schedules {
			fmt.Printf("  方案 %d:\n", i+1)

			// 统计每个律师的负载
			lawyerLoad := make(map[uint]int)
			for _, lawyerID := range schedule {
				lawyerLoad[lawyerID]++
			}

			// 计算负载分布
			loadDistribution := make(map[int]int) // 负载数量 -> 律师数量
			maxLoad := 0
			minLoad := int(^uint(0) >> 1) // 最大int值
			totalLoad := 0

			for _, load := range lawyerLoad {
				loadDistribution[load]++
				if load > maxLoad {
					maxLoad = load
				}
				if load < minLoad {
					minLoad = load
				}
				totalLoad += load
			}

			avgLoad := float64(totalLoad) / float64(len(lawyerLoad))

			fmt.Printf("    负载统计: 最小=%d, 最大=%d, 平均=%.2f\n", minLoad, maxLoad, avgLoad)
			fmt.Printf("    负载分布:\n")
			for load := minLoad; load <= maxLoad; load++ {
				if count, exists := loadDistribution[load]; exists {
					fmt.Printf("      %d个案件: %d个律师\n", load, count)
				}
			}

			// 计算负载平衡指标
			variance := 0.0
			for _, load := range lawyerLoad {
				diff := float64(load) - avgLoad
				variance += diff * diff
			}
			variance /= float64(len(lawyerLoad))
			stdDev := variance // 简化的标准差计算

			fmt.Printf("    负载方差: %.2f (方差越小越均衡)\n", stdDev)
		}
	}
}

func TestCustomWeightConfiguration(t *testing.T) {
	scheduler := newMinCostMaxFlowScheduler().(*MinCostMaxFlowScheduler)

	fmt.Printf("===== 自定义权重配置示例 =====\n")

	// 定义你的自定义权重配置
	customConfigs := []WeightConfig{
		{
			Name:          "距离优先",
			DistWeight:    10.0,
			TimeWeight:    1.0,
			BalanceWeight: 0.1,
		},
		{
			Name:          "时间优先",
			DistWeight:    1.0,
			TimeWeight:    10.0,
			BalanceWeight: 0.1,
		},
		{
			Name:          "负载均衡",
			DistWeight:    2.0,
			TimeWeight:    2.0,
			BalanceWeight: 10.0,
		},
		{
			Name:          "均衡优化",
			DistWeight:    12.0,
			TimeWeight:    3.0,
			BalanceWeight: 0.5,
		},
	}

	// 使用自定义配置进行调度
	result := scheduler.ScheduleWithCustomWeights(customConfigs)

	fmt.Printf("\n===== 自定义权重调度结果 =====\n")
	for matchCount, schedules := range result {
		fmt.Printf("匹配数量 %d: %d个方案\n", matchCount, len(schedules))

		if matchCount > 0 {
			// 显示第一个方案的详细信息
			schedule := schedules[0]
			distance, duration := scheduler.calculateMetrics(schedule)
			fmt.Printf("  最佳方案: 总距离=%d米 (%.1f公里), 总时间=%d分钟 (%.1f小时)\n",
				distance, float64(distance)/1000, duration, float64(duration)/60)
		}
	}

	// 也可以获取默认配置作为参考
	defaultConfigs := scheduler.GetDefaultWeightConfigs()
	fmt.Printf("\n===== 可用的默认配置 =====\n")
	for i, config := range defaultConfigs {
		fmt.Printf("%d. %s: 距离权重=%.1f, 时间权重=%.1f, 均衡权重=%.1f\n",
			i+1, config.Name, config.DistWeight, config.TimeWeight, config.BalanceWeight)
	}
}

func TestMinCostMaxFlowFiveWeights(t *testing.T) {
	scheduler := newMinCostMaxFlowScheduler().(*MinCostMaxFlowScheduler)

	fmt.Printf("===== MinCostMaxFlow 五权重模板测试 =====\n")
	fmt.Printf("律师数量: %d, 法院数量: %d, 案件数量: %d, 路径数量: %d\n",
		len(scheduler.lawyerMap), len(scheduler.courtMap), len(scheduler.trialMap), len(scheduler.routeMap))

	// 五个权重模板
	customConfigs := []WeightConfig{
		{
			Name:          "距离优先",
			DistWeight:    15.0,
			TimeWeight:    1.0,
			BalanceWeight: 0.1,
		},
		{
			Name:          "时间优先",
			DistWeight:    1.0,
			TimeWeight:    15.0,
			BalanceWeight: 0.1,
		},
		{
			Name:          "负载均衡",
			DistWeight:    2.0,
			TimeWeight:    2.0,
			BalanceWeight: 15.0,
		},
		{
			Name:          "均衡优化",
			DistWeight:    12.0,
			TimeWeight:    3.0,
			BalanceWeight: 0.5,
		},
		{
			Name:          "综合平衡",
			DistWeight:    6.0,
			TimeWeight:    4.0,
			BalanceWeight: 2.0,
		},
	}

	// 使用自定义配置进行调度
	result := scheduler.ScheduleWithCustomWeights(customConfigs)

	fmt.Printf("\n===== 五权重模板调度结果 =====\n")

	if len(result) == 0 {
		fmt.Printf("未找到任何匹配方案\n")
		return
	}

	totalTrials := len(scheduler.trialMap)
	for matchCount, schedules := range result {
		fmt.Printf("匹配数量 %d: %d个方案\n", matchCount, len(schedules))

		for i, schedule := range schedules {
			fmt.Printf("  方案 %d: %v\n", i+1, schedule)

			// 验证时间冲突
			if !scheduler.validateTimeConflicts(schedule) {
				fmt.Printf("    ❌ 检测到时间冲突！\n")
				t.Errorf("方案 %d 存在时间冲突", i+1)
			} else {
				fmt.Printf("    ✅ 无时间冲突\n")
			}

			// 计算指标
			if len(schedule) > 0 {
				totalDistance, totalDuration := scheduler.calculateMetrics(schedule)
				fmt.Printf("    总距离: %d米 (%.2f公里)\n", totalDistance, float64(totalDistance)/1000)
				fmt.Printf("    总时间: %d分钟 (%.2f小时)\n", totalDuration, float64(totalDuration)/60)
			}
		}
	}

	fmt.Printf("\n===== 测试完成 =====\n")
	fmt.Printf("总案件数量: %d\n", totalTrials)
}

// validateMatchingNoConflicts 验证匹配结果没有时间冲突
func validateMatchingNoConflicts(scheduler *MinCostMaxFlowScheduler, matching map[uint]uint) bool {
	// 按律师分组
	lawyerTrials := make(map[uint][]uint)
	for trialID, lawyerID := range matching {
		lawyerTrials[lawyerID] = append(lawyerTrials[lawyerID], trialID)
	}

	// 检查每个律师的案件是否有时间冲突
	for lawyerID, trials := range lawyerTrials {
		if len(trials) <= 1 {
			continue // 单个案件不会冲突
		}

		// 获取所有案件的时间段
		timeSlots := make([][2]time.Time, 0, len(trials))
		for _, trialID := range trials {
			trial := scheduler.trialMap[trialID]
			if trial == nil {
				return false
			}

			startTime, endTime, err := scheduler.calculateTravelTime(lawyerID, trial)
			if err != nil {
				return false
			}

			timeSlots = append(timeSlots, [2]time.Time{startTime, endTime})
		}

		// 检查任意两个时间段是否重叠
		for i := 0; i < len(timeSlots); i++ {
			for j := i + 1; j < len(timeSlots); j++ {
				if scheduler.isTimeOverlap(timeSlots[i][0], timeSlots[i][1],
					timeSlots[j][0], timeSlots[j][1]) {
					return false // 存在冲突
				}
			}
		}
	}

	return true
}

// checkDetailedTimeConflicts 详细检查律师的案件时间冲突
func checkDetailedTimeConflicts(scheduler *MinCostMaxFlowScheduler, lawyerID uint, trials []uint) bool {
	if len(trials) <= 1 {
		return false
	}

	// 获取所有案件的时间段并排序
	type trialTimeInfo struct {
		trialID   uint
		startTime time.Time
		endTime   time.Time
	}

	timeInfos := make([]trialTimeInfo, 0, len(trials))

	for _, trialID := range trials {
		trial := scheduler.trialMap[trialID]
		if trial == nil {
			continue
		}

		startTime, endTime, err := scheduler.calculateTravelTime(lawyerID, trial)
		if err != nil {
			continue
		}

		timeInfos = append(timeInfos, trialTimeInfo{
			trialID:   trialID,
			startTime: startTime,
			endTime:   endTime,
		})
	}

	// 按开始时间排序
	for i := 0; i < len(timeInfos); i++ {
		for j := i + 1; j < len(timeInfos); j++ {
			if timeInfos[i].startTime.After(timeInfos[j].startTime) {
				timeInfos[i], timeInfos[j] = timeInfos[j], timeInfos[i]
			}
		}
	}

	// 输出时间安排详情
	fmt.Printf("        时间安排详情:\n")
	for _, info := range timeInfos {
		trial := scheduler.trialMap[info.trialID]
		fmt.Printf("          案件%d: %s ~ %s (庭审: %s ~ %s)\n",
			info.trialID,
			info.startTime.Format("15:04"),
			info.endTime.Format("15:04"),
			trial.StartTime.Format("15:04"),
			trial.EndTime.Format("15:04"))
	}

	// 检查相邻时间段是否重叠
	hasConflict := false
	for i := 0; i < len(timeInfos)-1; i++ {
		if scheduler.isTimeOverlap(
			timeInfos[i].startTime, timeInfos[i].endTime,
			timeInfos[i+1].startTime, timeInfos[i+1].endTime) {
			fmt.Printf("          ❌ 案件%d和案件%d时间冲突！\n",
				timeInfos[i].trialID, timeInfos[i+1].trialID)
			hasConflict = true
		}
	}

	return hasConflict
}

// validateTimeConflicts 验证方案是否存在时间冲突
func (s *MinCostMaxFlowScheduler) validateTimeConflicts(schedule map[uint]uint) bool {
	// 按律师分组
	lawyerTrials := make(map[uint][]uint)
	for trialID, lawyerID := range schedule {
		lawyerTrials[lawyerID] = append(lawyerTrials[lawyerID], trialID)
	}

	// 检查每个律师的时间冲突
	for lawyerID, trials := range lawyerTrials {
		if len(trials) <= 1 {
			continue
		}

		// 获取所有案件的时间窗口
		timeWindows := make([][2]time.Time, 0, len(trials))
		for _, trialID := range trials {
			trial := s.trialMap[trialID]
			if trial == nil {
				continue
			}

			startTime, endTime, err := s.calculateTravelTime(lawyerID, trial)
			if err != nil {
				continue
			}

			timeWindows = append(timeWindows, [2]time.Time{startTime, endTime})
		}

		// 检查时间窗口是否有重叠
		for i := 0; i < len(timeWindows); i++ {
			for j := i + 1; j < len(timeWindows); j++ {
				if s.isTimeOverlap(timeWindows[i][0], timeWindows[i][1],
					timeWindows[j][0], timeWindows[j][1]) {
					return false
				}
			}
		}
	}

	return true
}
