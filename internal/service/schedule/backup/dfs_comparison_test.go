package schedule

import (
	"external-tool/internal/config"
	"external-tool/internal/model"
	"external-tool/internal/model/imap"
	"fmt"
	"testing"
	"time"
)

func TestDFSComparison(t *testing.T) {
	// 创建测试数据集
	db := config.GetDB()
	mapModel := imap.NewMapModel("tencent")

	var lawyers []model.Lawyer
	db.Model(&model.Lawyer{}).Where("lat != 0 AND lng != 0 AND status = 1 AND formatted_address <> '广东省深圳市福田区深南大道6025'").Order("id ASC").Limit(10).Find(&lawyers)
	var courts []model.Court
	db.Model(&model.Court{}).Where("lat != 0 AND lng != 0 AND status = 1").Order("id ASC").Find(&courts)
	var trials []model.Trial
	db.Model(&model.Trial{}).Where("status = 1").Order("id ASC").Limit(5).Find(&trials)
	var routes []model.Routes
	db.Model(&model.Routes{}).Where("status = 1").Order("id ASC").Find(&routes)

	fmt.Printf("=== DFS 对比测试 ===\n")
	fmt.Printf("律师数量: %d\n", len(lawyers))
	fmt.Printf("法院数量: %d\n", len(courts))
	fmt.Printf("庭审数量: %d\n", len(trials))
	fmt.Printf("路径数量: %d\n", len(routes))

	// 测试原始DFS
	fmt.Printf("\n--- 原始 DFS 测试 ---\n")
	originalDFS := NewDFS(mapModel, lawyers, courts, trials, routes).(*DFS)

	start := time.Now()
	originalResult := originalDFS.Schedule()
	originalDuration := time.Since(start)

	originalTotalSolutions := 0
	for _, solutions := range originalResult {
		originalTotalSolutions += len(solutions)
	}

	fmt.Printf("原始DFS执行时间: %v\n", originalDuration)
	fmt.Printf("原始DFS最大匹配数量: %d\n", originalDFS.maxTotalMatches)
	fmt.Printf("原始DFS解决方案数量: %d\n", originalDFS.solutionCount)
	fmt.Printf("原始DFS实际返回数量: %d\n", originalTotalSolutions)

	// 测试优化DFS
	fmt.Printf("\n--- 优化 DFS 测试 ---\n")
	optimizedDFS := NewOptimizedDFS(mapModel, lawyers, courts, trials, routes).(*OptimizedDFS)

	start = time.Now()
	optimizedResult := optimizedDFS.Schedule()
	optimizedDuration := time.Since(start)

	optimizedTotalSolutions := 0
	for _, solutions := range optimizedResult {
		optimizedTotalSolutions += len(solutions)
	}

	fmt.Printf("优化DFS执行时间: %v\n", optimizedDuration)
	fmt.Printf("优化DFS最大匹配数量: %d\n", optimizedDFS.maxTotalMatches)
	fmt.Printf("优化DFS解决方案数量: %d\n", optimizedDFS.solutionCount)
	fmt.Printf("优化DFS实际返回数量: %d\n", optimizedTotalSolutions)

	// 验证早期终止是否生效
	fmt.Printf("\n--- 验证结果 ---\n")
	if optimizedDFS.solutionCount > 20 {
		t.Errorf("优化版本早期终止失败: 解决方案数量 = %d > 20", optimizedDFS.solutionCount)
	} else {
		fmt.Printf("✓ 早期终止生效：优化版本解决方案数量 = %d <= 20\n", optimizedDFS.solutionCount)
	}

	// 如果有匹配，比较最大匹配数量（优化版本可能由于早期终止而找到较少匹配）
	if originalDFS.maxTotalMatches > 0 && optimizedDFS.maxTotalMatches > 0 {
		if originalDFS.maxTotalMatches != optimizedDFS.maxTotalMatches {
			fmt.Printf("⚠️ 最大匹配数量不同: 原始 = %d, 优化 = %d\n", originalDFS.maxTotalMatches, optimizedDFS.maxTotalMatches)
			fmt.Printf("   说明：优化版本由于早期终止，可能未找到所有最优解\n")
		} else {
			fmt.Printf("✓ 最大匹配数量一致: %d\n", originalDFS.maxTotalMatches)
		}
	}

	// 性能对比
	if originalDuration > 0 && optimizedDuration > 0 {
		speedup := float64(originalDuration) / float64(optimizedDuration)
		fmt.Printf("性能提升: %.2fx 倍\n", speedup)
	}

	// 显示解决方案限制效果
	fmt.Printf("解决方案限制效果: 原始 %d 个 -> 优化 %d 个\n", originalDFS.solutionCount, optimizedDFS.solutionCount)

	fmt.Printf("\n=== 对比测试完成 ===\n")
}
