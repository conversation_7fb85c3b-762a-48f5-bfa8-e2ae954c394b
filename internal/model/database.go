package model

import (
	"time"

	"gorm.io/gorm"
)

// 律师信息表
type Lawyer struct {
	ID               uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	Name             string    `gorm:"type:varchar(50);not null;comment:姓名" json:"name"`
	Phone            string    `gorm:"type:varchar(20);not null;comment:手机号" json:"phone"`
	IDCard           string    `gorm:"type:varchar(18);not null;uniqueIndex:uk_id_card;comment:身份证号" json:"id_card"`
	Employment       string    `gorm:"type:varchar(10);not null;comment:雇佣关系授薪/外包" json:"employment"`
	Ability          string    `gorm:"type:varchar(10);not null;comment:职能类别开庭/办案" json:"ability"`
	Status           int       `gorm:"type:tinyint;not null;default:1;comment:状态0-禁用1-启用" json:"status"`
	OriginalAddress  string    `gorm:"type:varchar(500);not null;comment:原始地址信息" json:"original_address"`
	FormattedAddress string    `gorm:"type:varchar(500);not null;default:'';comment:格式化后地址" json:"formatted_address"`
	Province         string    `gorm:"type:varchar(50);not null;default:'';comment:省份" json:"province"`
	City             string    `gorm:"type:varchar(50);not null;default:'';comment:城市" json:"city"`
	District         string    `gorm:"type:varchar(50);not null;default:'';comment:区县" json:"district"`
	Street           string    `gorm:"type:varchar(100);not null;default:'';comment:街道" json:"street"`
	StreetNumber     string    `gorm:"type:varchar(50);not null;default:'';comment:街道号码" json:"street_number"`
	Lng              float64   `gorm:"type:decimal(10,7);not null;default:0;comment:经度" json:"lng"`
	Lat              float64   `gorm:"type:decimal(10,7);not null;default:0;comment:纬度" json:"lat"`
	Reliability      int       `gorm:"type:tinyint;not null;default:0;comment:0-10可信度>=7可信任" json:"reliability"`
	CreatedAt        time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt        time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// 指定表名
func (Lawyer) TableName() string {
	return "lawyers"
}

func (l *Lawyer) GetLocation() *Location {
	return &Location{
		Lat:              l.Lat,
		Lng:              l.Lng,
		FormattedAddress: l.FormattedAddress,
	}
}

// 法院信息表
type Court struct {
	ID               uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	Name             string    `gorm:"type:varchar(100);not null;uniqueIndex:uk_name;comment:名称" json:"name"`
	Status           int       `gorm:"type:tinyint;not null;default:1;comment:状态0-禁用1-启用" json:"status"`
	OriginalAddress  string    `gorm:"type:varchar(500);not null;comment:原始地址信息" json:"original_address"`
	FormattedAddress string    `gorm:"type:varchar(500);not null;default:'';comment:格式化后地址" json:"formatted_address"`
	Province         string    `gorm:"type:varchar(50);not null;default:'';comment:省份" json:"province"`
	City             string    `gorm:"type:varchar(50);not null;default:'';comment:城市" json:"city"`
	District         string    `gorm:"type:varchar(50);not null;default:'';comment:区县" json:"district"`
	Street           string    `gorm:"type:varchar(100);not null;default:'';comment:街道" json:"street"`
	StreetNumber     string    `gorm:"type:varchar(50);not null;default:'';comment:街道号码" json:"street_number"`
	Lng              float64   `gorm:"type:decimal(10,7);not null;default:0;comment:经度" json:"lng"`
	Lat              float64   `gorm:"type:decimal(10,7);not null;default:0;comment:纬度" json:"lat"`
	Reliability      int       `gorm:"type:tinyint;not null;default:0;comment:0-10可信度>=7可信任" json:"reliability"`
	CreatedAt        time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt        time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// 指定表名
func (Court) TableName() string {
	return "courts"
}

func (c *Court) GetLocation() *Location {
	return &Location{
		Lat:              c.Lat,
		Lng:              c.Lng,
		FormattedAddress: c.FormattedAddress,
	}
}

// 开庭信息表
type Trial struct {
	ID        uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	CaseID    uint      `gorm:"not null;comment:案件ID" json:"case_id"`
	CourtID   uint      `gorm:"not null;comment:法院ID" json:"court_id"`
	Status    int       `gorm:"type:tinyint;not null;default:0;comment:状态0-待登记1-待排庭2-待滴滴3-待支付4-待开庭5-开庭缺席6-已开庭7-已取消" json:"status"`
	Mode      int       `gorm:"type:tinyint;not null;default:0;comment:开庭方式0-待定1-线上2-线下" json:"mode"`
	Type      int       `gorm:"type:tinyint;not null;comment:庭审类型1-民初庭2-调解庭" json:"type"`
	StartTime time.Time `gorm:"comment:开始时间" json:"start_time"`
	EndTime   time.Time `gorm:"comment:结束时间" json:"end_time"`
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// 指定表名
func (Trial) TableName() string {
	return "trials"
}

// 路线计算表
type Routes struct {
	ID                   uint       `gorm:"primaryKey;autoIncrement" json:"id"`
	Type                 string     `gorm:"type:varchar(50);not null;uniqueIndex:uk_type_from_to_time;comment:类型lawyer_to_court,court_to_court,court_to_lawyer" json:"type"`
	FromStartTime        *time.Time `gorm:"uniqueIndex:uk_type_from_to_time;comment:出发地开始时间" json:"from_start_time"`
	FromEndTime          *time.Time `gorm:"uniqueIndex:uk_type_from_to_time;comment:出发地结束时间" json:"from_end_time"`
	ToStartTime          *time.Time `gorm:"uniqueIndex:uk_type_from_to_time;comment:目标地开始时间" json:"to_start_time"`
	ToEndTime            *time.Time `gorm:"uniqueIndex:uk_type_from_to_time;comment:目标地结束时间" json:"to_end_time"`
	FromLat              float64    `gorm:"type:decimal(10,7);uniqueIndex:uk_type_from_to_time;comment:出发地纬度" json:"from_lat"`
	FromLng              float64    `gorm:"type:decimal(10,7);uniqueIndex:uk_type_from_to_time;comment:出发地经度" json:"from_lng"`
	FromFormattedAddress string     `gorm:"type:varchar(500);not null;default:'';comment:出发地格式化地址" json:"from_formatted_address"`
	ToLat                float64    `gorm:"type:decimal(10,7);uniqueIndex:uk_type_from_to_time;comment:目标地纬度" json:"to_lat"`
	ToLng                float64    `gorm:"type:decimal(10,7);uniqueIndex:uk_type_from_to_time;comment:目标地经度" json:"to_lng"`
	ToFormattedAddress   string     `gorm:"type:varchar(500);not null;default:'';comment:目标地格式化地址" json:"to_formatted_address"`
	Distance             int        `gorm:"not null;default:0;comment:距离单位: 米" json:"distance"`
	Duration             int        `gorm:"not null;default:0;comment:时间单位: 分钟" json:"duration"`
	Cost                 float64    `gorm:"type:decimal(10,2);default:0;comment:差旅成本" json:"cost"`
	Status               int        `gorm:"type:tinyint;not null;default:1;comment:状态0-禁用1-可用2-人工修正数据" json:"status"`
	Remark               string     `gorm:"type:varchar(500);default:'';comment:备注" json:"remark"`
	Detail               string     `gorm:"type:text;comment:路线规划结果" json:"detail"`
	CreatedAt            time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt            time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
}

// 指定表名
func (Routes) TableName() string {
	return "routes"
}

func (r *Routes) GetFromLocation() *Location {
	return &Location{
		Lat:              r.FromLat,
		Lng:              r.FromLng,
		FormattedAddress: r.FromFormattedAddress,
	}
}

func (r *Routes) GetToLocation() *Location {
	return &Location{
		Lat:              r.ToLat,
		Lng:              r.ToLng,
		FormattedAddress: r.ToFormattedAddress,
	}
}

// 数据库连接和初始化
func InitDatabase(db *gorm.DB) error {
	// 自动迁移表结构
	err := db.AutoMigrate(
		&Lawyer{},
		&Court{},
		&Trial{},
		&Routes{},
	)
	if err != nil {
		return err
	}

	return nil
}
