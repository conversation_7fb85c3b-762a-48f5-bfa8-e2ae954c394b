package imap

import (
	"encoding/json"
	"external-tool/internal/config"
	"external-tool/internal/model"
	"fmt"
	"strings"
	"time"
)

type MapModel interface {
	Geocoder(address string) (model.Location, model.AddressComponents, error)
	DirectionDriving(from, to, policy string, departure_time time.Time) ([]model.Route, error)
	DirectionTransit(from, to *model.Location, policy string, departure_time time.Time) ([]model.Route, string, error)
	DirectionTransitV2(from, to *model.Location, policy string, departure_time time.Time) ([]model.Route, string, error)
}

var match = map[string]MapModel{
	"tencent": NewTencentModel(),
}

func NewMapModel(name string) MapModel {
	return match[name]
}

type TencentModel struct {
	key    string
	domain string
}

func NewTencentModel() MapModel {
	return &TencentModel{
		key:    "FODBZ-4SI6W-NFURJ-3RX7Q-OGUWO-KPBKW",
		domain: "https://apis.map.qq.com",
	}
}

func (m *TencentModel) Geocoder(address string) (model.Location, model.AddressComponents, error) {
	url := fmt.Sprintf("%s/ws/geocoder/v1/", m.domain)

	resp, err := config.GetHTTPClient().R().
		SetQueryParams(map[string]string{
			"address": address,
			"key":     m.key,
		}).
		SetResult(&GeocoderResponse{}).
		Get(url)

	if err != nil {
		return model.Location{}, model.AddressComponents{}, fmt.Errorf("获取坐标失败: %w", err)
	}

	result := resp.Result().(*GeocoderResponse)
	if result.Status != 0 {
		return model.Location{}, model.AddressComponents{}, fmt.Errorf("获取坐标失败: %s", result.Message)
	}

	if result.Result.Reliability < 7 {
		fmt.Printf("[%s] result reliability is less than 7\n", address)
	}

	location := model.Location{
		Lat: result.Result.Location.Lat,
		Lng: result.Result.Location.Lng,
	}

	components := model.AddressComponents{
		Province:     result.Result.AddressComponents.Province,
		City:         result.Result.AddressComponents.City,
		District:     result.Result.AddressComponents.District,
		Street:       result.Result.AddressComponents.Street,
		StreetNumber: result.Result.AddressComponents.StreetNumber,
	}

	return location, components, nil
}

func (m *TencentModel) DirectionDriving(from, to, policy string, departure_time time.Time) ([]model.Route, error) {
	// url := fmt.Sprintf("%s/ws/direction/v1/driving/", m.domain)
	return nil, nil
}

func (m *TencentModel) DirectionTransit(from, to *model.Location, policy string, departure_time time.Time) ([]model.Route, string, error) {
	url := fmt.Sprintf("%s/ws/direction/v1/transit/", m.domain)

	resp, err := config.GetHTTPClient().R().
		SetQueryParams(map[string]string{
			"from":      m.formatLocation(from),
			"to":        m.formatLocation(to),
			"policy":    policy,
			"departure": fmt.Sprintf("%d", departure_time.Unix()),
			"key":       m.key,
		}).
		SetResult(&DirectionResponse{}).
		Get(url)

	if err != nil {
		return nil, "", fmt.Errorf("direction transit request failed: %w", err)
	}

	result := resp.Result().(*DirectionResponse)
	if result.Status != 0 && result.Status != 348 {
		return nil, "", fmt.Errorf("direction transit API error: %s", result.Message)
	}

	var routes []model.Route
	for _, route := range result.Result.Routes {
		routes = append(routes, model.Route{
			Distance: route.Distance,
			Duration: route.Duration,
			Price:    route.Price,
		})
	}
	return routes, string(resp.Body()), nil
}

func (m *TencentModel) formatLocation(location *model.Location) string {
	return fmt.Sprintf("%f,%f", location.Lat, location.Lng)
}

type GeocoderResponse struct {
	Status  int    `json:"status"`
	Message string `json:"message"`
	Result  struct {
		Location struct {
			Lat float64 `json:"lat"`
			Lng float64 `json:"lng"`
		} `json:"location"`
		AddressComponents struct {
			Province     string `json:"province"`
			City         string `json:"city"`
			District     string `json:"district"`
			Street       string `json:"street"`
			StreetNumber string `json:"street_number"`
		} `json:"address_components"`
		Reliability int `json:"reliability"`
	} `json:"result"`
}

type DirectionResponse struct {
	Status  int    `json:"status"`
	Message string `json:"message"`
	Result  struct {
		// Routes json.RawMessage `json:"routes"`
		Routes []struct {
			Distance int             `json:"distance"`
			Duration int             `json:"duration"`
			Price    int             `json:"price"`
			Steps    json.RawMessage `json:"steps"`
		} `json:"routes"`
	} `json:"result"`
}

// TencentDirectionStep 腾讯地图API返回的路径步骤
type TencentDirectionStep struct {
	Mode      string `json:"mode"`      // 出行方式: WALKING, TRANSIT
	Distance  int    `json:"distance"`  // 距离（米）
	Duration  int    `json:"duration"`  // 时间（分钟）
	Direction string `json:"direction"` // 方向
	// 公交相关
	Lines []struct {
		Vehicle       string `json:"vehicle"`        // 交通工具类型: BUS, SUBWAY, RAIL
		Title         string `json:"title"`          // 线路名称
		Distance      int    `json:"distance"`       // 距离
		Duration      int    `json:"duration"`       // 时间
		Price         int    `json:"price"`          // 价格
		StartTime     string `json:"start_time"`     // 首班时间 (公交/地铁)
		EndTime       string `json:"end_time"`       // 末班时间 (公交/地铁)
		DepartureTime string `json:"departure_time"` // 出发时间 (火车)
		ArriveTime    string `json:"arrive_time"`    // 到达时间 (火车)
		Geton         struct {
			Title    string `json:"title"` // 上车站名
			Location struct {
				Lat float64 `json:"lat"`
				Lng float64 `json:"lng"`
			} `json:"location"`
		} `json:"geton"`
		Getoff struct {
			Title    string `json:"title"` // 下车站名
			Location struct {
				Lat float64 `json:"lat"`
				Lng float64 `json:"lng"`
			} `json:"location"`
		} `json:"getoff"`
	} `json:"lines"`
}

// DirectionTransitV2 实现V2版本的公交路径规划，生成详细的路径信息
func (m *TencentModel) DirectionTransitV2(from, to *model.Location, policy string, departure_time time.Time) ([]model.Route, string, error) {
	url := fmt.Sprintf("%s/ws/direction/v1/transit/", m.domain)

	resp, err := config.GetHTTPClient().R().
		SetQueryParams(map[string]string{
			"from":      m.formatLocation(from),
			"to":        m.formatLocation(to),
			"policy":    policy,
			"departure": fmt.Sprintf("%d", departure_time.Unix()),
			"key":       m.key,
		}).
		SetResult(&DirectionResponse{}).
		Get(url)

	if err != nil {
		return nil, "", fmt.Errorf("direction transit v2 request failed: %w", err)
	}

	result := resp.Result().(*DirectionResponse)
	if result.Status != 0 && result.Status != 348 {
		return nil, "", fmt.Errorf("direction transit v2 API error: %s", result.Message)
	}

	var routes []model.Route
	for _, route := range result.Result.Routes {
		routeWithRemark := model.Route{
			Distance: route.Distance,
			Duration: route.Duration,
			Price:    route.Price,
		}

		routes = append(routes, routeWithRemark)
	}

	// 生成第一条路径的备注作为返回值
	var remarkStr string
	if len(result.Result.Routes) > 0 {
		remarkStr = m.parseStepsToRemark(result.Result.Routes[0].Steps)
	}

	return routes, remarkStr, nil
}

// parseStepsToRemark 解析步骤信息生成备注
func (m *TencentModel) parseStepsToRemark(stepsRaw json.RawMessage) string {
	// 尝试解析步骤信息
	var steps []TencentDirectionStep
	if err := json.Unmarshal(stepsRaw, &steps); err != nil {
		// 如果解析失败，返回简单的备注
		return "路径解析失败"
	}

	if len(steps) == 0 {
		return "暂无详细路径信息"
	}

	var remarkParts []string
	for _, step := range steps {
		var stepRemark string

		switch step.Mode {
		case "WALKING":
			stepRemark = fmt.Sprintf("步行(%.1fKM|%dMIN)",
				float64(step.Distance)/1000.0, step.Duration)
		case "TRANSIT":
			if len(step.Lines) > 0 {
				line := step.Lines[0]
				vehicleType := m.getVehicleTypeName(line.Vehicle)
				if line.Vehicle == "RAIL" {
					// 火车使用departure_time和arrive_time字段
					stepRemark = fmt.Sprintf("%s-%s(%s->%s|%.1fKM|%dMIN|%s~%s)",
						vehicleType, line.Title,
						line.Geton.Title, line.Getoff.Title,
						float64(line.Distance)/1000.0, line.Duration,
						line.DepartureTime, line.ArriveTime)
				} else {
					stepRemark = fmt.Sprintf("%s(%s->%s|%.1fKM|%dMIN)",
						vehicleType,
						line.Geton.Title, line.Getoff.Title,
						float64(line.Distance)/1000.0, line.Duration)
				}
			} else {
				stepRemark = fmt.Sprintf("公交(%.1fKM|%dMIN)",
					float64(step.Distance)/1000.0, step.Duration)
			}
		case "DRIVING":
			stepRemark = fmt.Sprintf("驾车(%.1fKM|%dMIN)",
				float64(step.Distance)/1000.0, step.Duration)
		default:
			stepRemark = fmt.Sprintf("%s(%.1fKM|%dMIN)",
				step.Mode, float64(step.Distance)/1000.0, step.Duration)
		}

		remarkParts = append(remarkParts, stepRemark)
	}

	return strings.Join(remarkParts, "->")
}

// getVehicleTypeName 获取交通工具类型名称
func (m *TencentModel) getVehicleTypeName(vehicleType string) string {
	switch vehicleType {
	case "BUS":
		return "公交"
	case "SUBWAY":
		return "地铁"
	case "RAIL":
		return "火车"
	default:
		return "交通工具"
	}
}
