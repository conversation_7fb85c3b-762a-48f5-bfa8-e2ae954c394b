package imap

import (
	"external-tool/internal/model"
	"strings"
	"testing"
	"time"
)

// TestDirectionTransitV2 测试DirectionTransitV2接口
func TestDirectionTransitV2(t *testing.T) {
	// 创建腾讯地图模型实例
	mapModel := NewTencentModel()

	// 定义测试用的地点
	from := &model.Location{
		Lat:              39.904202,
		Lng:              116.407394,
		FormattedAddress: "北京市东城区天安门广场",
	}

	to := &model.Location{
		Lat:              31.230391,
		Lng:              121.473701,
		FormattedAddress: "上海市黄浦区人民广场",
	}

	// 设置明天早上7点作为出发时间
	tomorrow := time.Now().Add(time.Hour * 24)
	departureTime := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 7, 0, 0, 0, tomorrow.Location())

	// 调用DirectionTransitV2接口
	routes, remark, err := mapModel.DirectionTransitV2(from, to, "RECOMMEND", departureTime)

	// 验证结果
	if err != nil {
		t.Logf("DirectionTransitV2 返回错误: %v", err)
		// 在某些情况下，API可能返回错误（如没有公交路线），这是正常的
		return
	}

	if len(routes) == 0 {
		t.Log("没有找到路线，但这可能是正常的（某些地点之间没有公交）")
		return
	}

	// 输出结果用于手动验证
	t.Logf("找到 %d 条路线", len(routes))
	for i, route := range routes {
		t.Logf("路线 %d: 距离=%d米, 时间=%d分钟, 价格=%d", i+1, route.Distance, route.Duration, route.Price)
	}

	// 验证备注信息
	if remark == "" {
		t.Log("备注信息为空")
	} else {
		t.Logf("备注信息: %s", remark)

		// 验证备注格式（应该包含箭头分隔符）
		if len(remark) > 0 && remark != "路径解析失败" && remark != "暂无详细路径信息" {
			t.Log("备注格式验证通过")
		}
	}
}

// TestDirectionTransitV2_SameCity 测试同城市路径
func TestDirectionTransitV2_SameCity(t *testing.T) {
	mapModel := NewTencentModel()

	// 北京市内的两个地点
	from := &model.Location{
		Lat:              39.904202,
		Lng:              116.407394,
		FormattedAddress: "北京市东城区天安门广场",
	}

	to := &model.Location{
		Lat:              39.913869,
		Lng:              116.391248,
		FormattedAddress: "北京市西城区西直门",
	}

	// 设置明天早上7点作为出发时间
	tomorrow := time.Now().Add(time.Hour * 24)
	departureTime := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 7, 0, 0, 0, tomorrow.Location())

	routes, remark, err := mapModel.DirectionTransitV2(from, to, "RECOMMEND", departureTime)

	if err != nil {
		t.Logf("DirectionTransitV2 返回错误: %v", err)
		return
	}

	if len(routes) == 0 {
		t.Log("没有找到路线")
		return
	}

	t.Logf("同城测试 - 找到 %d 条路线", len(routes))
	t.Logf("同城测试 - 备注信息: %s", remark)

	// 验证基本信息
	if routes[0].Distance > 0 {
		t.Log("距离信息正常")
	}

	if routes[0].Duration > 0 {
		t.Log("时间信息正常")
	}
}

// TestParseStepsToRemark 测试步骤解析功能
func TestParseStepsToRemark(t *testing.T) {
	mapModel := NewTencentModel().(*TencentModel)

	// 测试空步骤
	emptySteps := []byte(`[]`)
	remark := mapModel.parseStepsToRemark(emptySteps)
	if remark != "暂无详细路径信息" {
		t.Errorf("空步骤解析错误，期望 '暂无详细路径信息'，得到 '%s'", remark)
	}

	// 测试无效JSON
	invalidJSON := []byte(`invalid json`)
	remark = mapModel.parseStepsToRemark(invalidJSON)
	if remark != "路径解析失败" {
		t.Errorf("无效JSON解析错误，期望 '路径解析失败'，得到 '%s'", remark)
	}

	// 测试有效的步行数据
	walkingSteps := []byte(`[
		{
			"mode": "WALKING",
			"distance": 500,
			"duration": 6,
			"direction": "北"
		}
	]`)
	remark = mapModel.parseStepsToRemark(walkingSteps)
	t.Logf("步行步骤解析结果: %s", remark)

	// 测试有效的公交数据
	transitSteps := []byte(`[
		{
			"mode": "TRANSIT",
			"lines": [
				{
					"vehicle": "SUBWAY",
					"title": "地铁1号线",
					"distance": 2000,
					"duration": 15,
					"price": 300,
					"start_time": "05:00",
					"end_time": "23:00",
					"geton": {
						"title": "天安门东"
					},
					"getoff": {
						"title": "天安门西"
					}
				}
			]
		}
	]`)
	remark = mapModel.parseStepsToRemark(transitSteps)
	t.Logf("公交步骤解析结果: %s", remark)

	// 验证包含基本信息
	if len(remark) == 0 {
		t.Error("有效步骤解析结果为空")
	}
}

// TestParseStepsToRemark_RailTime 测试火车时间字段的正确处理
func TestParseStepsToRemark_RailTime(t *testing.T) {
	mapModel := NewTencentModel().(*TencentModel)

	// 测试火车数据 - 使用departure_time和arrive_time字段
	railSteps := []byte(`[
		{
			"mode": "TRANSIT",
			"lines": [
				{
					"vehicle": "RAIL",
					"title": "G21",
					"distance": 1063593,
					"duration": 258,
					"price": 662,
					"departure_time": "17:00",
					"arrive_time": "21:18",
					"geton": {
						"title": "北京南"
					},
					"getoff": {
						"title": "上海虹桥"
					}
				}
			]
		}
	]`)

	remark := mapModel.parseStepsToRemark(railSteps)
	t.Logf("火车步骤解析结果: %s", remark)

	// 验证火车时间正确显示
	if !strings.Contains(remark, "17:00~21:18") {
		t.Errorf("火车时间显示错误，期望包含 '17:00~21:18'，实际得到: %s", remark)
	}

	// 验证包含基本信息
	if !strings.Contains(remark, "火车-G21") {
		t.Errorf("火车信息显示错误，期望包含 '火车-G21'，实际得到: %s", remark)
	}

	if !strings.Contains(remark, "北京南->上海虹桥") {
		t.Errorf("火车站点信息显示错误，期望包含 '北京南->上海虹桥'，实际得到: %s", remark)
	}
}

// TestGetVehicleTypeName 测试交通工具类型名称转换
func TestGetVehicleTypeName(t *testing.T) {
	mapModel := NewTencentModel().(*TencentModel)

	tests := []struct {
		input    string
		expected string
	}{
		{"BUS", "公交"},
		{"SUBWAY", "地铁"},
		{"RAIL", "火车"},
		{"UNKNOWN", "交通工具"},
	}

	for _, test := range tests {
		result := mapModel.getVehicleTypeName(test.input)
		if result != test.expected {
			t.Errorf("getVehicleTypeName(%s) = %s, 期望 %s", test.input, result, test.expected)
		}
	}
}

// TestDirectionTransitRawResponse 测试腾讯地图API原始响应
func TestDirectionTransitRawResponse(t *testing.T) {
	mapModel := NewTencentModel()

	// 定义测试用的地点
	from := &model.Location{
		Lat:              39.904202,
		Lng:              116.407394,
		FormattedAddress: "北京市东城区天安门广场",
	}

	to := &model.Location{
		Lat:              39.913869,
		Lng:              116.391248,
		FormattedAddress: "北京市西城区西直门",
	}

	// 设置明天早上7点作为出发时间
	tomorrow := time.Now().Add(time.Hour * 24)
	departureTime := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 7, 0, 0, 0, tomorrow.Location())

	// 调用原始DirectionTransit获取完整响应
	routes, rawBody, err := mapModel.DirectionTransit(from, to, "RECOMMEND", departureTime)

	if err != nil {
		t.Logf("DirectionTransit 返回错误: %v", err)
		return
	}

	if len(routes) == 0 {
		t.Log("没有找到路线")
		return
	}

	t.Logf("找到 %d 条路线", len(routes))

	// 输出原始响应以便分析
	t.Logf("原始API响应:\n%s", rawBody)
}

// TestDirectionTransitV2_WithRail 测试包含火车的跨城市路线
func TestDirectionTransitV2_WithRail(t *testing.T) {
	mapModel := NewTencentModel()

	// 定义跨城市的测试地点 - 北京到上海
	from := &model.Location{
		Lat:              39.904202,
		Lng:              116.407394,
		FormattedAddress: "北京市东城区天安门广场",
	}

	to := &model.Location{
		Lat:              31.230391,
		Lng:              121.473701,
		FormattedAddress: "上海市黄浦区人民广场",
	}

	// 设置明天早上7点作为出发时间
	tomorrow := time.Now().Add(time.Hour * 24)
	departureTime := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 7, 0, 0, 0, tomorrow.Location())

	// 调用DirectionTransitV2接口
	routes, remark, err := mapModel.DirectionTransitV2(from, to, "RECOMMEND", departureTime)

	// 验证结果
	if err != nil {
		t.Logf("DirectionTransitV2 返回错误: %v", err)
		// 在某些情况下，API可能返回错误，这是正常的
		return
	}

	if len(routes) == 0 {
		t.Log("没有找到路线，但这可能是正常的")
		return
	}

	// 输出结果用于手动验证
	t.Logf("找到 %d 条路线", len(routes))
	for i, route := range routes {
		t.Logf("路线 %d: 距离=%d米, 时间=%d分钟, 价格=%d", i+1, route.Distance, route.Duration, route.Price)
	}

	// 验证备注信息
	if remark == "" {
		t.Log("备注信息为空")
	} else {
		t.Logf("备注信息: %s", remark)

		// 检查是否包含火车时间信息（如果有火车路线的话）
		if strings.Contains(remark, "火车-") {
			t.Log("发现火车路线，检查时间格式...")
			// 检查火车时间格式是否正确（应该是 HH:MM~HH:MM 格式，而不是 ~）
			if !strings.Contains(remark, "~") || strings.Contains(remark, "|~") {
				t.Errorf("火车时间格式可能不正确，备注: %s", remark)
			} else {
				t.Log("火车时间格式验证通过")
			}
		}
	}
}
