package model

import "fmt"

type Route struct {
	Distance int    `json:"distance"`
	Duration int    `json:"duration"`
	Price    int    `json:"price"`
	Steps    []Step `json:"steps"`
}
type Step struct {
	Mode     string `json:"mode"`
	Distance int    `json:"distance"`
	Duration int    `json:"duration"`
}

func (r Route) Available() bool {
	return r.Distance > 0
}

type Location struct {
	Lat              float64 `json:"lat"`
	Lng              float64 `json:"lng"`
	FormattedAddress string  `json:"formatted_address"`
}

func (l Location) String() string {
	return fmt.Sprintf("%.7f,%.7f", l.Lat, l.Lng)
}

type AddressComponents struct {
	Province     string `json:"province"`
	City         string `json:"city"`
	District     string `json:"district"`
	Street       string `json:"street"`
	StreetNumber string `json:"street_number"`
}
