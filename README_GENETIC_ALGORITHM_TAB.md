# 遗传算法调度 Tab 使用说明

## 功能概述

我为你的 web 应用添加了第5个 tab：**遗传算法调度**，它提供了基于遗传算法的律师开庭调度功能。

## 主要功能

### 1. 控制面板（第一行）

#### 权重配置下拉框
- **位置**：第一行左侧
- **功能**：选择不同的权重配置来优化调度策略
- **可选配置**：
  - 距离优先 (距离权重: 10.0, 时间权重: 1.0, 均衡权重: 0.1)
  - 时间优先 (距离权重: 1.0, 时间权重: 10.0, 均衡权重: 0.1)
  - 负载均衡 (距离权重: 2.0, 时间权重: 2.0, 均衡权重: 10.0)
  - 均衡优化 (距离权重: 5.0, 时间权重: 5.0, 均衡权重: 1.0)
  - 远距离惩罚 (距离权重: 20.0, 时间权重: 2.0, 均衡权重: 0.5)
  - 快速到达 (距离权重: 2.0, 时间权重: 15.0, 均衡权重: 0.5)
  - 工作量平衡 (距离权重: 3.0, 时间权重: 3.0, 均衡权重: 8.0)
  - 距离敏感 (距离权重: 15.0, 时间权重: 3.0, 均衡权重: 1.0)
  - 时间敏感 (距离权重: 3.0, 时间权重: 12.0, 均衡权重: 1.0)
  - 综合平衡 (距离权重: 6.0, 时间权重: 6.0, 均衡权重: 3.0)

#### 统计信息显示
- **总费用**：显示当前调度方案的总费用
- **总行程**：显示律师出差的总行程（单位：公里）
- **总耗时**：显示律师出差的总耗时（单位：分钟）

#### 操作按钮
- **地图显示**：点击后新开窗口显示中国地图和出差线路
- **重新计算**：运行遗传算法重新计算调度方案

### 2. 时间表格（第二行）

#### 表格结构
- **行**：显示有匹配的律师，格式为 `律师名(开庭数)`
- **列**：显示日期（最多显示7天）
- **交汇处**：显示该律师在该日期的开庭安排

#### 开庭标注
每个开庭标注分为三层显示：
1. **去程时间**：律师出发前往法院的时间
2. **开庭信息**：地址 + 开庭开始结束时间
3. **返程时间**：律师从法院返回的时间

#### 交互功能
- **鼠标悬停**：可以看到详细信息，包括法院名称、案件ID、距离、耗时等
- **日历样式**：美观的卡片式显示，支持多个开庭按时间排序

### 3. 地图显示功能

#### 地图窗口
- **新窗口**：点击"地图显示"按钮后在新窗口打开
- **中国地图**：显示简化的中国地图背景
- **出差线路**：显示律师到法院的出差路线

#### 地图元素
- **律师位置**：绿色圆点标记
- **法院位置**：橙色圆点标记
- **出差线路**：渐变色线条连接律师和法院
- **动画效果**：线条绘制动画，提升视觉效果

#### 交互功能
- **鼠标悬停**：显示起终点名称（人名/法院名）+ 时间
- **详细信息**：显示案件ID、开庭时间、距离、耗时等
- **图例**：显示不同颜色的含义

## 使用流程

1. **启动应用**：`go run main.go sched web`
2. **打开浏览器**：访问 `http://localhost:8080`
3. **切换到遗传算法调度 Tab**：点击第5个tab "遗传算法调度"
4. **选择权重配置**：从下拉框中选择合适的权重配置
5. **运行计算**：点击"重新计算"按钮开始遗传算法调度
6. **查看结果**：
   - 在控制面板查看总费用、总行程、总耗时
   - 在时间表格查看详细的律师开庭安排
   - 点击"地图显示"查看可视化的出差线路

## 技术特性

### 后端实现
- **API接口**：
  - `GET /api/genetic/configs` - 获取权重配置选项
  - `GET /api/genetic/schedule` - 获取调度结果
  - `POST /api/genetic/schedule` - 运行遗传算法调度
  - `GET /genetic/map` - 地图显示页面

### 前端实现
- **响应式设计**：支持各种屏幕尺寸
- **动态交互**：实时更新统计信息和表格
- **美观界面**：现代化的UI设计
- **可视化**：地图显示功能提供直观的线路展示

### 数据处理
- **时间冲突检测**：确保律师不会在同一时间出现在不同地点
- **路径优化**：基于距离和时间的综合优化
- **负载均衡**：合理分配律师的工作量
- **实时计算**：支持动态调整权重配置

## 注意事项

1. **数据要求**：
   - 需要有律师数据（状态为启用）
   - 需要有法院数据（状态为启用）
   - 需要有开庭数据（状态为待排庭、待滴滴或待开庭）
   - 需要有路线数据（状态为可用）

2. **计算时间**：
   - 遗传算法计算可能需要一定时间
   - 计算期间按钮会显示"计算中..."状态

3. **浏览器兼容性**：
   - 推荐使用现代浏览器（Chrome、Firefox、Safari、Edge）
   - 需要支持ES6语法和CSS Grid布局

4. **地图显示**：
   - 地图使用简化的坐标转换，仅供演示
   - 实际部署时可集成真实的地图服务

## 扩展功能

如需进一步扩展功能，可以考虑：

1. **更多权重配置**：允许用户自定义权重参数
2. **实时地图**：集成高德地图或百度地图API
3. **导出功能**：支持导出调度结果为Excel或PDF
4. **历史记录**：保存和回顾之前的调度方案
5. **更多算法**：支持其他调度算法（如模拟退火、禁忌搜索等）

## 故障排除

1. **无法加载配置**：检查网络连接和后端服务状态
2. **计算失败**：检查数据完整性和格式正确性
3. **地图显示异常**：检查浏览器是否支持弹出窗口
4. **样式显示异常**：检查CSS文件是否正确加载

---

**开发者**：Claude Assistant  
**版本**：1.0.0  
**最后更新**：2024年 