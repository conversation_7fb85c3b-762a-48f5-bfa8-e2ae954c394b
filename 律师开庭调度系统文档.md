# 律师开庭调度系统文档

## 系统概述

这是一个基于遗传算法的律师开庭调度系统，用于自动化分配律师到开庭案件，优化出行成本、时间安排和工作负载平衡。系统通过智能算法解决律师资源调度的复杂约束优化问题。

## 核心功能

### 1. 智能调度算法
- **遗传算法优化**：使用遗传算法求解律师-案件匹配的多目标优化问题
- **多策略配置**：支持距离优先、时间优先、负载均衡等多种调度策略
- **约束处理**：自动处理时间冲突、容量限制等约束条件
- **实时验证**：提供完整的方案验证和冲突检测机制

### 2. 地理信息集成
- **多地图服务支持**：集成腾讯地图API进行路径规划和距离计算
- **智能路径优化**：考虑实际出行时间、距离和成本
- **地址标准化**：自动解析和标准化律师、法院地址信息

### 3. 数据管理
- **完整的实体模型**：律师、法院、案件、路径等核心实体
- **状态管理**：支持案件状态跟踪和律师可用性管理
- **数据完整性验证**：自动检查数据一致性和完整性

## 系统架构

### 核心模块结构

```
internal/service/schedule/
├── genetic_algorithm.go      # 主调度器和核心算法
├── individual.go            # 遗传算法个体管理
├── fitness.go              # 适应度评估和方案评分
├── constraints.go          # 约束检查和冲突处理
├── config.go              # 配置管理和参数验证
├── scheduler_interface.go  # 接口定义
├── scheduler_impl.go      # 接口实现
└── ARCHITECTURE.md        # 详细架构文档
```

### 数据模型

#### 核心实体
- **Lawyer（律师）**：包含基本信息、地理位置、能力类型等
- **Court（法院）**：法院信息和地理位置
- **Trial（开庭）**：案件开庭信息，包含时间、地点等
- **Routes（路径）**：预计算的出行路径，包含距离、时间、成本

#### 关键数据结构
```go
type GeneticAlgorithmScheduler struct {
    // 数据映射 - O(1)查找
    lawyerMap map[uint]*model.Lawyer
    courtMap  map[uint]*model.Court
    trialMap  map[uint]*model.Trial
    routeMap  map[string]*model.Route
    
    // 优化数据结构
    conflicts map[string]bool    // 冲突映射
    currentCosts [][]float64     // 费用矩阵
    
    // 算法参数
    populationSize int           // 种群大小: 200
    generations    int           // 进化代数: 100
    mutationRate   float64       // 变异率: 0.1
    crossoverRate  float64       // 交叉率: 0.7
}
```

## 依赖关系

### 外部依赖
- **数据库**：GORM + MySQL，存储律师、法院、案件、路径数据
- **地图服务**：腾讯地图API，提供地理编码和路径规划
- **Web框架**：Gin，提供REST API接口
- **配置管理**：Viper，管理应用配置

### 内部依赖
- `internal/model`：数据模型定义
- `internal/config`：数据库配置和连接管理
- `internal/service`：业务服务层

## 系统入口

### 1. 命令行入口
```bash
# 启动Web服务
./external-tool sched web

# 初始化地址数据
./external-tool sched init-address

# 初始化路径数据
./external-tool sched init-route
```

### 2. Web API入口
- **主页**：`GET /`
- **调度页面**：`GET /genetic/map`
- **运行调度**：`POST /api/genetic/schedule`
- **获取结果**：`GET /api/genetic/schedule`

### 3. 程序入口
```go
// 创建调度器
mapModel := imap.NewMapModel("tencent")
scheduler := schedule.NewGeneticAlgorithmScheduler(mapModel, lawyers, courts, trials, routes)

// 执行调度
result := scheduler.Schedule()

// 自定义权重调度
configs := []schedule.GAWeightConfig{
    {"距离优先", 10.0, 1.0, 1.0, 0.1},
    {"时间优先", 1.0, 1.0, 10.0, 0.1},
}
result := scheduler.ScheduleWithCustomWeights(configs)
```

## 使用方法

### 1. 基础使用流程

#### 步骤1：数据准备
1. 导入律师信息（姓名、地址、能力等）
2. 导入法院信息（名称、地址等）
3. 导入开庭案件信息（时间、法院等）
4. 初始化路径数据（律师到法院的出行信息）

#### 步骤2：配置调度参数
```go
// 设置基本参数
scheduler.SetGAParameters(200, 100, 0.1, 0.7) // 种群大小、代数、变异率、交叉率
scheduler.SetMaxCapacityPerLawyer(5)           // 每个律师最大案件数

// 设置权重配置
configs := []GAWeightConfig{
    {"均衡策略", 5.0, 5.0, 5.0, 1.0},
}
```

#### 步骤3：执行调度
```go
// 使用默认配置
result := scheduler.Schedule()

// 使用自定义配置
result := scheduler.ScheduleWithCustomWeights(configs)

// 带性能指标的调度
result, metrics, err := scheduler.ScheduleWithMetrics()
```

### 2. Web界面使用

1. 启动服务：`./external-tool sched web`
2. 访问：`http://localhost:8080`
3. 进入调度页面：`/genetic/map`
4. 配置调度参数并运行
5. 查看调度结果和可视化展示

### 3. 调度策略配置

系统提供多种预设调度策略：

- **距离优先**：最小化出行距离
- **时间优先**：最小化出行时间
- **负载均衡**：平衡律师工作量
- **综合平衡**：多目标综合优化

每种策略通过权重配置实现：
```go
type GAWeightConfig struct {
    Name          string  // 策略名称
    DistWeight    float64 // 距离权重
    CostWeight    float64 // 成本权重  
    TimeWeight    float64 // 时间权重
    BalanceWeight float64 // 负载均衡权重
}
```

## 算法特性

### 1. 遗传算法核心
- **个体编码**：每个个体代表一个完整的分配方案
- **适应度函数**：综合考虑成本、时间、距离和负载均衡
- **选择策略**：轮盘赌选择
- **交叉操作**：单点交叉
- **变异操作**：随机重分配

### 2. 约束处理
- **时间冲突检测**：确保律师不会同时处理多个案件
- **容量限制**：控制每个律师的最大案件数量
- **地理约束**：考虑实际出行时间和距离

### 3. 性能优化
- **内存优化**：使用哈希映射替代三维数组存储冲突信息
- **预计算**：提前计算费用矩阵和冲突矩阵
- **并发支持**：为未来并发处理预留接口

## 输出结果

调度结果按匹配数量分组返回：
```go
// 结果格式：map[匹配数量][]方案
result := map[uint][]map[uint]uint{
    10: []map[uint]uint{  // 匹配10个案件的方案
        {1: 101, 2: 102, ...}, // 案件ID -> 律师ID
    },
    9: []map[uint]uint{   // 匹配9个案件的方案
        {1: 103, 3: 104, ...},
    },
}
```

每个方案包含详细信息：
- 匹配的案件-律师对应关系
- 总成本、总距离、总时间
- 律师工作负载分布
- 方案质量评分

## 新策略评估：跨城市连续开庭优化

### 策略需求分析

您提出的策略是：**如果律师离开自己城市去参加开庭，且该地有若干开庭离得比较近，则尽量安排一个律师连续处理**。

这是一个很有价值的优化策略，可以显著降低跨城市出行成本。

### 当前系统支持情况

#### 已有的相关功能
1. **地理位置感知**：系统已经具备律师和法院的精确地理位置信息
2. **时间约束处理**：现有的时间冲突检测机制可以确保连续安排的可行性
3. **成本计算**：已有完整的出行成本计算体系
4. **多目标优化**：遗传算法框架支持多种优化目标

#### 需要增强的部分
1. **城市识别逻辑**：需要判断律师是否离开了自己的城市
2. **地理聚类算法**：识别同一地区的相近开庭案件
3. **连续调度奖励**：在适应度函数中增加连续处理的奖励机制
4. **时间窗口优化**：考虑连续案件之间的时间间隔

### 修改范围评估

#### 1. 核心算法修改（中等复杂度）

**需要修改的文件：**
- `fitness.go`：增加跨城市连续处理的奖励计算
- `constraints.go`：增加地理聚类和连续性约束
- `genetic_algorithm.go`：增加城市识别和聚类逻辑

**预估工作量：** 2-3天

#### 2. 数据结构扩展（低复杂度）

**需要修改的文件：**
- `config.go`：增加新的权重配置参数
- `scheduler_interface.go`：扩展配置接口

**预估工作量：** 0.5天

#### 3. 算法逻辑增强（高复杂度）

**新增功能模块：**
```go
// 城市识别和聚类
type GeographicCluster struct {
    City        string           // 城市名称
    Trials      []uint          // 该城市的案件列表
    Center      *model.Location // 地理中心点
    Radius      float64         // 聚类半径
}

// 连续调度策略
type ContinuousSchedulingStrategy struct {
    CrossCityBonus    float64 // 跨城市连续处理奖励
    ClusterRadius     float64 // 聚类半径（公里）
    MinClusterSize    int     // 最小聚类大小
    MaxTravelTime     int     // 最大城际出行时间（分钟）
}
```

**预估工作量：** 3-4天

#### 4. 测试和验证（中等复杂度）

**需要增加的测试：**
- 城市识别准确性测试
- 地理聚类算法测试
- 连续调度效果验证
- 性能影响评估

**预估工作量：** 1-2天

### 技术实现方案

#### 1. 城市识别逻辑
```go
// 在 genetic_algorithm.go 中添加
func (s *GeneticAlgorithmScheduler) isLawyerOutOfCity(lawyerID uint, courtID uint) bool {
    lawyer := s.lawyerMap[lawyerID]
    court := s.courtMap[courtID]

    if lawyer == nil || court == nil {
        return false
    }

    // 基于城市字段判断
    return lawyer.City != court.City
}

// 计算两点间距离
func (s *GeneticAlgorithmScheduler) calculateDistance(loc1, loc2 *model.Location) float64 {
    // 使用Haversine公式计算地理距离
    // 或者直接使用已有的路径数据
}
```

#### 2. 地理聚类算法
```go
// 在 constraints.go 中添加
type GeographicCluster struct {
    City        string
    Trials      []uint
    Center      *model.Location
    Radius      float64
    LawyerID    uint  // 分配给该聚类的律师
}

func (s *GeneticAlgorithmScheduler) clusterTrialsByLocation(trials []uint, maxRadius float64) []GeographicCluster {
    clusters := make([]GeographicCluster, 0)

    for _, trialID := range trials {
        trial := s.trialMap[trialID]
        court := s.courtMap[trial.CourtID]

        // 寻找最近的聚类或创建新聚类
        assigned := false
        for i := range clusters {
            if s.calculateDistance(clusters[i].Center, court.GetLocation()) <= maxRadius {
                clusters[i].Trials = append(clusters[i].Trials, trialID)
                assigned = true
                break
            }
        }

        if !assigned {
            // 创建新聚类
            clusters = append(clusters, GeographicCluster{
                City:   court.City,
                Trials: []uint{trialID},
                Center: court.GetLocation(),
                Radius: maxRadius,
            })
        }
    }

    return clusters
}
```

#### 3. 适应度函数增强
```go
// 在 fitness.go 中修改 evaluateIndividual 方法
func (s *GeneticAlgorithmScheduler) evaluateIndividual(individual *Individual) {
    // ... 现有逻辑 ...

    // 新增：跨城市连续处理奖励
    crossCityBonus := s.calculateCrossCityBonus(individual)

    // 更新适应度计算
    individual.Fitness = -float64(matching)*matchingBonus + totalCost - crossCityBonus
}

func (s *GeneticAlgorithmScheduler) calculateCrossCityBonus(individual *Individual) float64 {
    bonus := 0.0

    // 按律师分组案件
    lawyerTrials := make(map[uint][]uint)
    for t, l := range individual.Genes {
        if l >= 0 && l < len(s.lawyersList) {
            lawyerID := s.lawyersList[l]
            trialID := s.trialsList[t]
            lawyerTrials[lawyerID] = append(lawyerTrials[lawyerID], trialID)
        }
    }

    // 计算每个律师的跨城市连续处理奖励
    for lawyerID, trials := range lawyerTrials {
        if len(trials) < 2 {
            continue
        }

        // 识别跨城市案件
        crossCityTrials := make([]uint, 0)
        for _, trialID := range trials {
            trial := s.trialMap[trialID]
            if s.isLawyerOutOfCity(lawyerID, trial.CourtID) {
                crossCityTrials = append(crossCityTrials, trialID)
            }
        }

        if len(crossCityTrials) < 2 {
            continue
        }

        // 对跨城市案件进行地理聚类
        clusters := s.clusterTrialsByLocation(crossCityTrials, 50.0) // 50公里聚类半径

        // 计算聚类奖励
        for _, cluster := range clusters {
            if len(cluster.Trials) >= 2 {
                // 验证时间可行性
                if s.isTimeSequenceFeasible(lawyerID, cluster.Trials) {
                    // 给予奖励：聚类大小 * 基础奖励
                    bonus += float64(len(cluster.Trials)) * s.crossCityBonusWeight
                }
            }
        }
    }

    return bonus
}

func (s *GeneticAlgorithmScheduler) isTimeSequenceFeasible(lawyerID uint, trials []uint) bool {
    // 按时间排序案件
    sort.Slice(trials, func(i, j int) bool {
        return s.trialMap[trials[i]].StartTime.Before(s.trialMap[trials[j]].StartTime)
    })

    // 检查相邻案件是否有足够的时间间隔
    for i := 0; i < len(trials)-1; i++ {
        trial1 := s.trialMap[trials[i]]
        trial2 := s.trialMap[trials[i+1]]

        // 计算案件间的最小间隔时间
        court1 := s.courtMap[trial1.CourtID]
        court2 := s.courtMap[trial2.CourtID]

        routeKey := s.getRouteKey(court1.GetLocation(), court2.GetLocation())
        route, exists := s.routeMap[routeKey]
        if !exists {
            return false
        }

        // 检查时间间隔是否足够
        minInterval := time.Duration(route.Duration) * time.Minute * time.Duration(Buffer)
        actualInterval := trial2.StartTime.Sub(trial1.EndTime)

        if actualInterval < minInterval {
            return false
        }
    }

    return true
}
```

#### 4. 配置参数扩展
```go
// 在 config.go 中添加
type CrossCityConfig struct {
    Enable           bool    // 是否启用跨城市优化
    BonusWeight      float64 // 连续处理奖励权重
    ClusterRadius    float64 // 地理聚类半径（公里）
    MinClusterSize   int     // 最小聚类大小
    MaxTravelTime    int     // 最大城际出行时间（分钟）
}

// 在 GeneticAlgorithmScheduler 中添加字段
type GeneticAlgorithmScheduler struct {
    // ... 现有字段 ...
    crossCityBonusWeight float64
    clusterRadius        float64
    minClusterSize       int
}
```

### 实现建议

#### 阶段1：基础功能实现（3天）
1. 实现城市识别和距离计算方法
2. 开发地理聚类算法
3. 在适应度函数中集成跨城市奖励机制

#### 阶段2：策略优化（2天）
1. 调整权重配置，平衡各种优化目标
2. 优化聚类参数和时间约束检查
3. 增加配置接口，支持参数调优

#### 阶段3：测试验证（2天）
1. 编写单元测试验证各个组件
2. 使用真实数据测试整体效果
3. 性能测试和参数调优

### 潜在挑战与解决方案

#### 1. 算法复杂度增加
**挑战**：地理聚类和连续性检查会增加计算复杂度
**解决方案**：
- 预计算城市间距离矩阵
- 使用空间索引优化聚类算法
- 并行处理不同律师的聚类计算

#### 2. 参数调优复杂性
**挑战**：新增多个参数需要仔细调优
**解决方案**：
- 提供默认参数配置
- 实现参数敏感性分析工具
- 支持A/B测试对比不同配置效果

#### 3. 边界情况处理
**挑战**：跨城市边界、时间紧张等特殊情况
**解决方案**：
- 增加边界条件检查
- 实现降级策略（回退到原有算法）
- 详细的日志记录和异常处理

#### 4. 数据质量依赖
**挑战**：依赖准确的地理位置和时间信息
**解决方案**：
- 增强数据验证机制
- 提供数据质量报告
- 支持手动数据修正

### 性能影响评估

#### 时间复杂度分析
- **原算法**：O(L × T²) （L=律师数，T=案件数）
- **新算法**：O(L × T² + L × C × log(C)) （C=聚类数）
- **预期影响**：增加10-20%的计算时间

#### 内存使用分析
- **额外内存**：城市映射表、聚类数据结构
- **预期增加**：5-10%的内存使用

#### 优化建议
1. 使用缓存减少重复计算
2. 实现增量聚类更新
3. 考虑使用近似算法处理大规模数据

### 测试策略

#### 单元测试
```go
func TestCityIdentification(t *testing.T) {
    // 测试城市识别准确性
}

func TestGeographicClustering(t *testing.T) {
    // 测试地理聚类算法
}

func TestCrossCityBonus(t *testing.T) {
    // 测试跨城市奖励计算
}

func TestTimeSequenceFeasibility(t *testing.T) {
    // 测试时间序列可行性检查
}
```

#### 集成测试
- 完整调度流程测试
- 多种数据规模测试
- 边界条件测试

#### 性能测试
- 执行时间对比测试
- 内存使用监控
- 大数据集压力测试

### 监控和度量

#### 关键指标
1. **成本节约率**：跨城市出行成本降低百分比
2. **聚类效率**：成功聚类的案件比例
3. **时间可行性**：连续安排的成功率
4. **算法性能**：执行时间和内存使用

#### 监控实现
```go
type CrossCityMetrics struct {
    TotalCrossCityTrials    int     // 总跨城市案件数
    ClusteredTrials         int     // 成功聚类的案件数
    CostSavings            float64  // 成本节约金额
    AverageClusterSize     float64  // 平均聚类大小
    TimeConstraintViolations int    // 时间约束违反次数
}
```

### 部署建议

#### 渐进式部署
1. **阶段1**：在测试环境验证功能
2. **阶段2**：小规模生产环境试点
3. **阶段3**：全面部署并监控效果

#### 配置管理
```go
// 支持运行时配置调整
type RuntimeConfig struct {
    CrossCityOptimization bool    `json:"cross_city_optimization"`
    BonusWeight          float64 `json:"bonus_weight"`
    ClusterRadius        float64 `json:"cluster_radius"`
    MinClusterSize       int     `json:"min_cluster_size"`
}
```

#### 回滚策略
- 保留原有算法作为备选
- 支持快速切换配置
- 详细的变更日志记录

### 总结

#### 技术可行性
✅ **高度可行**：现有架构完全支持新策略实现
✅ **风险可控**：主要修改集中在算法层面，不影响系统稳定性
✅ **扩展性好**：模块化设计便于后续功能扩展

#### 业务价值
🎯 **成本优化**：显著降低跨城市出行成本
🎯 **效率提升**：减少律师无效出行时间
🎯 **资源利用**：提高律师资源配置效率

#### 实施建议
**总工作量**：7-10个工作日
**技术风险**：中等（主要在参数调优）
**业务收益**：高（预期节约20-30%跨城市出行成本）

**推荐实施路径**：
1. 先实现基础功能原型
2. 小规模数据验证效果
3. 参数调优和性能优化
4. 渐进式生产部署

这个策略将为律师调度系统带来显著的成本优化效果，建议优先实施。
