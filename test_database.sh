#!/bin/bash

# 数据库测试脚本
echo "开始测试数据库功能..."

# 编译程序
echo "1. 编译程序..."
go build -o external-tool main.go
if [ $? -ne 0 ]; then
    echo "编译失败"
    exit 1
fi

echo "2. 测试程序帮助信息..."
./external-tool --help

echo "3. 测试数据库命令..."
./external-tool database --help

echo "4. 测试查询命令..."
./external-tool query --help

echo "5. 如果MySQL已经运行，可以尝试以下命令："
echo "   ./external-tool database init"
echo "   ./external-tool query tables"

echo "测试完成！" 