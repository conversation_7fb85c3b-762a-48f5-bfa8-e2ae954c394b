package cmd

import (
	"fmt"

	"github.com/spf13/cobra"
)

var Version = "1.0.0"

// versionCmd represents the version command
var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "显示版本信息",
	Long:  `显示 external-tool 的版本信息`,
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Printf("external-tool v%s\n", Version)
		fmt.Println("一个基于 Go 的轻量级地图服务命令行工具")
		fmt.Println("支持高德、百度、腾讯地图 API")
	},
}

func init() {
	rootCmd.AddCommand(versionCmd)
}
