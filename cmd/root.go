package cmd

import (
	"external-tool/internal/config"
	"fmt"
	"log"
	"os"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

var cfgFile string

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "external-tool",
	Short: "地图服务命令行工具",
	Long: `基于高德、百度、腾讯地图 API 的命令行工具
支持地理编码、距离计算、路径规划等功能`,
	PersistentPreRun: func(cmd *cobra.Command, args []string) {
		// 在所有命令执行前初始化数据库
		if err := config.InitDatabase(); err != nil {
			log.Fatalf("数据库初始化失败: %v", err)
		}
	},
}

// Execute adds all child commands to the root command and sets flags appropriately.
func Execute() error {
	defer func() {
		// 程序退出时关闭数据库连接
		if err := config.CloseDatabase(); err != nil {
			log.Printf("关闭数据库连接失败: %v", err)
		}
	}()
	return rootCmd.Execute()
}

func init() {
	cobra.OnInitialize(initConfig)

	// 全局配置文件标志
	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "config file (default is $HOME/.counsel-docket.yaml)")

	// 全局提供商标志
	rootCmd.PersistentFlags().String("provider", "amap", "地图服务提供商 (amap, baidu, tencent)")
	viper.BindPFlag("provider", rootCmd.PersistentFlags().Lookup("provider"))
}

// initConfig reads in config file and ENV variables.
func initConfig() {
	if cfgFile != "" {
		// Use config file from the flag.
		viper.SetConfigFile(cfgFile)
	} else {
		// Find home directory.
		home, err := os.UserHomeDir()
		cobra.CheckErr(err)

		// Search config in home directory with name ".external-tool" (without extension).
		viper.AddConfigPath(home)
		viper.AddConfigPath(".")
		viper.SetConfigType("yaml")
		viper.SetConfigName(".external-tool")
	}

	viper.AutomaticEnv() // read in environment variables that match

	// If a config file is found, read it in.
	if err := viper.ReadInConfig(); err == nil {
		fmt.Fprintln(os.Stderr, "Using config file:", viper.ConfigFileUsed())
	}
}
