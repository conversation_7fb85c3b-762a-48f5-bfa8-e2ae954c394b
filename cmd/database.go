package cmd

import (
	"fmt"
	"log"

	"external-tool/internal/config"
	"external-tool/internal/model"

	"github.com/spf13/cobra"
)

var databaseCmd = &cobra.Command{
	Use:   "database",
	Short: "数据库管理相关命令",
	Long:  `数据库管理相关命令，包括初始化表结构等功能`,
	PersistentPreRun: func(cmd *cobra.Command, args []string) {
		// 数据库命令不需要在父级PreRun中初始化数据库
		// 因为可能是第一次创建数据库
	},
}

var initDbCmd = &cobra.Command{
	Use:   "init",
	Short: "初始化数据库表结构",
	Long:  `连接数据库并创建所有必要的表结构`,
	Run: func(cmd *cobra.Command, args []string) {
		// 初始化数据库连接
		if err := config.InitDatabase(); err != nil {
			log.Fatalf("数据库连接失败: %v", err)
		}

		// 从单例获取数据库连接
		db := config.GetDB()

		// 初始化数据库表结构
		err := model.InitDatabase(db)
		if err != nil {
			log.Fatalf("数据库初始化失败: %v", err)
		}

		fmt.Println("数据库表结构初始化成功!")
		dbConfig := config.DefaultDatabaseConfig()
		fmt.Printf("数据库连接信息: %s\n", dbConfig.DSN())
	},
}

var migrateCmd = &cobra.Command{
	Use:   "migrate",
	Short: "数据库迁移",
	Long:  `执行数据库表结构迁移`,
	Run: func(cmd *cobra.Command, args []string) {
		// 从单例获取数据库连接
		db := config.GetDB()

		// 执行迁移
		err := model.InitDatabase(db)
		if err != nil {
			log.Fatalf("数据库迁移失败: %v", err)
		}

		fmt.Println("数据库迁移完成!")
	},
}

func init() {
	// 添加子命令
	databaseCmd.AddCommand(initDbCmd)
	databaseCmd.AddCommand(migrateCmd)

	// 添加到根命令
	rootCmd.AddCommand(databaseCmd)
}
