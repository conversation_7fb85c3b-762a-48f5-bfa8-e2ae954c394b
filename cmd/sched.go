package cmd

import (
	"external-tool/internal/service"

	"github.com/spf13/cobra"
)

var schedCmd = &cobra.Command{
	Use:   "sched",
	Short: "排庭",
	Long:  `根据律师时间/开庭信息进行编排`,
}

var initAddressCmd = &cobra.Command{
	Use:   "init-address",
	Short: "初始化地址",
	Long:  `初始化地址`,
	Run: func(cmd *cobra.Command, args []string) {
		mapService := service.NewMapService("tencent")
		mapService.InitAddress()
	},
}

var initRouteCmd = &cobra.Command{
	Use:   "init-route",
	Short: "初始化路线",
	Long:  `初始化路线（时间感知版本）`,
	Run: func(cmd *cobra.Command, args []string) {
		mapService := service.NewMapService("tencent")
		mapService.InitRouteV2()
	},
}
var trialSchedCmd = &cobra.Command{
	Use:   "trial",
	Short: "排庭",
	Long:  `根据律师时间/开庭信息进行编排`,
	Run: func(cmd *cobra.Command, args []string) {
		trialService := service.NewTrialService()
		trialService.Schedule()
	},
}

var webCmd = &cobra.Command{
	Use:   "web",
	Short: "Web界面",
	Long:  `启动Web界面进行数据管理`,
	Run: func(cmd *cobra.Command, args []string) {
		webService := service.NewWebService()
		webService.Start()
	},
}

func init() {
	// 添加子命令
	schedCmd.AddCommand(initAddressCmd)
	schedCmd.AddCommand(initRouteCmd)
	schedCmd.AddCommand(trialSchedCmd)
	schedCmd.AddCommand(webCmd)

	// 添加到根命令
	rootCmd.AddCommand(schedCmd)
}
