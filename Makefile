.PHONY: build clean test install help

BINARY_NAME=external-tool
VERSION?=1.0.0
LDFLAGS=-ldflags "-X main.Version=${VERSION}"

# 默认目标
all: build

# 构建
build:
	go build ${LDFLAGS} -o ${BINARY_NAME}

# 清理
clean:
	go clean
	rm -f ${BINARY_NAME}
	rm -f ${BINARY_NAME}-*

# 测试
test:
	go test -v ./...

# 安装到 GOPATH/bin
install:
	go install ${LDFLAGS}

# 交叉编译
build-all: build-linux build-windows build-darwin

build-linux:
	GOOS=linux GOARCH=amd64 go build ${LDFLAGS} -o ${BINARY_NAME}-linux-amd64

build-windows:
	GOOS=windows GOARCH=amd64 go build ${LDFLAGS} -o ${BINARY_NAME}-windows-amd64.exe

build-darwin:
	GOOS=darwin GOARCH=amd64 go build ${LDFLAGS} -o ${BINARY_NAME}-darwin-amd64

# 格式化代码
fmt:
	go fmt ./...

# 代码检查
lint:
	golangci-lint run

# 初始化 Go 模块
init:
	go mod init external-tool
	go mod tidy

# 帮助
help:
	@echo "可用的 make 目标:"
	@echo "  build       - 构建二进制文件"
	@echo "  clean       - 清理构建文件"
	@echo "  test        - 运行测试"
	@echo "  install     - 安装到 GOPATH/bin"
	@echo "  build-all   - 交叉编译所有平台"
	@echo "  fmt         - 格式化代码"
	@echo "  lint        - 代码检查"
	@echo "  init        - 初始化 Go 模块"
	@echo "  help        - 显示此帮助信息" 