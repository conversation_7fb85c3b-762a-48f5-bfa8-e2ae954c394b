# 枚举所有最大匹配功能使用说明

## 功能概述

`MaxMatchingScheduler` 现在支持枚举所有可能的最大匹配方案，而不是仅返回一个最优解。这个功能让用户可以：

1. **查看所有最优方案**: 找到所有达到最大匹配数的不同调度方案
2. **方案对比分析**: 比较不同方案的总距离、总时间、负载均衡等指标
3. **灵活选择**: 根据实际需求选择最合适的方案

## 核心方法

### 1. 启用/禁用枚举功能

```go
// 启用枚举所有最大匹配
scheduler.SetEnableAllMatchings(true)

// 禁用枚举（默认模式）
scheduler.SetEnableAllMatchings(false)
```

### 2. 获取所有最大匹配结果

```go
// 执行调度
result := scheduler.Schedule()

// 获取所有找到的最大匹配方案
allMaxMatchings := scheduler.GetAllMaxMatchings()
```

### 3. 分析和比较方案

```go
// 计算每个方案的总距离和时间
for i, matching := range allMaxMatchings {
    distance, duration := scheduler.CalculateTotalDistanceAndDuration(matching)
    fmt.Printf("方案%d: 总距离=%dm, 总时间=%d分钟\n", i+1, distance, duration)
}
```

## 完整使用示例

```go
package main

import (
    "external-tool/internal/service/schedule"
    "fmt"
)

func demonstrateAllMaxMatchings() {
    // 创建调度器实例
    scheduler := schedule.NewMaxMatchingScheduler(mapModel, lawyers, courts, trials, routes).(*schedule.MaxMatchingScheduler)
    
    fmt.Println("=== 默认模式（单一最优匹配）===")
    
    // 1. 默认模式执行
    defaultResult := scheduler.Schedule()
    fmt.Printf("默认模式找到 %d 个匹配级别\n", len(defaultResult))
    
    fmt.Println("\n=== 枚举所有最大匹配模式 ===")
    
    // 2. 启用枚举所有最大匹配
    scheduler.SetEnableAllMatchings(true)
    
    // 3. 重新执行调度
    scheduler.Schedule()
    
    // 4. 获取所有最大匹配
    allMaxMatchings := scheduler.GetAllMaxMatchings()
    
    fmt.Printf("找到 %d 个不同的最大匹配方案\n", len(allMaxMatchings))
    
    if len(allMaxMatchings) > 0 {
        fmt.Printf("每个方案匹配数: %d\n", len(allMaxMatchings[0]))
        
        // 5. 分析每个方案
        bestDistanceIndex := 0
        bestDistance := int(^uint(0) >> 1)
        bestTimeIndex := 0
        bestTime := int(^uint(0) >> 1)
        
        for i, matching := range allMaxMatchings {
            distance, duration := scheduler.CalculateTotalDistanceAndDuration(matching)
            
            fmt.Printf("\n方案 %d:\n", i+1)
            fmt.Printf("  总距离: %dm\n", distance)
            fmt.Printf("  总时间: %d分钟\n", duration)
            
            // 记录最优方案
            if distance < bestDistance {
                bestDistance = distance
                bestDistanceIndex = i
            }
            
            if duration < bestTime {
                bestTime = duration
                bestTimeIndex = i
            }
            
            // 显示匹配详情
            fmt.Printf("  匹配详情:\n")
            for trialID, lawyerID := range matching {
                fmt.Printf("    案件%d -> 律师%d\n", trialID, lawyerID)
            }
        }
        
        // 6. 推荐最优方案
        fmt.Printf("\n=== 最优方案推荐 ===\n")
        fmt.Printf("最短距离方案: 方案%d (%dm)\n", bestDistanceIndex+1, bestDistance)
        fmt.Printf("最短时间方案: 方案%d (%d分钟)\n", bestTimeIndex+1, bestTime)
        
        if bestDistanceIndex == bestTimeIndex {
            fmt.Printf("推荐: 方案%d 同时是距离和时间最优！\n", bestDistanceIndex+1)
        } else {
            fmt.Printf("建议: 根据业务优先级选择距离优先或时间优先方案\n")
        }
    }
}
```

## 算法特点

### 1. 回溯枚举算法
- 使用深度优先搜索遍历所有可能的匹配组合
- 智能剪枝：如果当前匹配数 + 剩余案件数 < 最大匹配数，则跳过该分支
- 避免重复：检查并去除重复的匹配方案

### 2. 约束检查
- **地理约束**: 律师必须能到达指定法院
- **时间约束**: 考虑开庭时间、路程时间、缓冲时间
- **冲突检查**: 同一律师不能同时处理时间冲突的案件

### 3. 性能优化
- 限制结果数量：默认最多50个方案（可调整）
- 早期终止：找到足够数量的方案后停止搜索
- 内存管理：及时释放不需要的中间结果

## 应用场景

### 1. 决策支持
当存在多个同等优秀的调度方案时，提供全面的选择供决策者参考。

### 2. 风险评估
分析不同方案的稳定性，选择在律师临时unavailable时影响最小的方案。

### 3. 负载均衡
比较不同方案的律师工作负载分布，选择最平衡的分配。

### 4. 成本优化
- 选择总距离最短的方案（降低交通成本）
- 选择总时间最短的方案（提高效率）
- 选择律师利用率最优的方案

## 性能考虑

### 计算复杂度
- **时间复杂度**: O(k^n)，其中k是平均每个案件可用律师数，n是案件数
- **空间复杂度**: O(n * m)，其中m是找到的最大匹配方案数

### 适用规模
- **小规模** (≤10案件): 可以枚举所有方案
- **中等规模** (11-20案件): 建议设置结果数量限制
- **大规模** (>20案件): 建议使用默认单一最优匹配模式

### 性能调优
```go
// 调整最大结果数量限制
func (s *MaxMatchingScheduler) Schedule() map[uint][]map[uint]uint {
    // ...
    if s.enableAllMatchings {
        limit = 100  // 可以根据需要调整
    }
    s.limitResults(result, limit)
    // ...
}
```

## 测试建议

### 1. 功能测试
```go
func TestAllMaxMatchings(t *testing.T) {
    scheduler := createTestScheduler()
    scheduler.SetEnableAllMatchings(true)
    
    result := scheduler.Schedule()
    allMatchings := scheduler.GetAllMaxMatchings()
    
    // 验证所有方案的匹配数相等
    if len(allMatchings) > 1 {
        expectedSize := len(allMatchings[0])
        for i, matching := range allMatchings {
            assert.Equal(t, expectedSize, len(matching), 
                "方案%d的匹配数不等于其他方案", i)
        }
    }
    
    // 验证方案的唯一性
    for i := 0; i < len(allMatchings); i++ {
        for j := i + 1; j < len(allMatchings); j++ {
            assert.False(t, isScheduleEqual(allMatchings[i], allMatchings[j]),
                "发现重复方案: %d和%d", i, j)
        }
    }
}
```

### 2. 性能测试
```go
func BenchmarkAllMaxMatchings(b *testing.B) {
    scheduler := createTestScheduler()
    scheduler.SetEnableAllMatchings(true)
    
    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        scheduler.Schedule()
    }
}
```

## 总结

枚举所有最大匹配功能为律师调度系统提供了更全面的决策支持。通过对比多个最优方案，用户可以根据具体需求（成本、时间、风险等）选择最合适的调度方案，显著提升了系统的实用性和灵活性。

在使用时建议：
1. 对于小规模问题启用该功能
2. 大规模问题时谨慎使用，注意性能影响
3. 结合业务需求选择评价指标
4. 定期测试以确保算法正确性和性能 