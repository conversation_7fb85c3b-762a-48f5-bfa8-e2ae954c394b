-- 临时修复脚本：处理 price 字段的数据类型问题
-- 这个脚本用于修复当前数据库中 price 字段的数据格式问题

-- 1. 查看当前表结构
DESCRIBE routes;

-- 2. 查看当前数据样本
SELECT id, distance, duration, price FROM routes LIMIT 5;

-- 3. 如果 price 字段存在但类型不对，重新创建
-- 先备份数据到临时表
CREATE TEMPORARY TABLE routes_backup AS 
SELECT id, type, from_lat, from_lng, from_formatted_address, 
       to_lat, to_lng, to_formatted_address, distance, duration, 
       CASE 
           WHEN price IS NULL THEN -1
           WHEN CAST(price AS DECIMAL(10,2)) = 0 THEN -1
           ELSE ROUND(CAST(price AS DECIMAL(10,2)) * 100) 
       END as price_int,
       status, remark, detail, trial_id, created_at, updated_at
FROM routes;

-- 4. 删除原 price 字段
ALTER TABLE routes DROP COLUMN IF EXISTS price;

-- 5. 添加正确类型的 price 字段
ALTER TABLE routes ADD COLUMN price INT NOT NULL DEFAULT -1 COMMENT '票价单位: 分,-1表示无票价信息';

-- 6. 从备份表恢复数据
UPDATE routes r 
JOIN routes_backup b ON r.id = b.id 
SET r.price = b.price_int;

-- 7. 验证结果
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN price = -1 THEN 1 END) as no_price_records,
    COUNT(CASE WHEN price > 0 THEN 1 END) as has_price_records,
    MIN(price) as min_price,
    MAX(price) as max_price
FROM routes;

-- 8. 显示修复后的数据样本
SELECT id, distance, duration, price FROM routes LIMIT 10;
